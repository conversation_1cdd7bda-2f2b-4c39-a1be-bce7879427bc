"use strict";var a=Object.defineProperty;var p=(s,e,t)=>e in s?a(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var o=(s,e,t)=>p(s,typeof e!="symbol"?e+"":e,t);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("../helpers/dayjs-setup.cjs.js");const n=require("dayjs");class h{constructor(e){o(this,"parsedOpenTime");o(this,"parsedCloseTime");this.options=e,this.parsedOpenTime=this.parseTime(this.options.open),this.parsedCloseTime=this.parseTime(this.options.close)}toMarketDate(e){return n(e).tz(this.options.timeZone)}parseTime(e){const[t,i]=e.split(":").map(Number);return{hours:t,minutes:i}}getTimeBlocksForDay(){return[{start:"00:00",end:this.options.open,isOpen:!1},{start:this.options.open,end:this.options.close,isOpen:!0},{start:this.options.close,end:"23:59",isOpen:!1}]}isTimeInRange(e,t){return e>=t.start&&e<t.end}marketZoneNow(){return this.toMarketDate(n())}_isOpen(e){const t=e.format("HH:mm");return this.getTimeBlocksForDay().some(r=>this.isTimeInRange(t,r)&&r.isOpen)}isOpen(e){return e?this._isOpen(this.toMarketDate(e)):this._isOpen(this.marketZoneNow())}getOpen(e){return this.toMarketDate(e).hour(this.parsedOpenTime.hours).minute(this.parsedOpenTime.minutes).second(0)}getClose(e){return this.toMarketDate(e).hour(this.parsedCloseTime.hours).minute(this.parsedCloseTime.minutes).second(0)}getNextOpen(e){const t=this.getOpen(e);return this.toMarketDate(e).isAfter(t)?t.add(1,"day"):t}getNextClose(e){const t=this.getClose(e);return this.toMarketDate(e).isAfter(t)?t.add(1,"day"):t}getPrevOpen(e){const t=this.getOpen(e);return this.toMarketDate(e).isBefore(t)?t.subtract(1,"day"):t}getPrevClose(e){const t=this.getClose(e);return this.toMarketDate(e).isBefore(t)?t.subtract(1,"day"):t}}exports.Market=h;
//# sourceMappingURL=market.cjs.js.map
