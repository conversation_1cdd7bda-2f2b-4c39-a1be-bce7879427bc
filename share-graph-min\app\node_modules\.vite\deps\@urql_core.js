import "./chunk-DC5AMYBS.js";

// ../node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
var e = {
  NAME: "Name",
  DOCUMENT: "Document",
  OPERATION_DEFINITION: "OperationDefinition",
  VARIABLE_DEFINITION: "VariableDefinition",
  SELECTION_SET: "SelectionSet",
  FIELD: "Field",
  ARGUMENT: "Argument",
  FRAGMENT_SPREAD: "FragmentSpread",
  INLINE_FRAGMENT: "InlineFragment",
  FRAGMENT_DEFINITION: "FragmentDefinition",
  VARIABLE: "Variable",
  INT: "IntValue",
  FLOAT: "FloatValue",
  STRING: "StringValue",
  BOOLEAN: "BooleanValue",
  NULL: "NullValue",
  ENUM: "EnumValue",
  LIST: "ListValue",
  OBJECT: "ObjectValue",
  OBJECT_FIELD: "ObjectField",
  DIRECTIVE: "Directive",
  NAMED_TYPE: "NamedType",
  LIST_TYPE: "ListType",
  NON_NULL_TYPE: "NonNullType"
};
var GraphQLError = class extends Error {
  constructor(e3, r2, i2, n2, t2, a2, o2) {
    if (super(e3), this.name = "GraphQLError", this.message = e3, t2) {
      this.path = t2;
    }
    if (r2) {
      this.nodes = Array.isArray(r2) ? r2 : [r2];
    }
    if (i2) {
      this.source = i2;
    }
    if (n2) {
      this.positions = n2;
    }
    if (a2) {
      this.originalError = a2;
    }
    var l2 = o2;
    if (!l2 && a2) {
      var d3 = a2.extensions;
      if (d3 && "object" == typeof d3) {
        l2 = d3;
      }
    }
    this.extensions = l2 || {};
  }
  toJSON() {
    return {
      ...this,
      message: this.message
    };
  }
  toString() {
    return this.message;
  }
  get [Symbol.toStringTag]() {
    return "GraphQLError";
  }
};
var i;
var n;
function error(e3) {
  return new GraphQLError(`Syntax Error: Unexpected token at ${n} in ${e3}`);
}
function advance(e3) {
  if (e3.lastIndex = n, e3.test(i)) {
    return i.slice(n, n = e3.lastIndex);
  }
}
var t = / +(?=[^\s])/y;
function blockString(e3) {
  var r2 = e3.split("\n");
  var i2 = "";
  var n2 = 0;
  var a2 = 0;
  var o2 = r2.length - 1;
  for (var l2 = 0; l2 < r2.length; l2++) {
    if (t.lastIndex = 0, t.test(r2[l2])) {
      if (l2 && (!n2 || t.lastIndex < n2)) {
        n2 = t.lastIndex;
      }
      a2 = a2 || l2, o2 = l2;
    }
  }
  for (var d3 = a2; d3 <= o2; d3++) {
    if (d3 !== a2) {
      i2 += "\n";
    }
    i2 += r2[d3].slice(n2).replace(/\\"""/g, '"""');
  }
  return i2;
}
function ignored() {
  for (var e3 = 0 | i.charCodeAt(n++); 9 === e3 || 10 === e3 || 13 === e3 || 32 === e3 || 35 === e3 || 44 === e3 || 65279 === e3; e3 = 0 | i.charCodeAt(n++)) {
    if (35 === e3) {
      for (; 10 !== (e3 = i.charCodeAt(n++)) && 13 !== e3; ) {
      }
    }
  }
  n--;
}
function name() {
  var e3 = n;
  for (var r2 = 0 | i.charCodeAt(n++); r2 >= 48 && r2 <= 57 || r2 >= 65 && r2 <= 90 || 95 === r2 || r2 >= 97 && r2 <= 122; r2 = 0 | i.charCodeAt(n++)) {
  }
  if (e3 === n - 1) {
    throw error("Name");
  }
  var t2 = i.slice(e3, --n);
  return ignored(), t2;
}
function nameNode() {
  return {
    kind: "Name",
    value: name()
  };
}
var a = /(?:"""|(?:[\s\S]*?[^\\])""")/y;
var o = /(?:(?:\.\d+)?[eE][+-]?\d+|\.\d+)/y;
function value(e3) {
  var r2;
  switch (i.charCodeAt(n)) {
    case 91:
      n++, ignored();
      var t2 = [];
      for (; 93 !== i.charCodeAt(n); ) {
        t2.push(value(e3));
      }
      return n++, ignored(), {
        kind: "ListValue",
        values: t2
      };
    case 123:
      n++, ignored();
      var l2 = [];
      for (; 125 !== i.charCodeAt(n); ) {
        var d3 = nameNode();
        if (58 !== i.charCodeAt(n++)) {
          throw error("ObjectField");
        }
        ignored(), l2.push({
          kind: "ObjectField",
          name: d3,
          value: value(e3)
        });
      }
      return n++, ignored(), {
        kind: "ObjectValue",
        fields: l2
      };
    case 36:
      if (e3) {
        throw error("Variable");
      }
      return n++, {
        kind: "Variable",
        name: nameNode()
      };
    case 34:
      if (34 === i.charCodeAt(n + 1) && 34 === i.charCodeAt(n + 2)) {
        if (n += 3, null == (r2 = advance(a))) {
          throw error("StringValue");
        }
        return ignored(), {
          kind: "StringValue",
          value: blockString(r2.slice(0, -3)),
          block: true
        };
      } else {
        var u3 = n;
        var s2;
        n++;
        var c2 = false;
        for (s2 = 0 | i.charCodeAt(n++); 92 === s2 && (n++, c2 = true) || 10 !== s2 && 13 !== s2 && 34 !== s2 && s2; s2 = 0 | i.charCodeAt(n++)) {
        }
        if (34 !== s2) {
          throw error("StringValue");
        }
        return r2 = i.slice(u3, n), ignored(), {
          kind: "StringValue",
          value: c2 ? JSON.parse(r2) : r2.slice(1, -1),
          block: false
        };
      }
    case 45:
    case 48:
    case 49:
    case 50:
    case 51:
    case 52:
    case 53:
    case 54:
    case 55:
    case 56:
    case 57:
      var v2 = n++;
      var f2;
      for (; (f2 = 0 | i.charCodeAt(n++)) >= 48 && f2 <= 57; ) {
      }
      var m2 = i.slice(v2, --n);
      if (46 === (f2 = i.charCodeAt(n)) || 69 === f2 || 101 === f2) {
        if (null == (r2 = advance(o))) {
          throw error("FloatValue");
        }
        return ignored(), {
          kind: "FloatValue",
          value: m2 + r2
        };
      } else {
        return ignored(), {
          kind: "IntValue",
          value: m2
        };
      }
    case 110:
      if (117 === i.charCodeAt(n + 1) && 108 === i.charCodeAt(n + 2) && 108 === i.charCodeAt(n + 3)) {
        return n += 4, ignored(), {
          kind: "NullValue"
        };
      } else {
        break;
      }
    case 116:
      if (114 === i.charCodeAt(n + 1) && 117 === i.charCodeAt(n + 2) && 101 === i.charCodeAt(n + 3)) {
        return n += 4, ignored(), {
          kind: "BooleanValue",
          value: true
        };
      } else {
        break;
      }
    case 102:
      if (97 === i.charCodeAt(n + 1) && 108 === i.charCodeAt(n + 2) && 115 === i.charCodeAt(n + 3) && 101 === i.charCodeAt(n + 4)) {
        return n += 5, ignored(), {
          kind: "BooleanValue",
          value: false
        };
      } else {
        break;
      }
  }
  return {
    kind: "EnumValue",
    value: name()
  };
}
function arguments_(e3) {
  if (40 === i.charCodeAt(n)) {
    var r2 = [];
    n++, ignored();
    do {
      var t2 = nameNode();
      if (58 !== i.charCodeAt(n++)) {
        throw error("Argument");
      }
      ignored(), r2.push({
        kind: "Argument",
        name: t2,
        value: value(e3)
      });
    } while (41 !== i.charCodeAt(n));
    return n++, ignored(), r2;
  }
}
function directives(e3) {
  if (64 === i.charCodeAt(n)) {
    var r2 = [];
    do {
      n++, r2.push({
        kind: "Directive",
        name: nameNode(),
        arguments: arguments_(e3)
      });
    } while (64 === i.charCodeAt(n));
    return r2;
  }
}
function type() {
  var e3 = 0;
  for (; 91 === i.charCodeAt(n); ) {
    e3++, n++, ignored();
  }
  var r2 = {
    kind: "NamedType",
    name: nameNode()
  };
  do {
    if (33 === i.charCodeAt(n)) {
      n++, ignored(), r2 = {
        kind: "NonNullType",
        type: r2
      };
    }
    if (e3) {
      if (93 !== i.charCodeAt(n++)) {
        throw error("NamedType");
      }
      ignored(), r2 = {
        kind: "ListType",
        type: r2
      };
    }
  } while (e3--);
  return r2;
}
function selectionSetStart() {
  if (123 !== i.charCodeAt(n++)) {
    throw error("SelectionSet");
  }
  return ignored(), selectionSet();
}
function selectionSet() {
  var e3 = [];
  do {
    if (46 === i.charCodeAt(n)) {
      if (46 !== i.charCodeAt(++n) || 46 !== i.charCodeAt(++n)) {
        throw error("SelectionSet");
      }
      switch (n++, ignored(), i.charCodeAt(n)) {
        case 64:
          e3.push({
            kind: "InlineFragment",
            typeCondition: void 0,
            directives: directives(false),
            selectionSet: selectionSetStart()
          });
          break;
        case 111:
          if (110 === i.charCodeAt(n + 1)) {
            n += 2, ignored(), e3.push({
              kind: "InlineFragment",
              typeCondition: {
                kind: "NamedType",
                name: nameNode()
              },
              directives: directives(false),
              selectionSet: selectionSetStart()
            });
          } else {
            e3.push({
              kind: "FragmentSpread",
              name: nameNode(),
              directives: directives(false)
            });
          }
          break;
        case 123:
          n++, ignored(), e3.push({
            kind: "InlineFragment",
            typeCondition: void 0,
            directives: void 0,
            selectionSet: selectionSet()
          });
          break;
        default:
          e3.push({
            kind: "FragmentSpread",
            name: nameNode(),
            directives: directives(false)
          });
      }
    } else {
      var r2 = nameNode();
      var t2 = void 0;
      if (58 === i.charCodeAt(n)) {
        n++, ignored(), t2 = r2, r2 = nameNode();
      }
      var a2 = arguments_(false);
      var o2 = directives(false);
      var l2 = void 0;
      if (123 === i.charCodeAt(n)) {
        n++, ignored(), l2 = selectionSet();
      }
      e3.push({
        kind: "Field",
        alias: t2,
        name: r2,
        arguments: a2,
        directives: o2,
        selectionSet: l2
      });
    }
  } while (125 !== i.charCodeAt(n));
  return n++, ignored(), {
    kind: "SelectionSet",
    selections: e3
  };
}
function variableDefinitions() {
  if (ignored(), 40 === i.charCodeAt(n)) {
    var e3 = [];
    n++, ignored();
    do {
      if (36 !== i.charCodeAt(n++)) {
        throw error("Variable");
      }
      var r2 = nameNode();
      if (58 !== i.charCodeAt(n++)) {
        throw error("VariableDefinition");
      }
      ignored();
      var t2 = type();
      var a2 = void 0;
      if (61 === i.charCodeAt(n)) {
        n++, ignored(), a2 = value(true);
      }
      ignored(), e3.push({
        kind: "VariableDefinition",
        variable: {
          kind: "Variable",
          name: r2
        },
        type: t2,
        defaultValue: a2,
        directives: directives(true)
      });
    } while (41 !== i.charCodeAt(n));
    return n++, ignored(), e3;
  }
}
function fragmentDefinition() {
  var e3 = nameNode();
  if (111 !== i.charCodeAt(n++) || 110 !== i.charCodeAt(n++)) {
    throw error("FragmentDefinition");
  }
  return ignored(), {
    kind: "FragmentDefinition",
    name: e3,
    typeCondition: {
      kind: "NamedType",
      name: nameNode()
    },
    directives: directives(false),
    selectionSet: selectionSetStart()
  };
}
function definitions() {
  var e3 = [];
  do {
    if (123 === i.charCodeAt(n)) {
      n++, ignored(), e3.push({
        kind: "OperationDefinition",
        operation: "query",
        name: void 0,
        variableDefinitions: void 0,
        directives: void 0,
        selectionSet: selectionSet()
      });
    } else {
      var r2 = name();
      switch (r2) {
        case "fragment":
          e3.push(fragmentDefinition());
          break;
        case "query":
        case "mutation":
        case "subscription":
          var t2;
          var a2 = void 0;
          if (40 !== (t2 = i.charCodeAt(n)) && 64 !== t2 && 123 !== t2) {
            a2 = nameNode();
          }
          e3.push({
            kind: "OperationDefinition",
            operation: r2,
            name: a2,
            variableDefinitions: variableDefinitions(),
            directives: directives(false),
            selectionSet: selectionSetStart()
          });
          break;
        default:
          throw error("Document");
      }
    }
  } while (n < i.length);
  return e3;
}
function parse(e3, r2) {
  if (i = e3.body ? e3.body : e3, n = 0, ignored(), r2 && r2.noLocation) {
    return {
      kind: "Document",
      definitions: definitions()
    };
  } else {
    return {
      kind: "Document",
      definitions: definitions(),
      loc: {
        start: 0,
        end: i.length,
        startToken: void 0,
        endToken: void 0,
        source: {
          body: i,
          name: "graphql.web",
          locationOffset: {
            line: 1,
            column: 1
          }
        }
      }
    };
  }
}
function mapJoin(e3, r2, i2) {
  var n2 = "";
  for (var t2 = 0; t2 < e3.length; t2++) {
    if (t2) {
      n2 += r2;
    }
    n2 += i2(e3[t2]);
  }
  return n2;
}
function printString(e3) {
  return JSON.stringify(e3);
}
function printBlockString(e3) {
  return '"""\n' + e3.replace(/"""/g, '\\"""') + '\n"""';
}
var d = "\n";
var u = {
  OperationDefinition(e3) {
    var r2 = e3.operation;
    if (e3.name) {
      r2 += " " + e3.name.value;
    }
    if (e3.variableDefinitions && e3.variableDefinitions.length) {
      if (!e3.name) {
        r2 += " ";
      }
      r2 += "(" + mapJoin(e3.variableDefinitions, ", ", u.VariableDefinition) + ")";
    }
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", u.Directive);
    }
    return "query" !== r2 ? r2 + " " + u.SelectionSet(e3.selectionSet) : u.SelectionSet(e3.selectionSet);
  },
  VariableDefinition(e3) {
    var r2 = u.Variable(e3.variable) + ": " + _print(e3.type);
    if (e3.defaultValue) {
      r2 += " = " + _print(e3.defaultValue);
    }
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", u.Directive);
    }
    return r2;
  },
  Field(e3) {
    var r2 = e3.alias ? e3.alias.value + ": " + e3.name.value : e3.name.value;
    if (e3.arguments && e3.arguments.length) {
      var i2 = mapJoin(e3.arguments, ", ", u.Argument);
      if (r2.length + i2.length + 2 > 80) {
        r2 += "(" + (d += "  ") + mapJoin(e3.arguments, d, u.Argument) + (d = d.slice(0, -2)) + ")";
      } else {
        r2 += "(" + i2 + ")";
      }
    }
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", u.Directive);
    }
    if (e3.selectionSet && e3.selectionSet.selections.length) {
      r2 += " " + u.SelectionSet(e3.selectionSet);
    }
    return r2;
  },
  StringValue(e3) {
    if (e3.block) {
      return printBlockString(e3.value).replace(/\n/g, d);
    } else {
      return printString(e3.value);
    }
  },
  BooleanValue: (e3) => "" + e3.value,
  NullValue: (e3) => "null",
  IntValue: (e3) => e3.value,
  FloatValue: (e3) => e3.value,
  EnumValue: (e3) => e3.value,
  Name: (e3) => e3.value,
  Variable: (e3) => "$" + e3.name.value,
  ListValue: (e3) => "[" + mapJoin(e3.values, ", ", _print) + "]",
  ObjectValue: (e3) => "{" + mapJoin(e3.fields, ", ", u.ObjectField) + "}",
  ObjectField: (e3) => e3.name.value + ": " + _print(e3.value),
  Document(e3) {
    if (!e3.definitions || !e3.definitions.length) {
      return "";
    } else {
      return mapJoin(e3.definitions, "\n\n", _print);
    }
  },
  SelectionSet: (e3) => "{" + (d += "  ") + mapJoin(e3.selections, d, _print) + (d = d.slice(0, -2)) + "}",
  Argument: (e3) => e3.name.value + ": " + _print(e3.value),
  FragmentSpread(e3) {
    var r2 = "..." + e3.name.value;
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", u.Directive);
    }
    return r2;
  },
  InlineFragment(e3) {
    var r2 = "...";
    if (e3.typeCondition) {
      r2 += " on " + e3.typeCondition.name.value;
    }
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", u.Directive);
    }
    return r2 += " " + u.SelectionSet(e3.selectionSet);
  },
  FragmentDefinition(e3) {
    var r2 = "fragment " + e3.name.value;
    if (r2 += " on " + e3.typeCondition.name.value, e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", u.Directive);
    }
    return r2 + " " + u.SelectionSet(e3.selectionSet);
  },
  Directive(e3) {
    var r2 = "@" + e3.name.value;
    if (e3.arguments && e3.arguments.length) {
      r2 += "(" + mapJoin(e3.arguments, ", ", u.Argument) + ")";
    }
    return r2;
  },
  NamedType: (e3) => e3.name.value,
  ListType: (e3) => "[" + _print(e3.type) + "]",
  NonNullType: (e3) => _print(e3.type) + "!"
};
var _print = (e3) => u[e3.kind](e3);
function print(e3) {
  return d = "\n", u[e3.kind] ? u[e3.kind](e3) : "";
}

// ../node_modules/wonka/dist/wonka.mjs
var teardownPlaceholder = () => {
};
var e2 = teardownPlaceholder;
function start(e3) {
  return {
    tag: 0,
    0: e3
  };
}
function push(e3) {
  return {
    tag: 1,
    0: e3
  };
}
var asyncIteratorSymbol = () => "function" == typeof Symbol && Symbol.asyncIterator || "@@asyncIterator";
var identity = (e3) => e3;
function filter(r2) {
  return (t2) => (i2) => {
    var a2 = e2;
    t2((e3) => {
      if (0 === e3) {
        i2(0);
      } else if (0 === e3.tag) {
        a2 = e3[0];
        i2(e3);
      } else if (!r2(e3[0])) {
        a2(0);
      } else {
        i2(e3);
      }
    });
  };
}
function map(e3) {
  return (r2) => (t2) => r2((r3) => {
    if (0 === r3 || 0 === r3.tag) {
      t2(r3);
    } else {
      t2(push(e3(r3[0])));
    }
  });
}
function mergeMap(r2) {
  return (t2) => (i2) => {
    var a2 = [];
    var f2 = e2;
    var n2 = false;
    var s2 = false;
    t2((t3) => {
      if (s2) {
      } else if (0 === t3) {
        s2 = true;
        if (!a2.length) {
          i2(0);
        }
      } else if (0 === t3.tag) {
        f2 = t3[0];
      } else {
        n2 = false;
        !function applyInnerSource(r3) {
          var t4 = e2;
          r3((e3) => {
            if (0 === e3) {
              if (a2.length) {
                var r4 = a2.indexOf(t4);
                if (r4 > -1) {
                  (a2 = a2.slice()).splice(r4, 1);
                }
                if (!a2.length) {
                  if (s2) {
                    i2(0);
                  } else if (!n2) {
                    n2 = true;
                    f2(0);
                  }
                }
              }
            } else if (0 === e3.tag) {
              a2.push(t4 = e3[0]);
              t4(0);
            } else if (a2.length) {
              i2(e3);
              t4(0);
            }
          });
        }(r2(t3[0]));
        if (!n2) {
          n2 = true;
          f2(0);
        }
      }
    });
    i2(start((e3) => {
      if (1 === e3) {
        if (!s2) {
          s2 = true;
          f2(1);
        }
        for (var r3 = 0, t3 = a2, i3 = a2.length; r3 < i3; r3++) {
          t3[r3](1);
        }
        a2.length = 0;
      } else {
        if (!s2 && !n2) {
          n2 = true;
          f2(0);
        } else {
          n2 = false;
        }
        for (var l2 = 0, u3 = a2, o2 = a2.length; l2 < o2; l2++) {
          u3[l2](0);
        }
      }
    }));
  };
}
function mergeAll(e3) {
  return mergeMap(identity)(e3);
}
function merge(e3) {
  return mergeAll(r(e3));
}
function onEnd(e3) {
  return (r2) => (t2) => {
    var i2 = false;
    r2((r3) => {
      if (i2) {
      } else if (0 === r3) {
        i2 = true;
        t2(0);
        e3();
      } else if (0 === r3.tag) {
        var a2 = r3[0];
        t2(start((r4) => {
          if (1 === r4) {
            i2 = true;
            a2(1);
            e3();
          } else {
            a2(r4);
          }
        }));
      } else {
        t2(r3);
      }
    });
  };
}
function onPush(e3) {
  return (r2) => (t2) => {
    var i2 = false;
    r2((r3) => {
      if (i2) {
      } else if (0 === r3) {
        i2 = true;
        t2(0);
      } else if (0 === r3.tag) {
        var a2 = r3[0];
        t2(start((e4) => {
          if (1 === e4) {
            i2 = true;
          }
          a2(e4);
        }));
      } else {
        e3(r3[0]);
        t2(r3);
      }
    });
  };
}
function onStart(e3) {
  return (r2) => (t2) => r2((r3) => {
    if (0 === r3) {
      t2(0);
    } else if (0 === r3.tag) {
      t2(r3);
      e3();
    } else {
      t2(r3);
    }
  });
}
function share(r2) {
  var t2 = [];
  var i2 = e2;
  var a2 = false;
  return (e3) => {
    t2.push(e3);
    if (1 === t2.length) {
      r2((e4) => {
        if (0 === e4) {
          for (var r3 = 0, f2 = t2, n2 = t2.length; r3 < n2; r3++) {
            f2[r3](0);
          }
          t2.length = 0;
        } else if (0 === e4.tag) {
          i2 = e4[0];
        } else {
          a2 = false;
          for (var s2 = 0, l2 = t2, u3 = t2.length; s2 < u3; s2++) {
            l2[s2](e4);
          }
        }
      });
    }
    e3(start((r3) => {
      if (1 === r3) {
        var f2 = t2.indexOf(e3);
        if (f2 > -1) {
          (t2 = t2.slice()).splice(f2, 1);
        }
        if (!t2.length) {
          i2(1);
        }
      } else if (!a2) {
        a2 = true;
        i2(0);
      }
    }));
  };
}
function switchMap(r2) {
  return (t2) => (i2) => {
    var a2 = e2;
    var f2 = e2;
    var n2 = false;
    var s2 = false;
    var l2 = false;
    var u3 = false;
    t2((t3) => {
      if (u3) {
      } else if (0 === t3) {
        u3 = true;
        if (!l2) {
          i2(0);
        }
      } else if (0 === t3.tag) {
        a2 = t3[0];
      } else {
        if (l2) {
          f2(1);
          f2 = e2;
        }
        if (!n2) {
          n2 = true;
          a2(0);
        } else {
          n2 = false;
        }
        !function applyInnerSource(e3) {
          l2 = true;
          e3((e4) => {
            if (!l2) {
            } else if (0 === e4) {
              l2 = false;
              if (u3) {
                i2(0);
              } else if (!n2) {
                n2 = true;
                a2(0);
              }
            } else if (0 === e4.tag) {
              s2 = false;
              (f2 = e4[0])(0);
            } else {
              i2(e4);
              if (!s2) {
                f2(0);
              } else {
                s2 = false;
              }
            }
          });
        }(r2(t3[0]));
      }
    });
    i2(start((e3) => {
      if (1 === e3) {
        if (!u3) {
          u3 = true;
          a2(1);
        }
        if (l2) {
          l2 = false;
          f2(1);
        }
      } else {
        if (!u3 && !n2) {
          n2 = true;
          a2(0);
        }
        if (l2 && !s2) {
          s2 = true;
          f2(0);
        }
      }
    }));
  };
}
function take(r2) {
  return (t2) => (i2) => {
    var a2 = e2;
    var f2 = false;
    var n2 = 0;
    t2((e3) => {
      if (f2) {
      } else if (0 === e3) {
        f2 = true;
        i2(0);
      } else if (0 === e3.tag) {
        if (r2 <= 0) {
          f2 = true;
          i2(0);
          e3[0](1);
        } else {
          a2 = e3[0];
        }
      } else if (n2++ < r2) {
        i2(e3);
        if (!f2 && n2 >= r2) {
          f2 = true;
          i2(0);
          a2(1);
        }
      } else {
        i2(e3);
      }
    });
    i2(start((e3) => {
      if (1 === e3 && !f2) {
        f2 = true;
        a2(1);
      } else if (0 === e3 && !f2 && n2 < r2) {
        a2(0);
      }
    }));
  };
}
function takeUntil(r2) {
  return (t2) => (i2) => {
    var a2 = e2;
    var f2 = e2;
    var n2 = false;
    t2((e3) => {
      if (n2) {
      } else if (0 === e3) {
        n2 = true;
        f2(1);
        i2(0);
      } else if (0 === e3.tag) {
        a2 = e3[0];
        r2((e4) => {
          if (0 === e4) {
          } else if (0 === e4.tag) {
            (f2 = e4[0])(0);
          } else {
            n2 = true;
            f2(1);
            a2(1);
            i2(0);
          }
        });
      } else {
        i2(e3);
      }
    });
    i2(start((e3) => {
      if (1 === e3 && !n2) {
        n2 = true;
        a2(1);
        f2(1);
      } else if (!n2) {
        a2(0);
      }
    }));
  };
}
function takeWhile(r2, t2) {
  return (i2) => (a2) => {
    var f2 = e2;
    var n2 = false;
    i2((e3) => {
      if (n2) {
      } else if (0 === e3) {
        n2 = true;
        a2(0);
      } else if (0 === e3.tag) {
        f2 = e3[0];
        a2(e3);
      } else if (!r2(e3[0])) {
        n2 = true;
        if (t2) {
          a2(e3);
        }
        a2(0);
        f2(1);
      } else {
        a2(e3);
      }
    });
  };
}
function lazy(e3) {
  return (r2) => e3()(r2);
}
function fromAsyncIterable(e3) {
  return (r2) => {
    var t2 = e3[asyncIteratorSymbol()] && e3[asyncIteratorSymbol()]() || e3;
    var i2 = false;
    var a2 = false;
    var f2 = false;
    var n2;
    r2(start(async (e4) => {
      if (1 === e4) {
        i2 = true;
        if (t2.return) {
          t2.return();
        }
      } else if (a2) {
        f2 = true;
      } else {
        for (f2 = a2 = true; f2 && !i2; ) {
          if ((n2 = await t2.next()).done) {
            i2 = true;
            if (t2.return) {
              await t2.return();
            }
            r2(0);
          } else {
            try {
              f2 = false;
              r2(push(n2.value));
            } catch (e5) {
              if (t2.throw) {
                if (i2 = !!(await t2.throw(e5)).done) {
                  r2(0);
                }
              } else {
                throw e5;
              }
            }
          }
        }
        a2 = false;
      }
    }));
  };
}
function fromIterable(e3) {
  if (e3[Symbol.asyncIterator]) {
    return fromAsyncIterable(e3);
  }
  return (r2) => {
    var t2 = e3[Symbol.iterator]();
    var i2 = false;
    var a2 = false;
    var f2 = false;
    var n2;
    r2(start((e4) => {
      if (1 === e4) {
        i2 = true;
        if (t2.return) {
          t2.return();
        }
      } else if (a2) {
        f2 = true;
      } else {
        for (f2 = a2 = true; f2 && !i2; ) {
          if ((n2 = t2.next()).done) {
            i2 = true;
            if (t2.return) {
              t2.return();
            }
            r2(0);
          } else {
            try {
              f2 = false;
              r2(push(n2.value));
            } catch (e5) {
              if (t2.throw) {
                if (i2 = !!t2.throw(e5).done) {
                  r2(0);
                }
              } else {
                throw e5;
              }
            }
          }
        }
        a2 = false;
      }
    }));
  };
}
var r = fromIterable;
function fromValue(e3) {
  return (r2) => {
    var t2 = false;
    r2(start((i2) => {
      if (1 === i2) {
        t2 = true;
      } else if (!t2) {
        t2 = true;
        r2(push(e3));
        r2(0);
      }
    }));
  };
}
function make(e3) {
  return (r2) => {
    var t2 = false;
    var i2 = e3({
      next(e4) {
        if (!t2) {
          r2(push(e4));
        }
      },
      complete() {
        if (!t2) {
          t2 = true;
          r2(0);
        }
      }
    });
    r2(start((e4) => {
      if (1 === e4 && !t2) {
        t2 = true;
        i2();
      }
    }));
  };
}
function makeSubject() {
  var e3;
  var r2;
  return {
    source: share(make((t2) => {
      e3 = t2.next;
      r2 = t2.complete;
      return teardownPlaceholder;
    })),
    next(r3) {
      if (e3) {
        e3(r3);
      }
    },
    complete() {
      if (r2) {
        r2();
      }
    }
  };
}
function fromPromise(e3) {
  return make((r2) => {
    e3.then((e4) => {
      Promise.resolve(e4).then(() => {
        r2.next(e4);
        r2.complete();
      });
    });
    return teardownPlaceholder;
  });
}
function subscribe(r2) {
  return (t2) => {
    var i2 = e2;
    var a2 = false;
    t2((e3) => {
      if (0 === e3) {
        a2 = true;
      } else if (0 === e3.tag) {
        (i2 = e3[0])(0);
      } else if (!a2) {
        r2(e3[0]);
        i2(0);
      }
    });
    return {
      unsubscribe() {
        if (!a2) {
          a2 = true;
          i2(1);
        }
      }
    };
  };
}
function publish(e3) {
  subscribe((e4) => {
  })(e3);
}
function toPromise(r2) {
  return new Promise((t2) => {
    var i2 = e2;
    var a2;
    r2((e3) => {
      if (0 === e3) {
        Promise.resolve(a2).then(t2);
      } else if (0 === e3.tag) {
        (i2 = e3[0])(0);
      } else {
        a2 = e3[0];
        i2(0);
      }
    });
  });
}

// ../node_modules/@urql/core/dist/urql-core-chunk.mjs
var rehydrateGraphQlError = (r2) => {
  if (r2 && "string" == typeof r2.message && (r2.extensions || "GraphQLError" === r2.name)) {
    return r2;
  } else if ("object" == typeof r2 && "string" == typeof r2.message) {
    return new GraphQLError(r2.message, r2.nodes, r2.source, r2.positions, r2.path, r2, r2.extensions || {});
  } else {
    return new GraphQLError(r2);
  }
};
var CombinedError = class extends Error {
  constructor(e3) {
    var r2 = (e3.graphQLErrors || []).map(rehydrateGraphQlError);
    var t2 = ((e4, r3) => {
      var t3 = "";
      if (e4) {
        return `[Network] ${e4.message}`;
      }
      if (r3) {
        for (var a2 = 0, n2 = r3.length; a2 < n2; a2++) {
          if (t3) {
            t3 += "\n";
          }
          t3 += `[GraphQL] ${r3[a2].message}`;
        }
      }
      return t3;
    })(e3.networkError, r2);
    super(t2);
    this.name = "CombinedError";
    this.message = t2;
    this.graphQLErrors = r2;
    this.networkError = e3.networkError;
    this.response = e3.response;
  }
  toString() {
    return this.message;
  }
};
var phash = (e3, r2) => {
  var t2 = 0 | (r2 || 5381);
  for (var a2 = 0, n2 = 0 | e3.length; a2 < n2; a2++) {
    t2 = (t2 << 5) + t2 + e3.charCodeAt(a2);
  }
  return t2;
};
var s = /* @__PURE__ */ new Set();
var f = /* @__PURE__ */ new WeakMap();
var stringify = (e3, r2) => {
  if (null === e3 || s.has(e3)) {
    return "null";
  } else if ("object" != typeof e3) {
    return JSON.stringify(e3) || "";
  } else if (e3.toJSON) {
    return stringify(e3.toJSON(), r2);
  } else if (Array.isArray(e3)) {
    var t2 = "[";
    for (var a2 = 0, n2 = e3.length; a2 < n2; a2++) {
      if (t2.length > 1) {
        t2 += ",";
      }
      t2 += stringify(e3[a2], r2) || "null";
    }
    return t2 += "]";
  } else if (!r2 && (l !== NoopConstructor && e3 instanceof l || d2 !== NoopConstructor && e3 instanceof d2)) {
    return "null";
  }
  var o2 = Object.keys(e3).sort();
  if (!o2.length && e3.constructor && Object.getPrototypeOf(e3).constructor !== Object.prototype.constructor) {
    var i2 = f.get(e3) || Math.random().toString(36).slice(2);
    f.set(e3, i2);
    return stringify({
      __key: i2
    }, r2);
  }
  s.add(e3);
  var c2 = "{";
  for (var v2 = 0, p2 = o2.length; v2 < p2; v2++) {
    var u3 = stringify(e3[o2[v2]], r2);
    if (u3) {
      if (c2.length > 1) {
        c2 += ",";
      }
      c2 += stringify(o2[v2], r2) + ":" + u3;
    }
  }
  s.delete(e3);
  return c2 += "}";
};
var extract = (e3, r2, t2) => {
  if (null == t2 || "object" != typeof t2 || t2.toJSON || s.has(t2)) {
  } else if (Array.isArray(t2)) {
    for (var a2 = 0, n2 = t2.length; a2 < n2; a2++) {
      extract(e3, `${r2}.${a2}`, t2[a2]);
    }
  } else if (t2 instanceof l || t2 instanceof d2) {
    e3.set(r2, t2);
  } else {
    s.add(t2);
    for (var o2 in t2) {
      extract(e3, `${r2}.${o2}`, t2[o2]);
    }
  }
};
var stringifyVariables = (e3, r2) => {
  s.clear();
  return stringify(e3, r2 || false);
};
var NoopConstructor = class {
};
var l = "undefined" != typeof File ? File : NoopConstructor;
var d2 = "undefined" != typeof Blob ? Blob : NoopConstructor;
var c = /("{3}[\s\S]*"{3}|"(?:\\.|[^"])*")/g;
var v = /(?:#[^\n\r]+)?(?:[\r\n]+|$)/g;
var replaceOutsideStrings = (e3, r2) => r2 % 2 == 0 ? e3.replace(v, "\n") : e3;
var sanitizeDocument = (e3) => e3.split(c).map(replaceOutsideStrings).join("").trim();
var p = /* @__PURE__ */ new Map();
var u2 = /* @__PURE__ */ new Map();
var stringifyDocument = (e3) => {
  var t2;
  if ("string" == typeof e3) {
    t2 = sanitizeDocument(e3);
  } else if (e3.loc && u2.get(e3.__key) === e3) {
    t2 = e3.loc.source.body;
  } else {
    t2 = p.get(e3) || sanitizeDocument(print(e3));
    p.set(e3, t2);
  }
  if ("string" != typeof e3 && !e3.loc) {
    e3.loc = {
      start: 0,
      end: t2.length,
      source: {
        body: t2,
        name: "gql",
        locationOffset: {
          line: 1,
          column: 1
        }
      }
    };
  }
  return t2;
};
var hashDocument = (e3) => {
  var r2;
  if (e3.documentId) {
    r2 = phash(e3.documentId);
  } else {
    r2 = phash(stringifyDocument(e3));
    if (e3.definitions) {
      var t2 = getOperationName(e3);
      if (t2) {
        r2 = phash(`
# ${t2}`, r2);
      }
    }
  }
  return r2;
};
var keyDocument = (e3) => {
  var r2;
  var a2;
  if ("string" == typeof e3) {
    r2 = hashDocument(e3);
    a2 = u2.get(r2) || parse(e3, {
      noLocation: true
    });
  } else {
    r2 = e3.__key || hashDocument(e3);
    a2 = u2.get(r2) || e3;
  }
  if (!a2.loc) {
    stringifyDocument(a2);
  }
  a2.__key = r2;
  u2.set(r2, a2);
  return a2;
};
var createRequest = (e3, r2, t2) => {
  var a2 = r2 || {};
  var n2 = keyDocument(e3);
  var o2 = stringifyVariables(a2, true);
  var i2 = n2.__key;
  if ("{}" !== o2) {
    i2 = phash(o2, i2);
  }
  return {
    key: i2,
    query: n2,
    variables: a2,
    extensions: t2
  };
};
var getOperationName = (e3) => {
  for (var r2 = 0, t2 = e3.definitions.length; r2 < t2; r2++) {
    var n2 = e3.definitions[r2];
    if (n2.kind === e.OPERATION_DEFINITION) {
      return n2.name ? n2.name.value : void 0;
    }
  }
};
var getOperationType = (e3) => {
  for (var r2 = 0, t2 = e3.definitions.length; r2 < t2; r2++) {
    var n2 = e3.definitions[r2];
    if (n2.kind === e.OPERATION_DEFINITION) {
      return n2.operation;
    }
  }
};
var makeResult = (e3, r2, t2) => {
  if (!("data" in r2 || "errors" in r2 && Array.isArray(r2.errors))) {
    throw new Error("No Content");
  }
  var a2 = "subscription" === e3.kind;
  return {
    operation: e3,
    data: r2.data,
    error: Array.isArray(r2.errors) ? new CombinedError({
      graphQLErrors: r2.errors,
      response: t2
    }) : void 0,
    extensions: r2.extensions ? {
      ...r2.extensions
    } : void 0,
    hasNext: null == r2.hasNext ? a2 : r2.hasNext,
    stale: false
  };
};
var deepMerge = (e3, r2) => {
  if ("object" == typeof e3 && null != e3) {
    if (Array.isArray(e3)) {
      e3 = [...e3];
      for (var t2 = 0, a2 = r2.length; t2 < a2; t2++) {
        e3[t2] = deepMerge(e3[t2], r2[t2]);
      }
      return e3;
    }
    if (!e3.constructor || e3.constructor === Object) {
      e3 = {
        ...e3
      };
      for (var n2 in r2) {
        e3[n2] = deepMerge(e3[n2], r2[n2]);
      }
      return e3;
    }
  }
  return r2;
};
var mergeResultPatch = (e3, r2, t2, a2) => {
  var n2 = e3.error ? e3.error.graphQLErrors : [];
  var o2 = !!e3.extensions || !!(r2.payload || r2).extensions;
  var i2 = {
    ...e3.extensions,
    ...(r2.payload || r2).extensions
  };
  var s2 = r2.incremental;
  if ("path" in r2) {
    s2 = [r2];
  }
  var f2 = {
    data: e3.data
  };
  if (s2) {
    var _loop = function() {
      var e4 = s2[l2];
      if (Array.isArray(e4.errors)) {
        n2.push(...e4.errors);
      }
      if (e4.extensions) {
        Object.assign(i2, e4.extensions);
        o2 = true;
      }
      var r3 = "data";
      var t3 = f2;
      var d4 = [];
      if (e4.path) {
        d4 = e4.path;
      } else if (a2) {
        var c2 = a2.find((r4) => r4.id === e4.id);
        if (e4.subPath) {
          d4 = [...c2.path, ...e4.subPath];
        } else {
          d4 = c2.path;
        }
      }
      for (var v2 = 0, p2 = d4.length; v2 < p2; r3 = d4[v2++]) {
        t3 = t3[r3] = Array.isArray(t3[r3]) ? [...t3[r3]] : {
          ...t3[r3]
        };
      }
      if (e4.items) {
        var u3 = +r3 >= 0 ? r3 : 0;
        for (var y2 = 0, h2 = e4.items.length; y2 < h2; y2++) {
          t3[u3 + y2] = deepMerge(t3[u3 + y2], e4.items[y2]);
        }
      } else if (void 0 !== e4.data) {
        t3[r3] = deepMerge(t3[r3], e4.data);
      }
    };
    for (var l2 = 0, d3 = s2.length; l2 < d3; l2++) {
      _loop();
    }
  } else {
    f2.data = (r2.payload || r2).data || e3.data;
    n2 = r2.errors || r2.payload && r2.payload.errors || n2;
  }
  return {
    operation: e3.operation,
    data: f2.data,
    error: n2.length ? new CombinedError({
      graphQLErrors: n2,
      response: t2
    }) : void 0,
    extensions: o2 ? i2 : void 0,
    hasNext: null != r2.hasNext ? r2.hasNext : e3.hasNext,
    stale: false
  };
};
var makeErrorResult = (e3, r2, t2) => ({
  operation: e3,
  data: void 0,
  error: new CombinedError({
    networkError: r2,
    response: t2
  }),
  extensions: void 0,
  hasNext: false,
  stale: false
});
function makeFetchBody(e3) {
  var r2 = {
    query: void 0,
    documentId: void 0,
    operationName: getOperationName(e3.query),
    variables: e3.variables || void 0,
    extensions: e3.extensions
  };
  if ("documentId" in e3.query && e3.query.documentId && (!e3.query.definitions || !e3.query.definitions.length)) {
    r2.documentId = e3.query.documentId;
  } else if (!e3.extensions || !e3.extensions.persistedQuery || e3.extensions.persistedQuery.miss) {
    r2.query = stringifyDocument(e3.query);
  }
  return r2;
}
var makeFetchURL = (e3, r2) => {
  var t2 = "query" === e3.kind && e3.context.preferGetMethod;
  if (!t2 || !r2) {
    return e3.context.url;
  }
  var a2 = splitOutSearchParams(e3.context.url);
  for (var n2 in r2) {
    var o2 = r2[n2];
    if (o2) {
      a2[1].set(n2, "object" == typeof o2 ? stringifyVariables(o2) : o2);
    }
  }
  var i2 = a2.join("?");
  if (i2.length > 2047 && "force" !== t2) {
    e3.context.preferGetMethod = false;
    return e3.context.url;
  }
  return i2;
};
var splitOutSearchParams = (e3) => {
  var r2 = e3.indexOf("?");
  return r2 > -1 ? [e3.slice(0, r2), new URLSearchParams(e3.slice(r2 + 1))] : [e3, new URLSearchParams()];
};
var serializeBody = (e3, r2) => {
  if (r2 && !("query" === e3.kind && !!e3.context.preferGetMethod)) {
    var t2 = stringifyVariables(r2);
    var a2 = ((e4) => {
      var r3 = /* @__PURE__ */ new Map();
      if (l !== NoopConstructor || d2 !== NoopConstructor) {
        s.clear();
        extract(r3, "variables", e4);
      }
      return r3;
    })(r2.variables);
    if (a2.size) {
      var n2 = new FormData();
      n2.append("operations", t2);
      n2.append("map", stringifyVariables({
        ...[...a2.keys()].map((e4) => [e4])
      }));
      var o2 = 0;
      for (var i2 of a2.values()) {
        n2.append("" + o2++, i2);
      }
      return n2;
    }
    return t2;
  }
};
var makeFetchOptions = (e3, r2) => {
  var t2 = {
    accept: "subscription" === e3.kind ? "text/event-stream, multipart/mixed" : "application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed"
  };
  var a2 = ("function" == typeof e3.context.fetchOptions ? e3.context.fetchOptions() : e3.context.fetchOptions) || {};
  if (a2.headers) {
    if (((e4) => "has" in e4 && !Object.keys(e4).length)(a2.headers)) {
      a2.headers.forEach((e4, r3) => {
        t2[r3] = e4;
      });
    } else if (Array.isArray(a2.headers)) {
      a2.headers.forEach((e4, r3) => {
        if (Array.isArray(e4)) {
          if (t2[e4[0]]) {
            t2[e4[0]] = `${t2[e4[0]]},${e4[1]}`;
          } else {
            t2[e4[0]] = e4[1];
          }
        } else {
          t2[r3] = e4;
        }
      });
    } else {
      for (var n2 in a2.headers) {
        t2[n2.toLowerCase()] = a2.headers[n2];
      }
    }
  }
  var o2 = serializeBody(e3, r2);
  if ("string" == typeof o2 && !t2["content-type"]) {
    t2["content-type"] = "application/json";
  }
  return {
    ...a2,
    method: o2 ? "POST" : "GET",
    body: o2,
    headers: t2
  };
};
var y = "undefined" != typeof TextDecoder ? new TextDecoder() : null;
var h = /boundary="?([^=";]+)"?/i;
var m = /data: ?([^\n]+)/;
var toString = (e3) => "Buffer" === e3.constructor.name ? e3.toString() : y.decode(e3);
async function* streamBody(e3) {
  if (e3.body[Symbol.asyncIterator]) {
    for await (var r2 of e3.body) {
      yield toString(r2);
    }
  } else {
    var t2 = e3.body.getReader();
    var a2;
    try {
      while (!(a2 = await t2.read()).done) {
        yield toString(a2.value);
      }
    } finally {
      t2.cancel();
    }
  }
}
async function* split(e3, r2) {
  var t2 = "";
  var a2;
  for await (var n2 of e3) {
    t2 += n2;
    while ((a2 = t2.indexOf(r2)) > -1) {
      yield t2.slice(0, a2);
      t2 = t2.slice(a2 + r2.length);
    }
  }
}
async function* fetchOperation(e3, r2, t2) {
  var a2 = true;
  var n2 = null;
  var o2;
  try {
    yield await Promise.resolve();
    var i2 = (o2 = await (e3.context.fetch || fetch)(r2, t2)).headers.get("Content-Type") || "";
    var s2;
    if (/multipart\/mixed/i.test(i2)) {
      s2 = async function* parseMultipartMixed(e4, r3) {
        var t3 = e4.match(h);
        var a3 = "--" + (t3 ? t3[1] : "-");
        var n3 = true;
        var o3;
        for await (var i3 of split(streamBody(r3), "\r\n" + a3)) {
          if (n3) {
            n3 = false;
            var s3 = i3.indexOf(a3);
            if (s3 > -1) {
              i3 = i3.slice(s3 + a3.length);
            } else {
              continue;
            }
          }
          try {
            yield o3 = JSON.parse(i3.slice(i3.indexOf("\r\n\r\n") + 4));
          } catch (e5) {
            if (!o3) {
              throw e5;
            }
          }
          if (o3 && false === o3.hasNext) {
            break;
          }
        }
        if (o3 && false !== o3.hasNext) {
          yield {
            hasNext: false
          };
        }
      }(i2, o2);
    } else if (/text\/event-stream/i.test(i2)) {
      s2 = async function* parseEventStream(e4) {
        var r3;
        for await (var t3 of split(streamBody(e4), "\n\n")) {
          var a3 = t3.match(m);
          if (a3) {
            var n3 = a3[1];
            try {
              yield r3 = JSON.parse(n3);
            } catch (e5) {
              if (!r3) {
                throw e5;
              }
            }
            if (r3 && false === r3.hasNext) {
              break;
            }
          }
        }
        if (r3 && false !== r3.hasNext) {
          yield {
            hasNext: false
          };
        }
      }(o2);
    } else if (!/text\//i.test(i2)) {
      s2 = async function* parseJSON(e4) {
        yield JSON.parse(await e4.text());
      }(o2);
    } else {
      s2 = async function* parseMaybeJSON(e4) {
        var r3 = await e4.text();
        try {
          var t3 = JSON.parse(r3);
          if (true) {
            console.warn('Found response with content-type "text/plain" but it had a valid "application/json" response.');
          }
          yield t3;
        } catch (e5) {
          throw new Error(r3);
        }
      }(o2);
    }
    var f2;
    for await (var l2 of s2) {
      if (l2.pending && !n2) {
        f2 = l2.pending;
      } else if (l2.pending) {
        f2 = [...f2, ...l2.pending];
      }
      n2 = n2 ? mergeResultPatch(n2, l2, o2, f2) : makeResult(e3, l2, o2);
      a2 = false;
      yield n2;
      a2 = true;
    }
    if (!n2) {
      yield n2 = makeResult(e3, {}, o2);
    }
  } catch (r3) {
    if (!a2) {
      throw r3;
    }
    yield makeErrorResult(e3, o2 && (o2.status < 200 || o2.status >= 300) && o2.statusText ? new Error(o2.statusText) : r3, o2);
  }
}
function makeFetchSource(e3, r2, t2) {
  var a2;
  if ("undefined" != typeof AbortController) {
    t2.signal = (a2 = new AbortController()).signal;
  }
  return onEnd(() => {
    if (a2) {
      a2.abort();
    }
  })(filter((e4) => !!e4)(fromAsyncIterable(fetchOperation(e3, r2, t2))));
}

// ../node_modules/@urql/core/dist/urql-core.mjs
var collectTypes = (e3, r2) => {
  if (Array.isArray(e3)) {
    for (var t2 = 0, n2 = e3.length; t2 < n2; t2++) {
      collectTypes(e3[t2], r2);
    }
  } else if ("object" == typeof e3 && null !== e3) {
    for (var a2 in e3) {
      if ("__typename" === a2 && "string" == typeof e3[a2]) {
        r2.add(e3[a2]);
      } else {
        collectTypes(e3[a2], r2);
      }
    }
  }
  return r2;
};
var formatNode = (r2) => {
  if ("definitions" in r2) {
    var t2 = [];
    for (var n2 = 0, a2 = r2.definitions.length; n2 < a2; n2++) {
      var i2 = formatNode(r2.definitions[n2]);
      t2.push(i2);
    }
    return {
      ...r2,
      definitions: t2
    };
  }
  if ("directives" in r2 && r2.directives && r2.directives.length) {
    var o2 = [];
    var s2 = {};
    for (var c2 = 0, u3 = r2.directives.length; c2 < u3; c2++) {
      var p2 = r2.directives[c2];
      var d3 = p2.name.value;
      if ("_" !== d3[0]) {
        o2.push(p2);
      } else {
        d3 = d3.slice(1);
      }
      s2[d3] = p2;
    }
    r2 = {
      ...r2,
      directives: o2,
      _directives: s2
    };
  }
  if ("selectionSet" in r2) {
    var l2 = [];
    var v2 = r2.kind === e.OPERATION_DEFINITION;
    if (r2.selectionSet) {
      for (var f2 = 0, h2 = r2.selectionSet.selections.length; f2 < h2; f2++) {
        var k = r2.selectionSet.selections[f2];
        v2 = v2 || k.kind === e.FIELD && "__typename" === k.name.value && !k.alias;
        var y2 = formatNode(k);
        l2.push(y2);
      }
      if (!v2) {
        l2.push({
          kind: e.FIELD,
          name: {
            kind: e.NAME,
            value: "__typename"
          },
          _generated: true
        });
      }
      return {
        ...r2,
        selectionSet: {
          ...r2.selectionSet,
          selections: l2
        }
      };
    }
  }
  return r2;
};
var I = /* @__PURE__ */ new Map();
var formatDocument = (e3) => {
  var t2 = keyDocument(e3);
  var n2 = I.get(t2.__key);
  if (!n2) {
    I.set(t2.__key, n2 = formatNode(t2));
    Object.defineProperty(n2, "__key", {
      value: t2.__key,
      enumerable: false
    });
  }
  return n2;
};
function withPromise(e3) {
  var source$ = (r2) => e3(r2);
  source$.toPromise = () => toPromise(take(1)(filter((e4) => !e4.stale && !e4.hasNext)(source$)));
  source$.then = (e4, r2) => source$.toPromise().then(e4, r2);
  source$.subscribe = (e4) => subscribe(e4)(source$);
  return source$;
}
function makeOperation(e3, r2, t2) {
  return {
    ...r2,
    kind: e3,
    context: r2.context ? {
      ...r2.context,
      ...t2
    } : t2 || r2.context
  };
}
var addMetadata = (e3, r2) => makeOperation(e3.kind, e3, {
  meta: {
    ...e3.context.meta,
    ...r2
  }
});
var noop = () => {
};
function gql(n2) {
  var a2 = /* @__PURE__ */ new Map();
  var i2 = [];
  var o2 = [];
  var s2 = Array.isArray(n2) ? n2[0] : n2 || "";
  for (var c2 = 1; c2 < arguments.length; c2++) {
    var u3 = arguments[c2];
    if (u3 && u3.definitions) {
      o2.push(u3);
    } else {
      s2 += u3;
    }
    s2 += arguments[0][c2];
  }
  o2.unshift(keyDocument(s2));
  for (var p2 = 0; p2 < o2.length; p2++) {
    for (var d3 = 0; d3 < o2[p2].definitions.length; d3++) {
      var l2 = o2[p2].definitions[d3];
      if (l2.kind === e.FRAGMENT_DEFINITION) {
        var v2 = l2.name.value;
        var f2 = stringifyDocument(l2);
        if (!a2.has(v2)) {
          a2.set(v2, f2);
          i2.push(l2);
        } else if (a2.get(v2) !== f2) {
          console.warn("[WARNING: Duplicate Fragment] A fragment with name `" + v2 + "` already exists in this document.\nWhile fragment names may not be unique across your source, each name must be unique per document.");
        }
      } else {
        i2.push(l2);
      }
    }
  }
  return keyDocument({
    kind: e.DOCUMENT,
    definitions: i2
  });
}
var shouldSkip = ({ kind: e3 }) => "mutation" !== e3 && "query" !== e3;
var mapTypeNames = (e3) => {
  var r2 = formatDocument(e3.query);
  if (r2 !== e3.query) {
    var t2 = makeOperation(e3.kind, e3);
    t2.query = r2;
    return t2;
  } else {
    return e3;
  }
};
var cacheExchange = ({ forward: e3, client: r2, dispatchDebug: t2 }) => {
  var a2 = /* @__PURE__ */ new Map();
  var i2 = /* @__PURE__ */ new Map();
  var isOperationCached = (e4) => "query" === e4.kind && "network-only" !== e4.context.requestPolicy && ("cache-only" === e4.context.requestPolicy || a2.has(e4.key));
  return (o2) => {
    var s2 = map((e4) => {
      var i3 = a2.get(e4.key);
      t2({
        operation: e4,
        ...i3 ? {
          type: "cacheHit",
          message: "The result was successfully retried from the cache"
        } : {
          type: "cacheMiss",
          message: "The result could not be retrieved from the cache"
        },
        source: "cacheExchange"
      });
      var o3 = i3 || makeResult(e4, {
        data: null
      });
      o3 = {
        ...o3,
        operation: addMetadata(e4, {
          cacheOutcome: i3 ? "hit" : "miss"
        })
      };
      if ("cache-and-network" === e4.context.requestPolicy) {
        o3.stale = true;
        reexecuteOperation(r2, e4);
      }
      return o3;
    })(filter((e4) => !shouldSkip(e4) && isOperationCached(e4))(o2));
    var c2 = onPush((e4) => {
      var { operation: n2 } = e4;
      if (!n2) {
        return;
      }
      var o3 = n2.context.additionalTypenames || [];
      if ("subscription" !== e4.operation.kind) {
        o3 = ((e5) => [...collectTypes(e5, /* @__PURE__ */ new Set())])(e4.data).concat(o3);
      }
      if ("mutation" === e4.operation.kind || "subscription" === e4.operation.kind) {
        var s3 = /* @__PURE__ */ new Set();
        t2({
          type: "cacheInvalidation",
          message: `The following typenames have been invalidated: ${o3}`,
          operation: n2,
          data: {
            typenames: o3,
            response: e4
          },
          source: "cacheExchange"
        });
        for (var c3 = 0; c3 < o3.length; c3++) {
          var u3 = o3[c3];
          var p2 = i2.get(u3);
          if (!p2) {
            i2.set(u3, p2 = /* @__PURE__ */ new Set());
          }
          for (var d3 of p2.values()) {
            s3.add(d3);
          }
          p2.clear();
        }
        for (var l2 of s3.values()) {
          if (a2.has(l2)) {
            n2 = a2.get(l2).operation;
            a2.delete(l2);
            reexecuteOperation(r2, n2);
          }
        }
      } else if ("query" === n2.kind && e4.data) {
        a2.set(n2.key, e4);
        for (var v2 = 0; v2 < o3.length; v2++) {
          var f2 = o3[v2];
          var h2 = i2.get(f2);
          if (!h2) {
            i2.set(f2, h2 = /* @__PURE__ */ new Set());
          }
          h2.add(n2.key);
        }
      }
    })(e3(filter((e4) => "query" !== e4.kind || "cache-only" !== e4.context.requestPolicy)(map((e4) => addMetadata(e4, {
      cacheOutcome: "miss"
    }))(merge([map(mapTypeNames)(filter((e4) => !shouldSkip(e4) && !isOperationCached(e4))(o2)), filter((e4) => shouldSkip(e4))(o2)])))));
    return merge([s2, c2]);
  };
};
var reexecuteOperation = (e3, r2) => e3.reexecuteOperation(makeOperation(r2.kind, r2, {
  requestPolicy: "network-only"
}));
var T = /* @__PURE__ */ new Set();
var ssrExchange = (e3 = {}) => {
  var r2 = !!e3.staleWhileRevalidate;
  var t2 = !!e3.includeExtensions;
  var n2 = {};
  var i2 = [];
  var invalidate = (e4) => {
    i2.push(e4.operation.key);
    if (1 === i2.length) {
      Promise.resolve().then(() => {
        var e5;
        while (e5 = i2.shift()) {
          n2[e5] = null;
        }
      });
    }
  };
  var ssr = ({ client: i3, forward: o2 }) => (s2) => {
    var c2 = e3 && "boolean" == typeof e3.isClient ? !!e3.isClient : !i3.suspense;
    var u3 = o2(map(mapTypeNames)(filter((e4) => "teardown" === e4.kind || !n2[e4.key] || !!n2[e4.key].hasNext || "network-only" === e4.context.requestPolicy)(s2)));
    var p2 = map((e4) => {
      var o3 = ((e5, r3, t3) => ({
        operation: e5,
        data: r3.data ? JSON.parse(r3.data) : void 0,
        extensions: t3 && r3.extensions ? JSON.parse(r3.extensions) : void 0,
        error: r3.error ? new CombinedError({
          networkError: r3.error.networkError ? new Error(r3.error.networkError) : void 0,
          graphQLErrors: r3.error.graphQLErrors
        }) : void 0,
        stale: false,
        hasNext: !!r3.hasNext
      }))(e4, n2[e4.key], t2);
      if (r2 && !T.has(e4.key)) {
        o3.stale = true;
        T.add(e4.key);
        reexecuteOperation(i3, e4);
      }
      return {
        ...o3,
        operation: addMetadata(e4, {
          cacheOutcome: "hit"
        })
      };
    })(filter((e4) => "teardown" !== e4.kind && !!n2[e4.key] && "network-only" !== e4.context.requestPolicy)(s2));
    if (!c2) {
      u3 = onPush((e4) => {
        var { operation: r3 } = e4;
        if ("mutation" !== r3.kind) {
          var a2 = ((e5, r4) => {
            var t3 = {
              hasNext: e5.hasNext
            };
            if (void 0 !== e5.data) {
              t3.data = JSON.stringify(e5.data);
            }
            if (r4 && void 0 !== e5.extensions) {
              t3.extensions = JSON.stringify(e5.extensions);
            }
            if (e5.error) {
              t3.error = {
                graphQLErrors: e5.error.graphQLErrors.map((e6) => {
                  if (!e6.path && !e6.extensions) {
                    return e6.message;
                  }
                  return {
                    message: e6.message,
                    path: e6.path,
                    extensions: e6.extensions
                  };
                })
              };
              if (e5.error.networkError) {
                t3.error.networkError = "" + e5.error.networkError;
              }
            }
            return t3;
          })(e4, t2);
          n2[r3.key] = a2;
        }
      })(u3);
    } else {
      p2 = onPush(invalidate)(p2);
    }
    return merge([u3, p2]);
  };
  ssr.restoreData = (e4) => {
    for (var r3 in e4) {
      if (null !== n2[r3]) {
        n2[r3] = e4[r3];
      }
    }
  };
  ssr.extractData = () => {
    var e4 = {};
    for (var r3 in n2) {
      if (null != n2[r3]) {
        e4[r3] = n2[r3];
      }
    }
    return e4;
  };
  if (e3 && e3.initialState) {
    ssr.restoreData(e3.initialState);
  }
  return ssr;
};
var subscriptionExchange = ({ forwardSubscription: e3, enableAllOperations: r2, isSubscriptionOperation: t2 }) => ({ client: a2, forward: i2 }) => {
  var u3 = t2 || ((e4) => "subscription" === e4.kind || !!r2 && ("query" === e4.kind || "mutation" === e4.kind));
  return (r3) => {
    var t3 = mergeMap((t4) => {
      var { key: i3 } = t4;
      var u4 = filter((e4) => "teardown" === e4.kind && e4.key === i3)(r3);
      return takeUntil(u4)(((r4) => {
        var t5 = e3(makeFetchBody(r4), r4);
        return make((e4) => {
          var i4 = false;
          var o2;
          var u5;
          function nextResult(t6) {
            e4.next(u5 = u5 ? mergeResultPatch(u5, t6) : makeResult(r4, t6));
          }
          Promise.resolve().then(() => {
            if (i4) {
              return;
            }
            o2 = t5.subscribe({
              next: nextResult,
              error(t6) {
                if (Array.isArray(t6)) {
                  nextResult({
                    errors: t6
                  });
                } else {
                  e4.next(makeErrorResult(r4, t6));
                }
                e4.complete();
              },
              complete() {
                if (!i4) {
                  i4 = true;
                  if ("subscription" === r4.kind) {
                    a2.reexecuteOperation(makeOperation("teardown", r4, r4.context));
                  }
                  if (u5 && u5.hasNext) {
                    nextResult({
                      hasNext: false
                    });
                  }
                  e4.complete();
                }
              }
            });
          });
          return () => {
            i4 = true;
            if (o2) {
              o2.unsubscribe();
            }
          };
        });
      })(t4));
    })(filter((e4) => "teardown" !== e4.kind && u3(e4))(r3));
    var p2 = i2(filter((e4) => "teardown" === e4.kind || !u3(e4))(r3));
    return merge([t3, p2]);
  };
};
var debugExchange = ({ forward: e3 }) => {
  if (false) {
    return (r2) => e3(r2);
  } else {
    return (r2) => onPush((e4) => console.log("[Exchange debug]: Completed operation: ", e4))(e3(onPush((e4) => console.log("[Exchange debug]: Incoming operation: ", e4))(r2)));
  }
};
var fetchExchange = ({ forward: e3, dispatchDebug: r2 }) => (t2) => {
  var n2 = mergeMap((e4) => {
    var n3 = makeFetchBody(e4);
    var a3 = makeFetchURL(e4, n3);
    var i2 = makeFetchOptions(e4, n3);
    r2({
      type: "fetchRequest",
      message: "A fetch request is being executed.",
      operation: e4,
      data: {
        url: a3,
        fetchOptions: i2
      },
      source: "fetchExchange"
    });
    var s2 = takeUntil(filter((r3) => "teardown" === r3.kind && r3.key === e4.key)(t2))(makeFetchSource(e4, a3, i2));
    if (true) {
      return onPush((t3) => {
        var n4 = !t3.data ? t3.error : void 0;
        r2({
          type: n4 ? "fetchError" : "fetchSuccess",
          message: `A ${n4 ? "failed" : "successful"} fetch response has been returned.`,
          operation: e4,
          data: {
            url: a3,
            fetchOptions: i2,
            value: n4 || t3
          },
          source: "fetchExchange"
        });
      })(s2);
    }
    return s2;
  })(filter((e4) => "teardown" !== e4.kind && ("subscription" !== e4.kind || !!e4.context.fetchSubscriptions))(t2));
  var a2 = e3(filter((e4) => "teardown" === e4.kind || "subscription" === e4.kind && !e4.context.fetchSubscriptions)(t2));
  return merge([n2, a2]);
};
var composeExchanges = (e3) => ({ client: r2, forward: t2, dispatchDebug: n2 }) => e3.reduceRight((e4, t3) => {
  var a2 = false;
  return t3({
    client: r2,
    forward(r3) {
      if (true) {
        if (a2) {
          throw new Error("forward() must only be called once in each Exchange.");
        }
        a2 = true;
      }
      return share(e4(share(r3)));
    },
    dispatchDebug(e5) {
      n2({
        timestamp: Date.now(),
        source: t3.name,
        ...e5
      });
    }
  });
}, t2);
var mapExchange = ({ onOperation: e3, onResult: r2, onError: t2 }) => ({ forward: n2 }) => (a2) => mergeMap((e4) => {
  if (t2 && e4.error) {
    t2(e4.error, e4.operation);
  }
  var n3 = r2 && r2(e4) || e4;
  return "then" in n3 ? fromPromise(n3) : fromValue(n3);
})(n2(mergeMap((r3) => {
  var t3 = e3 && e3(r3) || r3;
  return "then" in t3 ? fromPromise(t3) : fromValue(t3);
})(a2)));
var fallbackExchange = ({ dispatchDebug: e3 }) => (r2) => {
  if (true) {
    r2 = onPush((r3) => {
      if ("teardown" !== r3.kind && true) {
        var t2 = `No exchange has handled operations of kind "${r3.kind}". Check whether you've added an exchange responsible for these operations.`;
        e3({
          type: "fallbackCatch",
          message: t2,
          operation: r3,
          source: "fallbackExchange"
        });
        console.warn(t2);
      }
    })(r2);
  }
  return filter((e4) => false)(r2);
};
var C = function Client(e3) {
  if (!e3.url) {
    throw new Error("You are creating an urql-client without a url.");
  }
  var r2 = 0;
  var t2 = /* @__PURE__ */ new Map();
  var n2 = /* @__PURE__ */ new Map();
  var a2 = /* @__PURE__ */ new Set();
  var i2 = [];
  var o2 = {
    url: e3.url,
    fetchSubscriptions: e3.fetchSubscriptions,
    fetchOptions: e3.fetchOptions,
    fetch: e3.fetch,
    preferGetMethod: e3.preferGetMethod,
    requestPolicy: e3.requestPolicy || "cache-first"
  };
  var s2 = makeSubject();
  function nextOperation(e4) {
    if ("mutation" === e4.kind || "teardown" === e4.kind || !a2.has(e4.key)) {
      if ("teardown" === e4.kind) {
        a2.delete(e4.key);
      } else if ("mutation" !== e4.kind) {
        a2.add(e4.key);
      }
      s2.next(e4);
    }
  }
  var c2 = false;
  function dispatchOperation(e4) {
    if (e4) {
      nextOperation(e4);
    }
    if (!c2) {
      c2 = true;
      while (c2 && (e4 = i2.shift())) {
        nextOperation(e4);
      }
      c2 = false;
    }
  }
  var makeResultSource = (e4) => {
    var r3 = takeUntil(filter((r4) => "teardown" === r4.kind && r4.key === e4.key)(s2.source))(filter((r4) => r4.operation.kind === e4.kind && r4.operation.key === e4.key && (!r4.operation.context._instance || r4.operation.context._instance === e4.context._instance))(E));
    if ("query" !== e4.kind) {
      r3 = takeWhile((e5) => !!e5.hasNext, true)(r3);
    } else {
      r3 = switchMap((r4) => {
        var t3 = fromValue(r4);
        return r4.stale || r4.hasNext ? t3 : merge([t3, map(() => {
          r4.stale = true;
          return r4;
        })(take(1)(filter((r5) => r5.key === e4.key)(s2.source)))]);
      })(r3);
    }
    if ("mutation" !== e4.kind) {
      r3 = onEnd(() => {
        a2.delete(e4.key);
        t2.delete(e4.key);
        n2.delete(e4.key);
        c2 = false;
        for (var r4 = i2.length - 1; r4 >= 0; r4--) {
          if (i2[r4].key === e4.key) {
            i2.splice(r4, 1);
          }
        }
        nextOperation(makeOperation("teardown", e4, e4.context));
      })(onPush((r4) => {
        if (r4.stale) {
          if (!r4.hasNext) {
            a2.delete(e4.key);
          } else {
            for (var n3 = 0; n3 < i2.length; n3++) {
              var o3 = i2[n3];
              if (o3.key === r4.operation.key) {
                a2.delete(o3.key);
                break;
              }
            }
          }
        } else if (!r4.hasNext) {
          a2.delete(e4.key);
        }
        t2.set(e4.key, r4);
      })(r3));
    } else {
      r3 = onStart(() => {
        nextOperation(e4);
      })(r3);
    }
    return share(r3);
  };
  var u3 = this instanceof Client ? this : Object.create(Client.prototype);
  var p2 = Object.assign(u3, {
    suspense: !!e3.suspense,
    operations$: s2.source,
    reexecuteOperation(e4) {
      if ("teardown" === e4.kind) {
        dispatchOperation(e4);
      } else if ("mutation" === e4.kind) {
        i2.push(e4);
        Promise.resolve().then(dispatchOperation);
      } else if (n2.has(e4.key)) {
        var r3 = false;
        for (var t3 = 0; t3 < i2.length; t3++) {
          if (i2[t3].key === e4.key) {
            i2[t3] = e4;
            r3 = true;
          }
        }
        if (!(r3 || a2.has(e4.key) && "network-only" !== e4.context.requestPolicy)) {
          i2.push(e4);
          Promise.resolve().then(dispatchOperation);
        } else {
          a2.delete(e4.key);
          Promise.resolve().then(dispatchOperation);
        }
      }
    },
    createRequestOperation(e4, t3, n3) {
      if (!n3) {
        n3 = {};
      }
      var a3;
      if ("teardown" !== e4 && (a3 = getOperationType(t3.query)) !== e4) {
        throw new Error(`Expected operation of type "${e4}" but found "${a3}"`);
      }
      return makeOperation(e4, t3, {
        _instance: "mutation" === e4 ? r2 = r2 + 1 | 0 : void 0,
        ...o2,
        ...n3,
        requestPolicy: n3.requestPolicy || o2.requestPolicy,
        suspense: n3.suspense || false !== n3.suspense && p2.suspense
      });
    },
    executeRequestOperation(e4) {
      if ("mutation" === e4.kind) {
        return withPromise(makeResultSource(e4));
      }
      return withPromise(lazy(() => {
        var r3 = n2.get(e4.key);
        if (!r3) {
          n2.set(e4.key, r3 = makeResultSource(e4));
        }
        r3 = onStart(() => {
          dispatchOperation(e4);
        })(r3);
        var a3 = t2.get(e4.key);
        if ("query" === e4.kind && a3 && (a3.stale || a3.hasNext)) {
          return switchMap(fromValue)(merge([r3, filter((r4) => r4 === t2.get(e4.key))(fromValue(a3))]));
        } else {
          return r3;
        }
      }));
    },
    executeQuery(e4, r3) {
      var t3 = p2.createRequestOperation("query", e4, r3);
      return p2.executeRequestOperation(t3);
    },
    executeSubscription(e4, r3) {
      var t3 = p2.createRequestOperation("subscription", e4, r3);
      return p2.executeRequestOperation(t3);
    },
    executeMutation(e4, r3) {
      var t3 = p2.createRequestOperation("mutation", e4, r3);
      return p2.executeRequestOperation(t3);
    },
    readQuery(e4, r3, t3) {
      var n3 = null;
      subscribe((e5) => {
        n3 = e5;
      })(p2.query(e4, r3, t3)).unsubscribe();
      return n3;
    },
    query: (e4, r3, t3) => p2.executeQuery(createRequest(e4, r3), t3),
    subscription: (e4, r3, t3) => p2.executeSubscription(createRequest(e4, r3), t3),
    mutation: (e4, r3, t3) => p2.executeMutation(createRequest(e4, r3), t3)
  });
  var d3 = noop;
  if (true) {
    var { next: f2, source: x } = makeSubject();
    p2.subscribeToDebugTarget = (e4) => subscribe(e4)(x);
    d3 = f2;
  }
  var w = composeExchanges(e3.exchanges);
  var E = share(w({
    client: p2,
    dispatchDebug: d3,
    forward: fallbackExchange({
      dispatchDebug: d3
    })
  })(s2.source));
  publish(E);
  return p2;
};
var Q = C;
export {
  C as Client,
  CombinedError,
  cacheExchange,
  composeExchanges,
  Q as createClient,
  createRequest,
  debugExchange,
  mapExchange as errorExchange,
  fetchExchange,
  formatDocument,
  gql,
  makeErrorResult,
  makeOperation,
  makeResult,
  mapExchange,
  mergeResultPatch,
  ssrExchange,
  stringifyDocument,
  stringifyVariables,
  subscriptionExchange
};
//# sourceMappingURL=@urql_core.js.map
