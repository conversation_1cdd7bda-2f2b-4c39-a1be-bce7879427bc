{"version": 3, "file": "band.es.js", "sources": ["../../../src/custom-primitive/pane-view/band.ts"], "sourcesContent": ["import {CanvasRenderingTarget2D} from \"fancy-canvas\";\r\nimport {PrimitivePaneViewBase} from \"../primitive-base\";\r\nimport { Time} from \"lightweight-charts\";\r\nimport {ensureNotNull} from \"../../helpers/assertions\";\r\nimport {binarySearchIndex, timeToUnix} from \"../../helpers/utils\";\r\n\r\nexport interface BandPrimitivePaneViewOptions {\r\n  backgroundColor: string,\r\n}\r\n\r\nexport const BandPrimitiveOptionsDefault: BandPrimitivePaneViewOptions = {\r\n  backgroundColor: '#2196f31a',\r\n}\r\n\r\nexport type BandPrimitiveData = {\r\n  time: Time, \r\n  upper: number, \r\n  lower: number\r\n}\r\n\r\nexport class BandPrimitivePaneView extends PrimitivePaneViewBase<BandPrimitivePaneViewOptions, BandPrimitiveData> {\r\n\r\n  dataVisible() {\r\n    const visibleRange = this.getVisibleRange()\r\n    if(!visibleRange) return [];\r\n\r\n    const data = this.data;\r\n    let fromIndex = binarySearchIndex(data, timeToUnix(visibleRange.from), item => timeToUnix(item.time))\r\n    if(fromIndex === -1) fromIndex = 1\r\n    let toIndex = binarySearchIndex(data, timeToUnix(visibleRange.to), item => timeToUnix(item.time))\r\n    if(toIndex === -1) toIndex = data.length\r\n    return data.slice(fromIndex - 1, toIndex + 2)\r\n  }\r\n  drawBackground(target: CanvasRenderingTarget2D): void {\r\n    const data = this.dataVisible().map((item) => ({\r\n      x: ensureNotNull(this.timeToCoordinate(item.time)),\r\n      upperCoor: ensureNotNull(this.priceToCoordinate(item.upper)),\r\n      lowerCoor: ensureNotNull(this.priceToCoordinate(item.lower)),\r\n    }));\r\n\r\n    if(data.length < 2) return;\r\n    \r\n    target.useBitmapCoordinateSpace(scope => {\r\n      const ctx = scope.context;\r\n      ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);\r\n\r\n      ctx.beginPath();\r\n      const region = new Path2D();\r\n      const from = 0;\r\n      const to = data.length\r\n      const first = data[from];\r\n      region.moveTo(\r\n        first.x, \r\n        first.upperCoor\r\n      );\r\n\r\n      for (let i = from + 1; i < to; i++) {\r\n        const point = data[i];\r\n        region.lineTo(\r\n          point.x,\r\n          point.upperCoor\r\n        );\r\n      }\r\n\r\n      for (let i = to - 1; i >= from; i--) {\r\n        const point = data[i];\r\n        region.lineTo(point.x, point.lowerCoor);\r\n      }\r\n\r\n      region.lineTo(\r\n        first.x, \r\n        first.lowerCoor\r\n      );\r\n      region.closePath();\r\n      ctx.fillStyle = this.options.backgroundColor\r\n      ctx.fill(region)\r\n    })\r\n  }\r\n  defaultOptions(): BandPrimitivePaneViewOptions {\r\n    return BandPrimitiveOptionsDefault\r\n  }\r\n}"], "names": ["BandPrimitiveOptionsDefault", "BandPrimitivePaneView", "PrimitivePaneViewBase", "visibleRange", "data", "fromIndex", "binarySearchIndex", "timeToUnix", "item", "toIndex", "target", "ensureNotNull", "scope", "ctx", "region", "from", "to", "first", "i", "point"], "mappings": ";;;AAUO,MAAMA,IAA4D;AAAA,EACvE,iBAAiB;AACnB;AAQO,MAAMC,UAA8BC,EAAuE;AAAA,EAEhH,cAAc;AACN,UAAAC,IAAe,KAAK,gBAAgB;AACvC,QAAA,CAACA,EAAc,QAAO,CAAC;AAE1B,UAAMC,IAAO,KAAK;AACd,QAAAC,IAAYC,EAAkBF,GAAMG,EAAWJ,EAAa,IAAI,GAAG,CAAQK,MAAAD,EAAWC,EAAK,IAAI,CAAC;AACjG,IAAAH,MAAc,OAAgBA,IAAA;AAC7B,QAAAI,IAAUH,EAAkBF,GAAMG,EAAWJ,EAAa,EAAE,GAAG,CAAQK,MAAAD,EAAWC,EAAK,IAAI,CAAC;AAC7F,WAAAC,MAAY,OAAIA,IAAUL,EAAK,SAC3BA,EAAK,MAAMC,IAAY,GAAGI,IAAU,CAAC;AAAA,EAAA;AAAA,EAE9C,eAAeC,GAAuC;AACpD,UAAMN,IAAO,KAAK,YAAc,EAAA,IAAI,CAACI,OAAU;AAAA,MAC7C,GAAGG,EAAc,KAAK,iBAAiBH,EAAK,IAAI,CAAC;AAAA,MACjD,WAAWG,EAAc,KAAK,kBAAkBH,EAAK,KAAK,CAAC;AAAA,MAC3D,WAAWG,EAAc,KAAK,kBAAkBH,EAAK,KAAK,CAAC;AAAA,IAAA,EAC3D;AAEC,IAAAJ,EAAK,SAAS,KAEjBM,EAAO,yBAAyB,CAASE,MAAA;AACvC,YAAMC,IAAMD,EAAM;AAClB,MAAAC,EAAI,MAAMD,EAAM,sBAAsBA,EAAM,kBAAkB,GAE9DC,EAAI,UAAU;AACR,YAAAC,IAAS,IAAI,OAAO,GACpBC,IAAO,GACPC,IAAKZ,EAAK,QACVa,IAAQb,EAAKW,CAAI;AAChB,MAAAD,EAAA;AAAA,QACLG,EAAM;AAAA,QACNA,EAAM;AAAA,MACR;AAEA,eAASC,IAAIH,IAAO,GAAGG,IAAIF,GAAIE,KAAK;AAC5B,cAAAC,IAAQf,EAAKc,CAAC;AACb,QAAAJ,EAAA;AAAA,UACLK,EAAM;AAAA,UACNA,EAAM;AAAA,QACR;AAAA,MAAA;AAGF,eAASD,IAAIF,IAAK,GAAGE,KAAKH,GAAMG,KAAK;AAC7B,cAAAC,IAAQf,EAAKc,CAAC;AACpB,QAAAJ,EAAO,OAAOK,EAAM,GAAGA,EAAM,SAAS;AAAA,MAAA;AAGjC,MAAAL,EAAA;AAAA,QACLG,EAAM;AAAA,QACNA,EAAM;AAAA,MACR,GACAH,EAAO,UAAU,GACbD,EAAA,YAAY,KAAK,QAAQ,iBAC7BA,EAAI,KAAKC,CAAM;AAAA,IAAA,CAChB;AAAA,EAAA;AAAA,EAEH,iBAA+C;AACtC,WAAAd;AAAA,EAAA;AAEX;"}