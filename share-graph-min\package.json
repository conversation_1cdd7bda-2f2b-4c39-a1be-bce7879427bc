{"name": "@sharegraph-mini/root", "private": true, "version": "0.0.1", "packageManager": "yarn@1.22.21", "workspaces": ["app", "packages/*"], "scripts": {"dev": "turbo run dev", "dev:nodemon": "nodemon", "dev:app": "nodemon --config nodemon.app.json", "dev:charts": "nodemon --config nodemon.charts.json", "dev:hot": "nodemon --exec \"turbo run dev\"", "dev:test": "nodemon --config nodemon.test.json", "build": "turbo run build -- ", "test": "turbo run test", "lint": "turbo run lint"}, "devDependencies": {"nodemon": "^3.1.10", "prettier": "^3.0.0", "rimraf": "^5.0.0", "turbo": "latest"}}