import { AdvanceChart, Period } from "./advance-chart";
import { OHLCVSimple } from "./interface";

const container = document.getElementById('app') as HTMLElement;
const chart = new AdvanceChart(container, { height: 500 });
const generateSampleData = (days: number = 100): OHLCVSimple[] => {
    const data: OHLCVSimple[] = [];
    let basePrice = 100;
    const startTime = new Date('2024-01-01').getTime() / 1000;

    for (let i = 0; i < days; i++) {
    const time = startTime + i * 24 * 60 * 60; // Daily data
    const volatility = 0.02;
    const change = (Math.random() - 0.5) * volatility * basePrice;
    
    const open = basePrice;
    const close = basePrice + change;
    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice;
    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice;
    const volume = Math.floor(Math.random() * 1000000) + 100000;

    data.push({
        time: time as any,
        open,
        high,
        low,
        close,
        volume
    });

    basePrice = close;
    }

    return data;
};

chart.setChartType('candle')

chart.setData(generateSampleData(1000), { times: 1, period: Period.day })
chart.addIndicator('ema')
chart.addIndicator('wma')
chart.addIndicator('momentum')
chart.showVolume('volume_overlay')

// DataFeed example (commented out to avoid unused variable warning)
// const dataFeed = new DataFeed(chart, {
//     fetchInitialData: async () => {
//         return generateSampleData(1000)
//     },
//     fetchPaginationData: async () => {
//         return generateSampleData(1000)
//     },
//     fetchUpdateData: async () => {
//         return generateSampleData(1000)
//     }
// });
