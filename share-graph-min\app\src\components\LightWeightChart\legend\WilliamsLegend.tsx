import { FC } from 'react';
import { Legend } from './Legend';
import { williamsIndicator } from "@sharegraph-mini/advance-charts";
import { AdvanceChart } from "@sharegraph-mini/advance-charts";

const RSILegend: FC<{
  indicator: williamsIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="Williams %R"
      indicator={indicator}
      renderer={(d) =>
        d && d.value ? (
          <span style={{ color: indicator.options.color }}>
            {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>
        ) : null
      }
    />
  );
};

export default RSILegend;
