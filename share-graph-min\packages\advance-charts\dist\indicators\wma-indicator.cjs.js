"use strict";var v=Object.defineProperty;var p=(t,i,e)=>i in t?v(t,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[i]=e;var c=(t,i,e)=>p(t,typeof i!="symbol"?i+"":i,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const P=require("../custom-primitive/primitive-base.cjs.js"),w=require("../custom-primitive/pane-view/line.cjs.js"),f=require("./abstract-indicator.cjs.js"),l={color:"#03fc03",period:9,overlay:!0};class m extends P.SeriesPrimitiveBase{constructor(e){super();c(this,"linePrimitive");this.source=e,this.linePrimitive=new w.LinePrimitivePaneView({lineColor:this.source.options.color}),this._paneViews=[this.linePrimitive]}update(e){const s=[];for(const o of e){const r=o.value;r&&s.push({time:o.time,price:r[0]})}this.linePrimitive.update(s)}}class g extends f.ChartIndicator{constructor(){super(...arguments);c(this,"wmaPrimitive",new m(this))}getDefaultOptions(){return l}_mainSeriesChanged(e){e.attachPrimitive(this.wmaPrimitive)}remove(){var e;super.remove(),(e=this.mainSeries)==null||e.detachPrimitive(this.wmaPrimitive)}applyIndicatorData(){this.wmaPrimitive.update(this._executionContext.data)}formula(e){const s=e.new_var(e.symbol.close,this.options.period);if(!s.calculable())return;const o=s.getAll(),r=this.options.period;if(o.length<r)return;const u=Array.from({length:r},(a,n)=>r-n),d=u.reduce((a,n)=>a+n,0);return[o.slice(-r).reduce((a,n,h)=>a+n*u[h],0)/d]}}exports.WMAPrimitive=m;exports.default=g;exports.defaultOptions=l;
//# sourceMappingURL=wma-indicator.cjs.js.map
