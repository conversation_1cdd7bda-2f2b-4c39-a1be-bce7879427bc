var p = Object.defineProperty;
var u = (t, o, i) => o in t ? p(t, o, { enumerable: !0, configurable: !0, writable: !0, value: i }) : t[o] = i;
var m = (t, o, i) => u(t, typeof o != "symbol" ? o + "" : o, i);
import { LineSeries as d } from "lightweight-charts";
import { ChartIndicator as w } from "./abstract-indicator.es.js";
import { RegionPrimitive as f } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as g } from "../helpers/utils.es.js";
const S = {
  color: "rgba(108, 80, 175, 1)",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#7e57c21a",
  period: 14,
  overlay: !1
};
class y extends w {
  constructor(i, e, s) {
    super(i, e);
    m(this, "williamsSeries");
    this.williamsSeries = i.addSeries(d, {
      color: this.options.color,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "williams",
      autoscaleInfoProvider: g({ maxValue: 0, minValue: -100 })
    }, s), this.williamsSeries.attachPrimitive(
      new f({
        upPrice: -20,
        lowPrice: -80,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return S;
  }
  formula(i) {
    const e = this.options.period, s = i.new_var(i.symbol.high, e + 1), l = i.new_var(i.symbol.low, e + 1);
    if (!s.calculable() || !l.calculable()) return;
    const a = s.getAll().slice(0, -1), n = l.getAll().slice(0, -1), h = i.symbol.close;
    if (a.length < e || n.length < e) return;
    const r = Math.max(...a.slice(-e)), c = Math.min(...n.slice(-e));
    return r === c ? [-50] : [(r - h) / (r - c) * -100];
  }
  applyIndicatorData() {
    const i = [];
    for (const e of this._executionContext.data) {
      const s = e.value;
      s && i.push({ time: e.time, value: s[0] });
    }
    this.williamsSeries.setData(i);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.williamsSeries);
  }
  _applyOptions() {
    this.williamsSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(i) {
    this.williamsSeries.moveToPane(i);
  }
  getPaneIndex() {
    return this.williamsSeries.getPane().paneIndex();
  }
}
export {
  y as default,
  S as defaultOptions
};
//# sourceMappingURL=williams-indicator.es.js.map
