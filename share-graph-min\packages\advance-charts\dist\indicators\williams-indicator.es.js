var n = Object.defineProperty;
var c = (l, i, e) => i in l ? n(l, i, { enumerable: !0, configurable: !0, writable: !0, value: e }) : l[i] = e;
var a = (l, i, e) => c(l, typeof i != "symbol" ? i + "" : i, e);
import { LineSeries as p } from "lightweight-charts";
import { WilliamsR as m } from "technicalindicators";
import { ChartIndicator as h } from "./abstract-indicator.es.js";
import { RegionPrimitive as u } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as d } from "../helpers/utils.es.js";
const w = {
  color: "rgba(108, 80, 175, 1)",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#7e57c21a",
  period: 14,
  overlay: !1
};
class C extends h {
  constructor(e, o, r) {
    super(e, o);
    a(this, "williamsSeries");
    this.williamsSeries = e.addSeries(p, {
      color: this.options.color,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "williams",
      autoscaleInfoProvider: d({ maxValue: 0, minValue: -100 })
    }, r), this.williamsSeries.attachPrimitive(
      new u({
        upPrice: -20,
        lowPrice: -80,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return w;
  }
  formula(e) {
    const o = e.new_var(e.symbol.high, this.options.period), r = e.new_var(e.symbol.low, this.options.period), t = e.new_var(e.symbol.close, this.options.period);
    if (!o.calculable() || !r.calculable() || !t.calculable()) return;
    const s = new m({
      period: this.options.period,
      high: o.getAll(),
      low: r.getAll(),
      close: t.getAll()
    }).getResult();
    if (s.length !== 0)
      return [s[s.length - 1]];
  }
  applyIndicatorData() {
    const e = [];
    for (const o of this._executionContext.data) {
      const r = o.value;
      r && e.push({ time: o.time, value: r[0] });
    }
    this.williamsSeries.setData(e);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.williamsSeries);
  }
  _applyOptions() {
    this.williamsSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.williamsSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.williamsSeries.getPane().paneIndex();
  }
}
export {
  C as default,
  w as defaultOptions
};
//# sourceMappingURL=williams-indicator.es.js.map
