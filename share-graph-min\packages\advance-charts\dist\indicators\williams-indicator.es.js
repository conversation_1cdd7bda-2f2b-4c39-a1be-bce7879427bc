var w = Object.defineProperty;
var f = (s, e, i) => e in s ? w(s, e, { enumerable: !0, configurable: !0, writable: !0, value: i }) : s[e] = i;
var g = (s, e, i) => f(s, typeof e != "symbol" ? e + "" : e, i);
import { LineSeries as S } from "lightweight-charts";
import { ChartIndicator as v } from "./abstract-indicator.es.js";
import { RegionPrimitive as b } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as C } from "../helpers/utils.es.js";
const x = {
  color: "rgba(108, 80, 175, 1)",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#7e57c21a",
  period: 14,
  overlay: !1
};
class M extends v {
  constructor(i, o, t) {
    super(i, o);
    g(this, "williamsSeries");
    this.williamsSeries = i.addSeries(S, {
      color: this.options.color,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "williams",
      autoscaleInfoProvider: C({ maxValue: 0, minValue: -100 })
    }, t), this.williamsSeries.attachPrimitive(
      new b({
        upPrice: -20,
        lowPrice: -80,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return x;
  }
  formula(i) {
    const o = i.new_var(i.symbol.high, this.options.period), t = i.new_var(i.symbol.low, this.options.period), n = i.new_var(i.symbol.close, this.options.period);
    if (!o.calculable() || !t.calculable() || !n.calculable()) return;
    const c = o.getAll(), p = t.getAll(), l = n.getAll();
    if (c.length < this.options.period || p.length < this.options.period || l.length < this.options.period)
      return;
    const r = Math.max(...c), h = Math.min(...p), m = l[l.length - 1], a = r - h;
    if (a === 0)
      return [0];
    const u = (r - m) / a * -100, d = Math.max(-100, Math.min(0, u));
    return console.log("Williams %R Calculation:", {
      period: this.options.period,
      highestHigh: r,
      lowestLow: h,
      currentClose: m,
      range: a,
      williamsR: u,
      clampedWilliamsR: d
    }), [d];
  }
  applyIndicatorData() {
    const i = [];
    for (const o of this._executionContext.data) {
      const t = o.value;
      t && i.push({ time: o.time, value: t[0] });
    }
    this.williamsSeries.setData(i);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.williamsSeries);
  }
  _applyOptions() {
    this.williamsSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(i) {
    this.williamsSeries.moveToPane(i);
  }
  getPaneIndex() {
    return this.williamsSeries.getPane().paneIndex();
  }
}
export {
  M as default,
  x as defaultOptions
};
//# sourceMappingURL=williams-indicator.es.js.map
