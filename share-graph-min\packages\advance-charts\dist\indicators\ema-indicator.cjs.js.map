{"version": 3, "file": "ema-indicator.cjs.js", "sources": ["../../src/indicators/ema-indicator.ts"], "sourcesContent": ["import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from \"lightweight-charts\";\r\nimport { SMAIndicatorOptions } from \"./sma-indicator\";\r\nimport { Context, IIndicatorBar } from \"../helpers/execution-indicator\";\r\nimport { SeriesPrimitiveBase } from \"../custom-primitive/primitive-base\";\r\nimport { LineData, LinePrimitivePaneView } from \"../custom-primitive/pane-view/line\";\r\nimport { ChartIndicator } from \"./abstract-indicator\";\r\n\r\nexport interface EMAIndicatorOptions extends SMAIndicatorOptions {}\r\n\r\nexport const defaultOptions: EMAIndicatorOptions = {\r\n    color: \"#d26400\",\r\n    period: 9,\r\n    overlay: true\r\n}\r\n\r\nexport class EMAPrimitive extends SeriesPrimitiveBase<\r\nSingleValueData | WhitespaceData\r\n> {\r\n    linePrimitive: LinePrimitivePaneView;\r\n    constructor(protected source: EMAIndicator) {\r\n        super();\r\n        this.linePrimitive = new LinePrimitivePaneView({\r\n            lineColor: this.source.options.color,\r\n        });\r\n        this._paneViews = [this.linePrimitive];\r\n    }\r\n\r\n    update(indicatorBars: IIndicatorBar<EMAData>[]) {\r\n        const lineData: LineData[] = []\r\n        for(const bar of indicatorBars) {\r\n            const value = bar.value\r\n            if(value) lineData.push({time: bar.time as Time, price: value[0]})\r\n        }\r\n\r\n        this.linePrimitive.update(lineData);\r\n    }\r\n}\r\n\r\nexport type EMAData = readonly [Nominal<number, 'EMA'>]\r\n\r\nexport default class EMAIndicator extends ChartIndicator<EMAIndicatorOptions, EMAData> {\r\n    emaPrimitive = new EMAPrimitive(this)\r\n\r\n    getDefaultOptions(): EMAIndicatorOptions {\r\n        return defaultOptions\r\n    }\r\n\r\n    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {\r\n        series.attachPrimitive(this.emaPrimitive)\r\n    }\r\n\r\n    remove(): void {\r\n        super.remove();\r\n        this.mainSeries?.detachPrimitive(this.emaPrimitive)\r\n    }\r\n\r\n    applyIndicatorData(): void {\r\n        this.emaPrimitive.update(\r\n            this._executionContext.data\r\n        )\r\n    }\r\n\r\n    formula(c: Context) {\r\n        console.log(c);\r\n        \r\n        const period = this.options.period\r\n        const close = c.symbol.close\r\n\r\n        const alpha = 2 / (period + 1);\r\n\r\n        const closeSeries = c.new_var(close, period);\r\n        const emaSeries = c.new_var(NaN, 2);\r\n\r\n        if(!closeSeries.calculable()) return;\r\n        const previusEMA = emaSeries.get(1)\r\n\r\n        let ema: number;\r\n\r\n        if(isNaN(previusEMA)) {\r\n            const closes = closeSeries.getAll();\r\n            ema = closes.reduce((sum, val) => sum + val, 0) / period;\r\n            emaSeries.set(ema);\r\n\r\n            return [ema as Nominal<number, 'EMA'>] as EMAData\r\n        } else {\r\n            ema = alpha * close + (1 - alpha) * previusEMA;\r\n        }\r\n\r\n        emaSeries.set(ema);\r\n\r\n        return [ema as Nominal<number, 'EMA'>] as EMAData\r\n    }\r\n\r\n}"], "names": ["defaultOptions", "EMAPrimitive", "SeriesPrimitiveBase", "source", "__publicField", "LinePrimitivePaneView", "indicatorBars", "lineData", "bar", "value", "EMAIndicator", "ChartIndicator", "series", "_a", "c", "period", "close", "alpha", "closeSeries", "emaSeries", "previusEMA", "ema", "sum", "val"], "mappings": "6aASaA,EAAsC,CAC/C,MAAO,UACP,OAAQ,EACR,QAAS,EACb,EAEO,MAAMC,UAAqBC,EAAAA,mBAEhC,CAEE,YAAsBC,EAAsB,CAClC,MAAA,EAFVC,EAAA,sBACsB,KAAA,OAAAD,EAEb,KAAA,cAAgB,IAAIE,wBAAsB,CAC3C,UAAW,KAAK,OAAO,QAAQ,KAAA,CAClC,EACI,KAAA,WAAa,CAAC,KAAK,aAAa,CAAA,CAGzC,OAAOC,EAAyC,CAC5C,MAAMC,EAAuB,CAAC,EAC9B,UAAUC,KAAOF,EAAe,CAC5B,MAAMG,EAAQD,EAAI,MACfC,GAAgBF,EAAA,KAAK,CAAC,KAAMC,EAAI,KAAc,MAAOC,EAAM,CAAC,CAAA,CAAE,CAAA,CAGhE,KAAA,cAAc,OAAOF,CAAQ,CAAA,CAE1C,CAIA,MAAqBG,UAAqBC,EAAAA,cAA6C,CAAvF,kCACIP,EAAA,oBAAe,IAAIH,EAAa,IAAI,GAEpC,mBAAyC,CAC9B,OAAAD,CAAA,CAGX,mBAAmBY,EAAsC,CAC9CA,EAAA,gBAAgB,KAAK,YAAY,CAAA,CAG5C,QAAe,OACX,MAAM,OAAO,GACRC,EAAA,KAAA,aAAA,MAAAA,EAAY,gBAAgB,KAAK,aAAY,CAGtD,oBAA2B,CACvB,KAAK,aAAa,OACd,KAAK,kBAAkB,IAC3B,CAAA,CAGJ,QAAQC,EAAY,CAChB,QAAQ,IAAIA,CAAC,EAEP,MAAAC,EAAS,KAAK,QAAQ,OACtBC,EAAQF,EAAE,OAAO,MAEjBG,EAAQ,GAAKF,EAAS,GAEtBG,EAAcJ,EAAE,QAAQE,EAAOD,CAAM,EACrCI,EAAYL,EAAE,QAAQ,IAAK,CAAC,EAE/B,GAAA,CAACI,EAAY,aAAc,OACxB,MAAAE,EAAaD,EAAU,IAAI,CAAC,EAE9B,IAAAE,EAED,OAAA,MAAMD,CAAU,GAETC,EADSH,EAAY,OAAO,EACrB,OAAO,CAACI,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAAIR,EAClDI,EAAU,IAAIE,CAAG,EAEV,CAACA,CAA6B,IAE/BA,EAAAJ,EAAQD,GAAS,EAAIC,GAASG,EAGxCD,EAAU,IAAIE,CAAG,EAEV,CAACA,CAA6B,EAAA,CAG7C"}