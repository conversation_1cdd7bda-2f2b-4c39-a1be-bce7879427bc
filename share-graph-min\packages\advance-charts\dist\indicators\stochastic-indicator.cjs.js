"use strict";var f=Object.defineProperty;var g=(r,i,e)=>i in r?f(r,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[i]=e;var l=(r,i,e)=>g(r,typeof i!="symbol"?i+"":i,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const u=require("lightweight-charts"),m=require("technicalindicators"),v=require("./abstract-indicator.cjs.js"),b=require("../custom-primitive/primitive/region.cjs.js"),k=require("../helpers/utils.cjs.js"),d=require("es-toolkit/compat"),h=require("../helpers/assertions.cjs.js"),p={color:"#2962ff",signalColor:"#ff6d00",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#2196f31a",period:14,overlay:!1,signalPeriod:3};class P extends v.ChartIndicator{constructor(e,t,s){super(e,t);l(this,"kSeries");l(this,"dSeries");this.kSeries=e.addSeries(u.LineSeries,{color:this.options.color,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"Stochastic"},s),this.dSeries=e.addSeries(u.LineSeries,{color:this.options.signalColor,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"Stochastic",autoscaleInfoProvider:k.autoScaleInfoProviderCreator({maxValue:90,minValue:10})},s),this.kSeries.attachPrimitive(new b.RegionPrimitive({upPrice:80,lowPrice:20,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return p}formula(e){const t=e.new_var(e.symbol.high,this.options.period),s=e.new_var(e.symbol.low,this.options.period),o=e.new_var(NaN,this.options.signalPeriod);if(!t.calculable()||!s.calculable())return;const n=h.ensureDefined(d.max(t.getAll())),c=h.ensureDefined(d.min(s.getAll()));let a=(e.symbol.close-c)/(n-c)*100;if(a=isNaN(a)?0:a,o.set(a),!o.calculable())return;const[S]=new m.SMA({period:this.options.signalPeriod,values:o.getAll()}).result;return[a,S]}applyIndicatorData(){const e=[],t=[];for(const s of this._executionContext.data){const o=s.value,n=s.time;o&&(e.push({time:n,value:o[0]}),t.push({time:n,value:o[1]}))}this.kSeries.setData(e),this.dSeries.setData(t)}remove(){super.remove(),this.chart.removeSeries(this.kSeries),this.chart.removeSeries(this.dSeries)}_applyOptions(){this.kSeries.applyOptions({color:this.options.color}),this.dSeries.applyOptions({color:this.options.signalColor}),this.calcIndicatorData(),this.applyIndicatorData()}setPaneIndex(e){this.kSeries.moveToPane(e),this.dSeries.moveToPane(e)}getPaneIndex(){return this.kSeries.getPane().paneIndex()}}exports.default=P;exports.defaultOptions=p;
//# sourceMappingURL=stochastic-indicator.cjs.js.map
