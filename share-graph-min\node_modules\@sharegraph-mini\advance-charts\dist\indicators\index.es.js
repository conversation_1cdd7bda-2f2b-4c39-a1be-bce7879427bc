import t from "./bb-indicator.es.js";
import i from "./macd-indicator.es.js";
import m from "./rsi-indicator.es.js";
import o from "./volume-indicator.es.js";
import { IndicatorFactory as r } from "./indicator-factory.es.js";
import a from "./sma-indicator.es.js";
import e from "./stochastic-indicator.es.js";
import c from "./ema-indicator.es.js";
import n from "./wma-indicator.es.js";
import d from "./momentum-indicator.es.js";
import I from "./williams-indicator.es.js";
r.registerIndicator("bb", t);
r.registerIndicator("rsi", m);
r.registerIndicator("macd", i);
r.registerIndicator("volume_overlay", o, { overlay: !0 });
r.registerIndicator("volume", o);
r.registerIndicator("sma", a);
r.registerIndicator("stochastic", e);
r.registerIndicator("ema", c);
r.registerIndicator("wma", n);
r.registerIndicator("momentum", d);
r.registerIndicator("williams", I);
export {
  t as BBIndicator,
  c as EMAIndicator,
  r as IndicatorFactory,
  i as MACDIndicator,
  d as MomentumIndicator,
  m as RSIIndicator,
  a as SMAIndicator,
  e as StochasticIndicator,
  o as VolumeIndicator,
  n as WMAIndicator,
  I as WilliamsIndicator
};
//# sourceMappingURL=index.es.js.map
