{"version": 3, "file": "resolve-path.js", "sourceRoot": "", "sources": ["../../../../lib/src/legacy/resolve-path.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;AAWvC,kCAeC;AAxBD,yBAAyB;AACzB,0BAA0B;AAE1B;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,IAAY,EAAE,UAAmB;IAC3D,MAAM,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;QAC3E,OAAO,CACL,CAAC,UAAU;YACT,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,SAAS,EAAE,CAAC,CAAC;YACrE,CAAC,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CACvC,CAAC;IACJ,CAAC;IAED,OAAO,CACL,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzE,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACvC,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CACrC,CAAC;AACJ,CAAC;AAED,sEAAsE;AACtE,SAAS,qBAAqB,CAAC,IAAY;IACzC,MAAM,MAAM,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC;IACxE,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;AAC7D,CAAC;AAED,8EAA8E;AAC9E,oDAAoD;AACpD,SAAS,OAAO,CAAC,IAAY;IAC3B,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAChE,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,UAAU,CAAC,OAAO,CAAC;QAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,IAAI,UAAU,CAAC,IAAI,CAAC;QAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,8EAA8E;AAC9E,gDAAgD;AAChD,SAAS,kBAAkB,CAAC,IAAY,EAAE,UAAmB;IAC3D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC;IAElC,OAAO,CACL,CAAC,UAAU;QACT,CAAC,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CACtE,CAAC;AACJ,CAAC;AAED,6EAA6E;AAC7E,4EAA4E;AAC5E,SAAS,UAAU,CAAC,KAAe;IACjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC;IACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAExC,MAAM,IAAI,KAAK,CACb,+CAA+C;QAC7C,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5C,CAAC;AACJ,CAAC;AAED,oEAAoE;AACpE,SAAS,UAAU,CAAC,IAAY;IAC9B,6EAA6E;IAC7E,6EAA6E;IAC7E,4EAA4E;IAC5E,cAAc;IACd,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;QAAE,OAAO,KAAK,CAAC;IAEvC,IAAI,CAAC;QACH,OAAO,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;IACpC,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,IAAK,KAA+B,CAAC,IAAI,KAAK,QAAQ;YAAE,OAAO,KAAK,CAAC;QACrE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,oEAAoE;AACpE,SAAS,SAAS,CAAC,IAAY;IAC7B,6EAA6E;IAC7E,6EAA6E;IAC7E,4EAA4E;IAC5E,cAAc;IACd,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;QAAE,OAAO,KAAK,CAAC;IAEvC,IAAI,CAAC;QACH,OAAO,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IACzC,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,IAAK,KAA+B,CAAC,IAAI,KAAK,QAAQ;YAAE,OAAO,KAAK,CAAC;QACrE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,6CAA6C;AAC7C,SAAS,gBAAgB,CAAC,IAAY;IACpC,MAAM,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3D,CAAC"}