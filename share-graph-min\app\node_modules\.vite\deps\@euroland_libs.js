import {
  require_dayjs_min
} from "./chunk-DS522S2D.js";
import {
  require_utc
} from "./chunk-LYBXDMTC.js";
import {
  require_timezone
} from "./chunk-6GXFYDEO.js";
import {
  __commonJS,
  __toESM
} from "./chunk-DC5AMYBS.js";

// ../node_modules/dayjs/plugin/duration.js
var require_duration = __commonJS({
  "../node_modules/dayjs/plugin/duration.js"(exports, module) {
    !function(t2, s2) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = s2() : "function" == typeof define && define.amd ? define(s2) : (t2 = "undefined" != typeof globalThis ? globalThis : t2 || self).dayjs_plugin_duration = s2();
    }(exports, function() {
      "use strict";
      var t2, s2, n4 = 1e3, i3 = 6e4, e2 = 36e5, r2 = 864e5, o = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g, u5 = 31536e6, d5 = 2628e6, a5 = /^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/, h3 = { years: u5, months: d5, days: r2, hours: e2, minutes: i3, seconds: n4, milliseconds: 1, weeks: 6048e5 }, c3 = function(t3) {
        return t3 instanceof g4;
      }, f5 = function(t3, s3, n5) {
        return new g4(t3, n5, s3.$l);
      }, m3 = function(t3) {
        return s2.p(t3) + "s";
      }, l2 = function(t3) {
        return t3 < 0;
      }, $2 = function(t3) {
        return l2(t3) ? Math.ceil(t3) : Math.floor(t3);
      }, y2 = function(t3) {
        return Math.abs(t3);
      }, v2 = function(t3, s3) {
        return t3 ? l2(t3) ? { negative: true, format: "" + y2(t3) + s3 } : { negative: false, format: "" + t3 + s3 } : { negative: false, format: "" };
      }, g4 = function() {
        function l3(t3, s3, n5) {
          var i4 = this;
          if (this.$d = {}, this.$l = n5, void 0 === t3 && (this.$ms = 0, this.parseFromMilliseconds()), s3) return f5(t3 * h3[m3(s3)], this);
          if ("number" == typeof t3) return this.$ms = t3, this.parseFromMilliseconds(), this;
          if ("object" == typeof t3) return Object.keys(t3).forEach(function(s4) {
            i4.$d[m3(s4)] = t3[s4];
          }), this.calMilliseconds(), this;
          if ("string" == typeof t3) {
            var e3 = t3.match(a5);
            if (e3) {
              var r3 = e3.slice(2).map(function(t4) {
                return null != t4 ? Number(t4) : 0;
              });
              return this.$d.years = r3[0], this.$d.months = r3[1], this.$d.weeks = r3[2], this.$d.days = r3[3], this.$d.hours = r3[4], this.$d.minutes = r3[5], this.$d.seconds = r3[6], this.calMilliseconds(), this;
            }
          }
          return this;
        }
        var y3 = l3.prototype;
        return y3.calMilliseconds = function() {
          var t3 = this;
          this.$ms = Object.keys(this.$d).reduce(function(s3, n5) {
            return s3 + (t3.$d[n5] || 0) * h3[n5];
          }, 0);
        }, y3.parseFromMilliseconds = function() {
          var t3 = this.$ms;
          this.$d.years = $2(t3 / u5), t3 %= u5, this.$d.months = $2(t3 / d5), t3 %= d5, this.$d.days = $2(t3 / r2), t3 %= r2, this.$d.hours = $2(t3 / e2), t3 %= e2, this.$d.minutes = $2(t3 / i3), t3 %= i3, this.$d.seconds = $2(t3 / n4), t3 %= n4, this.$d.milliseconds = t3;
        }, y3.toISOString = function() {
          var t3 = v2(this.$d.years, "Y"), s3 = v2(this.$d.months, "M"), n5 = +this.$d.days || 0;
          this.$d.weeks && (n5 += 7 * this.$d.weeks);
          var i4 = v2(n5, "D"), e3 = v2(this.$d.hours, "H"), r3 = v2(this.$d.minutes, "M"), o2 = this.$d.seconds || 0;
          this.$d.milliseconds && (o2 += this.$d.milliseconds / 1e3, o2 = Math.round(1e3 * o2) / 1e3);
          var u6 = v2(o2, "S"), d6 = t3.negative || s3.negative || i4.negative || e3.negative || r3.negative || u6.negative, a6 = e3.format || r3.format || u6.format ? "T" : "", h4 = (d6 ? "-" : "") + "P" + t3.format + s3.format + i4.format + a6 + e3.format + r3.format + u6.format;
          return "P" === h4 || "-P" === h4 ? "P0D" : h4;
        }, y3.toJSON = function() {
          return this.toISOString();
        }, y3.format = function(t3) {
          var n5 = t3 || "YYYY-MM-DDTHH:mm:ss", i4 = { Y: this.$d.years, YY: s2.s(this.$d.years, 2, "0"), YYYY: s2.s(this.$d.years, 4, "0"), M: this.$d.months, MM: s2.s(this.$d.months, 2, "0"), D: this.$d.days, DD: s2.s(this.$d.days, 2, "0"), H: this.$d.hours, HH: s2.s(this.$d.hours, 2, "0"), m: this.$d.minutes, mm: s2.s(this.$d.minutes, 2, "0"), s: this.$d.seconds, ss: s2.s(this.$d.seconds, 2, "0"), SSS: s2.s(this.$d.milliseconds, 3, "0") };
          return n5.replace(o, function(t4, s3) {
            return s3 || String(i4[t4]);
          });
        }, y3.as = function(t3) {
          return this.$ms / h3[m3(t3)];
        }, y3.get = function(t3) {
          var s3 = this.$ms, n5 = m3(t3);
          return "milliseconds" === n5 ? s3 %= 1e3 : s3 = "weeks" === n5 ? $2(s3 / h3[n5]) : this.$d[n5], s3 || 0;
        }, y3.add = function(t3, s3, n5) {
          var i4;
          return i4 = s3 ? t3 * h3[m3(s3)] : c3(t3) ? t3.$ms : f5(t3, this).$ms, f5(this.$ms + i4 * (n5 ? -1 : 1), this);
        }, y3.subtract = function(t3, s3) {
          return this.add(t3, s3, true);
        }, y3.locale = function(t3) {
          var s3 = this.clone();
          return s3.$l = t3, s3;
        }, y3.clone = function() {
          return f5(this.$ms, this);
        }, y3.humanize = function(s3) {
          return t2().add(this.$ms, "ms").locale(this.$l).fromNow(!s3);
        }, y3.valueOf = function() {
          return this.asMilliseconds();
        }, y3.milliseconds = function() {
          return this.get("milliseconds");
        }, y3.asMilliseconds = function() {
          return this.as("milliseconds");
        }, y3.seconds = function() {
          return this.get("seconds");
        }, y3.asSeconds = function() {
          return this.as("seconds");
        }, y3.minutes = function() {
          return this.get("minutes");
        }, y3.asMinutes = function() {
          return this.as("minutes");
        }, y3.hours = function() {
          return this.get("hours");
        }, y3.asHours = function() {
          return this.as("hours");
        }, y3.days = function() {
          return this.get("days");
        }, y3.asDays = function() {
          return this.as("days");
        }, y3.weeks = function() {
          return this.get("weeks");
        }, y3.asWeeks = function() {
          return this.as("weeks");
        }, y3.months = function() {
          return this.get("months");
        }, y3.asMonths = function() {
          return this.as("months");
        }, y3.years = function() {
          return this.get("years");
        }, y3.asYears = function() {
          return this.as("years");
        }, l3;
      }(), p5 = function(t3, s3, n5) {
        return t3.add(s3.years() * n5, "y").add(s3.months() * n5, "M").add(s3.days() * n5, "d").add(s3.hours() * n5, "h").add(s3.minutes() * n5, "m").add(s3.seconds() * n5, "s").add(s3.milliseconds() * n5, "ms");
      };
      return function(n5, i4, e3) {
        t2 = e3, s2 = e3().$utils(), e3.duration = function(t3, s3) {
          var n6 = e3.locale();
          return f5(t3, { $l: n6 }, s3);
        }, e3.isDuration = c3;
        var r3 = i4.prototype.add, o2 = i4.prototype.subtract;
        i4.prototype.add = function(t3, s3) {
          return c3(t3) ? p5(this, t3, 1) : r3.bind(this)(t3, s3);
        }, i4.prototype.subtract = function(t3, s3) {
          return c3(t3) ? p5(this, t3, -1) : o2.bind(this)(t3, s3);
        };
      };
    });
  }
});

// ../node_modules/dayjs/locale/ar.js
var require_ar = __commonJS({
  "../node_modules/dayjs/locale/ar.js"(exports, module) {
    !function(e2, t2) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t2(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], t2) : (e2 = "undefined" != typeof globalThis ? globalThis : e2 || self).dayjs_locale_ar = t2(e2.dayjs);
    }(exports, function(e2) {
      "use strict";
      function t2(e3) {
        return e3 && "object" == typeof e3 && "default" in e3 ? e3 : { default: e3 };
      }
      var n4 = t2(e2), r2 = "يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"), d5 = { 1: "١", 2: "٢", 3: "٣", 4: "٤", 5: "٥", 6: "٦", 7: "٧", 8: "٨", 9: "٩", 0: "٠" }, _ = { "١": "1", "٢": "2", "٣": "3", "٤": "4", "٥": "5", "٦": "6", "٧": "7", "٨": "8", "٩": "9", "٠": "0" }, o = { name: "ar", weekdays: "الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"), weekdaysShort: "أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"), weekdaysMin: "ح_ن_ث_ر_خ_ج_س".split("_"), months: r2, monthsShort: r2, weekStart: 6, meridiem: function(e3) {
        return e3 > 12 ? "م" : "ص";
      }, relativeTime: { future: "بعد %s", past: "منذ %s", s: "ثانية واحدة", m: "دقيقة واحدة", mm: "%d دقائق", h: "ساعة واحدة", hh: "%d ساعات", d: "يوم واحد", dd: "%d أيام", M: "شهر واحد", MM: "%d أشهر", y: "عام واحد", yy: "%d أعوام" }, preparse: function(e3) {
        return e3.replace(/[١٢٣٤٥٦٧٨٩٠]/g, function(e4) {
          return _[e4];
        }).replace(/،/g, ",");
      }, postformat: function(e3) {
        return e3.replace(/\d/g, function(e4) {
          return d5[e4];
        }).replace(/,/g, "،");
      }, ordinal: function(e3) {
        return e3;
      }, formats: { LT: "HH:mm", LTS: "HH:mm:ss", L: "D/‏M/‏YYYY", LL: "D MMMM YYYY", LLL: "D MMMM YYYY HH:mm", LLLL: "dddd D MMMM YYYY HH:mm" } };
      return n4.default.locale(o, null, true), o;
    });
  }
});

// ../node_modules/dayjs/plugin/localizedFormat.js
var require_localizedFormat = __commonJS({
  "../node_modules/dayjs/plugin/localizedFormat.js"(exports, module) {
    !function(e2, t2) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t2() : "function" == typeof define && define.amd ? define(t2) : (e2 = "undefined" != typeof globalThis ? globalThis : e2 || self).dayjs_plugin_localizedFormat = t2();
    }(exports, function() {
      "use strict";
      var e2 = { LTS: "h:mm:ss A", LT: "h:mm A", L: "MM/DD/YYYY", LL: "MMMM D, YYYY", LLL: "MMMM D, YYYY h:mm A", LLLL: "dddd, MMMM D, YYYY h:mm A" };
      return function(t2, o, n4) {
        var r2 = o.prototype, i3 = r2.format;
        n4.en.formats = e2, r2.format = function(t3) {
          void 0 === t3 && (t3 = "YYYY-MM-DDTHH:mm:ssZ");
          var o2 = this.$locale().formats, n5 = function(t4, o3) {
            return t4.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g, function(t5, n6, r3) {
              var i4 = r3 && r3.toUpperCase();
              return n6 || o3[r3] || e2[r3] || o3[i4].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g, function(e3, t6, o4) {
                return t6 || o4.slice(1);
              });
            });
          }(t3, void 0 === o2 ? {} : o2);
          return i3.call(this, n5);
        };
      };
    });
  }
});

// ../node_modules/dayjs/plugin/preParsePostFormat.js
var require_preParsePostFormat = __commonJS({
  "../node_modules/dayjs/plugin/preParsePostFormat.js"(exports, module) {
    !function(t2, e2) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e2() : "function" == typeof define && define.amd ? define(e2) : (t2 = "undefined" != typeof globalThis ? globalThis : t2 || self).dayjs_plugin_preParsePostFormat = e2();
    }(exports, function() {
      "use strict";
      return function(t2, e2) {
        var o = e2.prototype.parse;
        e2.prototype.parse = function(t3) {
          if ("string" == typeof t3.date) {
            var e3 = this.$locale();
            t3.date = e3 && e3.preparse ? e3.preparse(t3.date) : t3.date;
          }
          return o.bind(this)(t3);
        };
        var r2 = e2.prototype.format;
        e2.prototype.format = function() {
          for (var t3 = arguments.length, e3 = new Array(t3), o2 = 0; o2 < t3; o2++) e3[o2] = arguments[o2];
          var a6 = r2.call.apply(r2, [this].concat(e3)), p5 = this.$locale();
          return p5 && p5.postformat ? p5.postformat(a6) : a6;
        };
        var a5 = e2.prototype.fromToBase;
        a5 && (e2.prototype.fromToBase = function(t3, e3, o2, r3) {
          var p5 = this.$locale() || o2.$locale();
          return a5.call(this, t3, e3, o2, r3, p5 && p5.postformat);
        });
      };
    });
  }
});

// ../node_modules/dayjs/plugin/updateLocale.js
var require_updateLocale = __commonJS({
  "../node_modules/dayjs/plugin/updateLocale.js"(exports, module) {
    !function(e2, n4) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = n4() : "function" == typeof define && define.amd ? define(n4) : (e2 = "undefined" != typeof globalThis ? globalThis : e2 || self).dayjs_plugin_updateLocale = n4();
    }(exports, function() {
      "use strict";
      return function(e2, n4, t2) {
        t2.updateLocale = function(e3, n5) {
          var o = t2.Ls[e3];
          if (o) return (n5 ? Object.keys(n5) : []).forEach(function(e4) {
            o[e4] = n5[e4];
          }), o;
        };
      };
    });
  }
});

// ../node_modules/@euroland/format-number/dist/number-format.js
var require_number_format = __commonJS({
  "../node_modules/@euroland/format-number/dist/number-format.js"(exports, module) {
    function _extends() {
      _extends = Object.assign ? Object.assign.bind() : function(target) {
        for (var i3 = 1; i3 < arguments.length; i3++) {
          var source = arguments[i3];
          for (var key in source) {
            if (Object.prototype.hasOwnProperty.call(source, key)) {
              target[key] = source[key];
            }
          }
        }
        return target;
      };
      return _extends.apply(this, arguments);
    }
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports) : typeof define === "function" && define.amd ? define(["exports"], factory) : (global = global || self, factory(global.numberFormat = {}));
    })(exports, function(exports2) {
      "use strict";
      function formatNumber() {
        const validNagativeFormatPattern = ["(n)", "-n", "- n", "n-", "n -"];
        const validPositiveFormatPattern = ["n", "+n", "+ n", "n+", "n +"];
        let _default = {
          groupingSeparator: ",",
          decimalSeparator: ".",
          format: "#,##0.0#",
          nagativeFormatPattern: "-n",
          positiveFormatPattern: "n",
          useLatinNumbers: true
        };
        const ORIGINAL_GROUPING_SEPARATOR = ",", ORIGINAL_DECIMAL_SEPARATOR = ".";
        const arabicSymbolMap = {
          1: "١",
          2: "٢",
          3: "٣",
          4: "٤",
          5: "٥",
          6: "٦",
          7: "٧",
          8: "٨",
          9: "٩",
          0: "٠"
        };
        const arabicNumberMap = {
          "١": "1",
          "٢": "2",
          "٣": "3",
          "٤": "4",
          "٥": "5",
          "٦": "6",
          "٧": "7",
          "٨": "8",
          "٩": "9",
          "٠": "0"
        };
        const numberToArabic2 = (number2) => {
          return number2.toString().replace(/\d/g, (match) => arabicSymbolMap[match]);
        };
        const arabicToNumber2 = (string) => {
          return string.replace(/[١٢٣٤٥٦٧٨٩٠]/g, (match) => arabicNumberMap[match]);
        };
        function internalFormat(value, format, options = {}) {
          const _options = _extends({}, _default, options);
          var nagativeFormatPattern = "-n", positiveFormatPattern = "n", groupingSeparator, groupingIndex, decimalSeparator, decimalIndex, roundFactor, result, i3;
          if (validNagativeFormatPattern.includes(_options.nagativeFormatPattern)) {
            nagativeFormatPattern = _options.nagativeFormatPattern;
          }
          if (validPositiveFormatPattern.includes(_options.positiveFormatPattern)) {
            positiveFormatPattern = _options.positiveFormatPattern;
          }
          if (typeof value === "string") {
            groupingSeparator = _options.groupingSeparator;
            decimalSeparator = _options.decimalSeparator;
            decimalIndex = value.indexOf(decimalSeparator);
            roundFactor = 1;
            if (decimalIndex !== -1) {
              roundFactor = Math.pow(10, value.length - decimalIndex - 1);
            }
            value = value.replace(new RegExp("[" + groupingSeparator + "]", "g"), "");
            value = value.replace(new RegExp("[" + decimalSeparator + "]"), ".");
            return Math.round(value * roundFactor) / roundFactor;
          } else {
            if (typeof format === "undefined" || format.length < 1) {
              format = _options.format;
            }
            groupingSeparator = ORIGINAL_GROUPING_SEPARATOR;
            decimalSeparator = ORIGINAL_DECIMAL_SEPARATOR;
            groupingIndex = format.lastIndexOf(groupingSeparator);
            decimalIndex = format.indexOf(decimalSeparator);
            var integer = "", fraction = "", negative = value < 0, positive = value > 0, minFraction = format.substring(decimalIndex + 1).replace(/#/g, "").length, maxFraction = format.substring(decimalIndex + 1).length, powFraction = 10;
            value = Math.abs(value);
            if (decimalIndex !== -1) {
              fraction = _options.decimalSeparator;
              if (maxFraction > 0) {
                roundFactor = 1e3;
                powFraction = Math.pow(powFraction, maxFraction);
                var tempRound = Math.round(parseInt(value * powFraction * roundFactor - Math.round(value) * powFraction * roundFactor, 10) / roundFactor), tempFraction = String(tempRound < 0 ? Math.round(parseInt(value * powFraction * roundFactor - parseInt(value, 10) * powFraction * roundFactor, 10) / roundFactor) : tempRound), parts = value.toString().split(".");
                if (typeof parts[1] !== "undefined") {
                  for (i3 = 0; i3 < maxFraction; i3++) {
                    if (parts[1].substring(i3, i3 + 1) == "0" && i3 < maxFraction - 1 && tempFraction.length != maxFraction) {
                      tempFraction = "0" + tempFraction;
                    } else {
                      break;
                    }
                  }
                }
                for (i3 = 0; i3 < maxFraction - fraction.length; i3++) {
                  tempFraction += "0";
                }
                var symbol, formattedFraction = "";
                for (i3 = 0; i3 < tempFraction.length; i3++) {
                  symbol = tempFraction.substring(i3, i3 + 1);
                  if (i3 >= minFraction && symbol == "0" && /^0*$/.test(tempFraction.substring(i3 + 1))) {
                    break;
                  }
                  formattedFraction += symbol;
                }
                fraction += formattedFraction;
              }
              if (fraction === _options.decimalSeparator) {
                fraction = "";
              }
            }
            if (decimalIndex !== 0) {
              if (fraction !== "") {
                integer = String(parseInt(Math.round(value * powFraction) / powFraction, 10));
              } else {
                integer = String(Math.round(value));
              }
              var grouping = _options.groupingSeparator, groupingSize = 0;
              if (groupingIndex != -1) {
                if (decimalIndex != -1) {
                  groupingSize = decimalIndex - groupingIndex;
                } else {
                  groupingSize = format.length - groupingIndex;
                }
                groupingSize--;
              }
              if (groupingSize > 0) {
                var count = 0, formattedInteger = "";
                i3 = integer.length;
                while (i3--) {
                  if (count !== 0 && count % groupingSize === 0) {
                    formattedInteger = grouping + formattedInteger;
                  }
                  formattedInteger = integer.substring(i3, i3 + 1) + formattedInteger;
                  count++;
                }
                integer = formattedInteger;
              }
              var maxInteger, maxRegExp = /#|,/g;
              if (decimalIndex != -1) {
                maxInteger = format.substring(0, decimalIndex).replace(maxRegExp, "").length;
              } else {
                maxInteger = format.replace(maxRegExp, "").length;
              }
              var tempInteger = integer.length;
              for (i3 = tempInteger; i3 < maxInteger; i3++) {
                integer = "0" + integer;
              }
            }
            result = integer + fraction;
            if (positive) {
              return positiveFormatPattern.replace(/n/i, result);
            } else if (negative) {
              return nagativeFormatPattern.replace(/n/i, result);
            } else {
              return result;
            }
          }
        }
        return {
          /**
           *
           * @param {number|string} value Number to format
           * @param {string} format
           * @param {{groupingSeparator: string, decimalSeparator: string, format: string, nagativeFormatPattern: string}} options
           */
          number: (value, format, options = {}) => {
            return _default.useLatinNumbers || options.useLatinNumbers ? internalFormat(value, format, options) : numberToArabic2(internalFormat(value, format, options));
          },
          /**
           *
           * @param {{groupingSeparator: string, decimalSeparator: string, format: string, nagativeFormatPattern: string}} options
           */
          updateDefault: (options = {}) => {
            _default = _extends({}, _default, options);
          },
          arabicToNumber: arabicToNumber2,
          numberToArabic: numberToArabic2
        };
      }
      const formater = formatNumber();
      const number = formater.number;
      const updateDefault = formater.updateDefault;
      const arabicToNumber = formater.arabicToNumber;
      const numberToArabic = formater.numberToArabic;
      exports2.number = number;
      exports2.updateDefault = updateDefault;
      exports2.arabicToNumber = arabicToNumber;
      exports2.numberToArabic = numberToArabic;
      Object.defineProperty(exports2, "__esModule", {
        value: true
      });
    });
  }
});

// ../node_modules/@euroland/libs/dist/utils/index.es.js
function h(...e2) {
  return e2.reduce((n4, t2) => {
    if (t2 && typeof t2 == "string")
      n4.push(t2);
    else if ((t2 == null ? void 0 : t2.constructor) === Object)
      for (const r2 in t2)
        t2[r2] && n4.push(r2);
    return n4;
  }, []).join(" ").trim();
}
function p(e2) {
  return e2 = e2.replace("on", ""), e2 = e2.toLowerCase().trim(), e2;
}
function y(e2, n4 = 20) {
  let t2 = false, r2 = null;
  return {
    onPointerDown: (o) => {
      r2 = {
        pageX: o.pageX,
        pageY: o.pageY
      }, t2 = true;
    },
    onPointerMove: (o) => {
      r2 && (Math.abs(r2.pageX - o.pageX) > n4 || Math.abs(r2.pageY - o.pageY) > n4) && (t2 = false);
    },
    onClick: (o) => {
      r2 = null, t2 && (t2 = false, e2(o));
    }
  };
}
function a(e2, n4, t2) {
  if (n4 === 0) return 0;
  const r2 = n4 / t2 * 100;
  return (100 - r2) / 100 * e2 + r2;
}
function m(e2, n4) {
  return e2.replace(/\{([^{}]+)\}/gi, function(t2, r2) {
    return r2 in n4 ? n4[r2] : t2;
  });
}
function w(e2) {
  return JSON.stringify(e2).replace(/"([^"]+)":/g, "$1:").replace(/\uFFFF/g, '\\"');
}
function b(e2) {
  return e2.constructor === Object && Object.keys(e2).length === 0;
}
function k(e2, n4) {
  return e2 = e2 ? typeof e2 == "number" ? new Date(e2) : e2 : /* @__PURE__ */ new Date(), n4 = n4 ? typeof n4 == "number" ? new Date(n4) : n4 : /* @__PURE__ */ new Date(), e2.getFullYear() === n4.getFullYear() && e2.getMonth() === n4.getMonth() && e2.getDate() === n4.getDate();
}
function j(e2 = {}, n4 = {}) {
  const t2 = Object.keys(e2), r2 = Object.keys(n4);
  if (t2.length !== r2.length) return false;
  for (let o of t2)
    if (!(o in n4)) return false;
  return true;
}
var d = (e2) => {
  const n4 = {};
  for (const [t2, r2] of Object.entries(e2)) {
    if (t2 === r2) throw new Error(`duplicate key ${r2}`);
    if (t2 in n4) throw new Error(`duplicate key ${t2}`);
    if (r2 in n4) throw new Error(`duplicate key ${r2}`);
    n4[t2] = r2, n4[r2] = t2;
  }
  return n4;
};
function O(e2) {
  for (var n4 = "", t2 = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", r2 = t2.length, o = 0; o < e2; o++)
    n4 += t2.charAt(Math.floor(Math.random() * r2));
  return n4;
}
var u = (e2) => (e2 == null ? void 0 : e2.constructor) === Object;
var f = (e2, n4) => {
  const t2 = Array.isArray(e2), r2 = Array.isArray(n4);
  if (t2 && r2) {
    const o = [];
    for (let i3 in e2)
      i3 <= n4.length - 1 ? o.push(f(e2[i3], n4[i3])) : o.push(e2[i3]);
    for (; o.length < n4.length; )
      o.push(n4[o.length]);
    return o;
  }
  if (u(e2) && u(n4)) {
    const o = {};
    for (let i3 in e2)
      i3 in n4 ? o[i3] = f(e2[i3], n4[i3]) : o[i3] = e2[i3];
    for (let i3 in n4)
      i3 in e2 || (o[i3] = n4[i3]);
    return o;
  }
  return n4;
};
var E = (e2) => (window.addEventListener("resize", e2), () => window.removeEventListener("resize", e2));
function g(e2) {
  return Object.prototype.toString.call(e2) === "[object String]";
}
function M(e2 = [], n4 = null, t2) {
  const r2 = e2.slice(0), o = /^https?:\/\//i;
  function i3(c3) {
    var l2 = document.createElement("link");
    l2.href = c3, l2.rel = "stylesheet", l2.type = "text/css", (document.head || document.documentElement).appendChild(l2);
  }
  if (g(n4) && n4.length) {
    let c3 = r2.length > 0 ? r2.length - 1 : 0;
    o.test(n4) ? r2[c3] = n4 : r2[c3] = `${t2}${n4}`;
  }
  let s2 = 0;
  for (; s2 < r2.length; )
    i3(r2[s2++]);
}
var P = () => (Math.random() + 1).toString(36).substring(2);
function A(e2 = {}, n4 = (t2) => t2 !== null && typeof t2 < "u") {
  const t2 = {};
  for (const r2 in e2)
    Object.hasOwnProperty.call(e2, r2) && n4(e2[r2]) && (t2[r2] = e2[r2]);
  return t2;
}
function F(e2) {
  return ["(n)", "-n", "- n", "n-", "n -"].includes(e2);
}
function $(e2, ...n4) {
  const t2 = n4.map((s2) => ({ ...s2.common, ...s2[e2] })).reduce((s2, c3) => (s2 = { ...s2, ...c3 }, s2), {}), r2 = () => Object.keys(t2).reduce((s2, c3) => s2 + `${c3}:${t2[c3]};
`, ""), o = () => {
    const s2 = document.createElement("style");
    return s2.innerHTML = `
        :root {
          ${r2()}
        }
      `, s2;
  };
  return {
    result: t2,
    toString: r2,
    toStyleElement: o,
    injectTo: (s2, c3 = false) => {
      c3 ? s2.insertBefore(o(), s2.firstChild) : s2.appendChild(o());
    }
  };
}

// ../node_modules/@euroland/libs/dist/utils/dayjs.es.js
var import_utc = __toESM(require_utc());
var import_timezone = __toESM(require_timezone());
var import_dayjs = __toESM(require_dayjs_min());
var import_duration = __toESM(require_duration());
var import_ar = __toESM(require_ar());
var import_localizedFormat = __toESM(require_localizedFormat());
var import_preParsePostFormat = __toESM(require_preParsePostFormat());
var import_updateLocale = __toESM(require_updateLocale());
var t = import_dayjs.default;
t.extend(import_utc.default);
t.extend(import_timezone.default);
t.extend(import_duration.default);
t.extend(import_localizedFormat.default);
t.extend(import_preParsePostFormat.default);
t.extend(import_updateLocale.default);
try {
  const e2 = (
    /** @type { DateNameSettings } */
    JSON.parse(window.appSettings.dateNames)
  );
  t.updateLocale("en", {
    weekdays: e2.dayNames,
    weekdaysShort: e2.shortDayNames,
    weekdaysMin: e2.shortestDayNames,
    months: e2.monthNames,
    monthsShort: e2.shortMonthNames,
    meridiem: (r2, h3, o) => r2 > 12 ? o ? e2.period[1] : e2.period[3] ?? e2.period[1] : o ? e2.period[0] : e2.period[2] ?? e2.period[0]
  }), t.locale("en");
} catch {
}
var S = t;

// ../node_modules/@euroland/libs/dist/utils/pluginArabicNumber.es.js
var import_format_number = __toESM(require_number_format());
function u2(c3, r2) {
  const p5 = r2.prototype.parse;
  r2.prototype.parse = function(t2) {
    if (typeof t2.date == "string") {
      const o = this.$locale();
      t2.date = o && o.preparse ? o.preparse(t2.date) : (0, import_format_number.arabicToNumber)(t2.date);
    }
    return p5.bind(this)(t2);
  };
  const a5 = r2.prototype.format;
  r2.prototype.format = function(...t2) {
    const o = a5.call(this, ...t2), e2 = this.$locale();
    return e2 && e2.postformat ? e2.postformat(o) : (0, import_format_number.numberToArabic)(o);
  };
}

// ../node_modules/@euroland/libs/dist/utils/formatDate.es.js
function f2() {
  let a5 = {
    timeZone: "",
    settingFormat: {},
    useLatinNumbers: true
  };
  function r2(t2) {
    t2 = t2 || /* @__PURE__ */ new Date();
    let e2 = a5.timeZone;
    return S.utc(t2).tz(e2);
  }
  function m3(t2, e2) {
    let i3 = e2 in a5.settingFormat ? a5.settingFormat[e2] : e2;
    return r2(t2).format(i3);
  }
  return {
    /**
     *
     * @param {number|string|Date} value 
     * @param {string} typeFormat
     */
    formatDate: (t2, e2) => m3(t2, e2),
    /**
     *
     * @param {{timeZone: string, }} options
     */
    updateDefault: async (t2 = {}) => {
      a5 = Object.assign({}, a5, t2), a5.useLatinNumbers || S.extend(u2);
    },
    getDate: r2
  };
}
var n3 = f2();
var c = n3.formatDate;
var g2 = n3.updateDefault;
var d3 = n3.getDate;

// ../node_modules/@euroland/libs/dist/utils/debounce.es.js
function C(o) {
  const e2 = typeof o;
  return o != null && (e2 === "object" || e2 === "function");
}
function D(o, e2, c3) {
  let a5, l2, m3, f5, t2, r2, s2 = 0, k2 = false, d5 = false, T = true;
  const p5 = !e2 && e2 !== 0 && typeof requestAnimationFrame == "function";
  if (typeof o != "function")
    throw new TypeError("Expected a function");
  e2 = +e2 || 0, C(c3) && (k2 = !!c3.leading, d5 = "maxWait" in c3, m3 = d5 ? Math.max(+c3.maxWait || 0, e2) : m3, T = "trailing" in c3 ? !!c3.trailing : T);
  function A2(n4) {
    const i3 = a5, u5 = l2;
    return a5 = l2 = void 0, s2 = n4, f5 = o.apply(u5, i3), f5;
  }
  function g4(n4, i3) {
    return p5 ? (cancelAnimationFrame(t2), requestAnimationFrame(n4)) : setTimeout(n4, i3);
  }
  function E2(n4) {
    if (p5)
      return cancelAnimationFrame(n4);
    clearTimeout(n4);
  }
  function F2(n4) {
    return s2 = n4, t2 = g4(h3, e2), k2 ? A2(n4) : f5;
  }
  function W(n4) {
    const i3 = n4 - r2, u5 = n4 - s2, v2 = e2 - i3;
    return d5 ? Math.min(v2, m3 - u5) : v2;
  }
  function y2(n4) {
    const i3 = n4 - r2, u5 = n4 - s2;
    return r2 === void 0 || i3 >= e2 || i3 < 0 || d5 && u5 >= m3;
  }
  function h3() {
    const n4 = Date.now();
    if (y2(n4))
      return I(n4);
    t2 = g4(h3, W(n4));
  }
  function I(n4) {
    return t2 = void 0, T && a5 ? A2(n4) : (a5 = l2 = void 0, f5);
  }
  function b2() {
    t2 !== void 0 && E2(t2), s2 = 0, a5 = r2 = l2 = t2 = void 0;
  }
  function L() {
    return t2 === void 0 ? f5 : I(Date.now());
  }
  function S2() {
    return t2 !== void 0;
  }
  function x(...n4) {
    const i3 = Date.now(), u5 = y2(i3);
    if (a5 = n4, l2 = this, r2 = i3, u5) {
      if (t2 === void 0)
        return F2(r2);
      if (d5)
        return t2 = g4(h3, e2), A2(r2);
    }
    return t2 === void 0 && (t2 = g4(h3, e2)), f5;
  }
  return x.cancel = b2, x.flush = L, x.pending = S2, x;
}

// ../node_modules/@euroland/libs/dist/utils/throttle.es.js
function g3(f5, s2, l2) {
  var a5, r2, n4, e2 = null, t2 = 0;
  l2 || (l2 = {});
  var v2 = function() {
    t2 = l2.leading === false ? 0 : Date.now(), e2 = null, n4 = f5.apply(a5, r2), e2 || (a5 = r2 = null);
  };
  return function() {
    var i3 = Date.now();
    !t2 && l2.leading === false && (t2 = i3);
    var u5 = s2 - (i3 - t2);
    return a5 = this, r2 = arguments, u5 <= 0 || u5 > s2 ? (e2 && (clearTimeout(e2), e2 = null), t2 = i3, n4 = f5.apply(a5, r2), e2 || (a5 = r2 = null)) : !e2 && l2.trailing !== false && (e2 = setTimeout(v2, u5)), n4;
  };
}

// ../node_modules/@euroland/libs/dist/utils/tiny-emitter.es.js
var v = class {
  on(r2, s2, e2) {
    var t2 = this.e || (this.e = {});
    return (t2[r2] || (t2[r2] = [])).push({
      fn: s2,
      ctx: e2
    }), this;
  }
  once(r2, s2, e2) {
    var t2 = this;
    function i3() {
      t2.off(r2, i3), s2.apply(e2, arguments);
    }
    return i3._ = s2, this.on(r2, i3, e2);
  }
  emit(r2) {
    var s2 = [].slice.call(arguments, 1), e2 = ((this.e || (this.e = {}))[r2] || []).slice(), t2 = 0, i3 = e2.length;
    for (t2; t2 < i3; t2++)
      e2[t2].fn.apply(e2[t2].ctx, s2);
    return this;
  }
  off(r2, s2) {
    var e2 = this.e || (this.e = {}), t2 = e2[r2], i3 = [];
    if (t2 && s2)
      for (var n4 = 0, h3 = t2.length; n4 < h3; n4++)
        t2[n4].fn !== s2 && t2[n4].fn._ !== s2 && i3.push(t2[n4]);
    return i3.length ? e2[r2] = i3 : delete e2[r2], this;
  }
};

// ../node_modules/@euroland/libs/dist/utils/document-ready.es.js
function f3(n4) {
  return Object.prototype.toString.call(n4) === "[object Function]";
}
function c2(n4, t2) {
  if (t2 === void 0 && (t2 = 1), t2 >= 3) return "stringifyError stack overflow";
  try {
    if (!n4) return `<unknown error: ${{}.toString.call(n4)}>`;
    if (typeof n4 == "string") return n4;
    if (n4 instanceof Error) {
      var o = n4 && n4.stack, i3 = n4 && n4.message;
      if (o && i3) return o.indexOf(i3) !== -1 ? o : i3 + `
` + o;
      if (o) return o;
      if (i3) return i3;
    }
    return n4 && n4.toString && typeof n4.toString == "function" ? n4.toString() : {}.toString.call(n4);
  } catch (u5) {
    return "Error while stringifying error: " + c2(u5, t2 + 1);
  }
}
function a3() {
  return new Promise((n4) => {
    if (r() || e())
      n4();
    else
      var t2 = setInterval(() => {
        (r() || e()) && (clearInterval(t2), n4());
      }, 10);
  });
}
function d4() {
  return new Promise((n4) => {
    r() && n4(), window.addEventListener("load", () => n4());
  });
}
function r() {
  return !!document.body && document.readyState === "complete";
}
function e() {
  return !!document.body && document.readyState === "interactive";
}

// ../node_modules/@euroland/libs/dist/utils/url-join.es.js
function p3(e2) {
  const l2 = [];
  if (e2.length === 0)
    return "";
  if (e2 = e2.filter((i3) => i3 !== ""), typeof e2[0] != "string")
    throw new TypeError("Url must be a string. Received " + e2[0]);
  e2[0].match(/^[^/:]+:\/*$/) && e2.length > 1 && (e2[0] = e2.shift() + e2[0]), e2[0] === "/" && e2.length > 1 && (e2[0] = e2.shift() + e2[0]), e2[0].match(/^file:\/\/\//) ? e2[0] = e2[0].replace(/^([^/:]+):\/*/, "$1:///") : e2[0].match(/^\[.*:.*\]/) || (e2[0] = e2[0].replace(/^([^/:]+):\/*/, "$1://"));
  for (let i3 = 0; i3 < e2.length; i3++) {
    let n4 = e2[i3];
    if (typeof n4 != "string")
      throw new TypeError("Url must be a string. Received " + n4);
    i3 > 0 && (n4 = n4.replace(/^[/]+/, "")), i3 < e2.length - 1 ? n4 = n4.replace(/[/]+$/, "") : n4 = n4.replace(/[/]+$/, "/"), n4 !== "" && l2.push(n4);
  }
  let t2 = "";
  for (let i3 = 0; i3 < l2.length; i3++) {
    const n4 = l2[i3];
    if (i3 === 0) {
      t2 += n4;
      continue;
    }
    const c3 = l2[i3 - 1];
    if (c3 && c3.endsWith("?") || c3.endsWith("#")) {
      t2 += n4;
      continue;
    }
    t2 += "/" + n4;
  }
  t2 = t2.replace(/\/(\?|&|#[^!])/g, "$1");
  const [h3, o] = t2.split("#"), f5 = h3.split(/(?:\?|&)+/).filter(Boolean);
  return t2 = f5.shift() + (f5.length > 0 ? "?" : "") + f5.join("&") + (o && o.length > 0 ? "#" + o : ""), t2;
}
function u3(...e2) {
  const l2 = Array.from(Array.isArray(e2[0]) ? e2[0] : e2);
  return p3(l2);
}

// ../node_modules/@euroland/libs/dist/locale/i18n.es.js
var a4 = function() {
  function n4() {
    this.locale = "en-gb", this.translations = {}, this.customPhrases = {};
  }
  return n4.prototype.load = function(t2, e2, s2 = () => {
  }) {
    return this._request(e2).then((r2) => (this.locale = t2, this.translations = r2, s2 && s2("success"), Promise.resolve(r2))).catch((r2) => (s2("error", r2.message), Promise.reject(r2.message)));
  }, n4.prototype.find = function(t2) {
    const e2 = arguments.length;
    if (this._hasKey(this.translations, t2)) {
      let s2 = this.translations[t2];
      const r2 = this;
      if (e2 <= 1)
        return s2;
      const o = Array.prototype.slice.call(arguments, 1);
      return o.length === 1 && this._isObject(o[0]) ? s2.replace(/\{([^{}]+)\}/, function(c3, i3) {
        return r2._hasKey(o[0], i3) ? o[0][i3] : c3;
      }) : s2.replace(/\{(\d+)\}/g, function(c3, i3) {
        return i3 * 1 < o.length ? o[i3 * 1] : c3;
      });
    }
  }, n4.prototype.setCustomPhrases = function(t2 = {}) {
    this.customPhrases = Object.assign({}, this.customPhrases, t2), Object.entries(this.customPhrases).forEach(([e2, s2]) => {
      this._hasKey(this.translations, e2) && (this.translations[e2] = s2);
    });
  }, n4.prototype._hasKey = function(t2, e2) {
    return e2 in t2 && Object.prototype.toString.call(t2[e2]) === "[object String]";
  }, n4.prototype._isString = function(t2) {
    return Object.prototype.toString.call(t2) === "[object String]";
  }, n4.prototype._isObject = function(t2) {
    return Object.prototype.toString.call(t2) === "[object Object]";
  }, n4.prototype._request = function(t2) {
    return Object.prototype.toString.call(t2) === "[object Object]" ? new Promise((e2) => {
      e2(t2);
    }) : fetch(`${t2}`).then((e2) => e2.json());
  }, new n4();
}();
var u4 = function(n4, t2, e2) {
  return a4.load(n4, t2, e2);
};
var h2 = function(n4 = {}) {
  a4.setCustomPhrases(n4);
};
var l = function() {
  return a4.find(...arguments);
};
var f4 = { load: u4, setCustomPhrases: h2, translate: l };
var p4 = f4;
export {
  v as TinyEmitter,
  b as checkEmptyObject,
  h as classNames,
  y as clickWithoutMove,
  k as compareDate,
  j as compareSameKeyObject,
  w as convertToJson,
  S as dayjs,
  D as debounce,
  c as formatDate,
  d3 as getDate,
  P as getRandomString,
  p as getRealEventName,
  p4 as i18n,
  e as isDocumentInteractive,
  r as isDocumentReady,
  f3 as isFunction,
  u as isObject,
  g as isString,
  F as isValidNegativeNumberFormat,
  M as loadCustomStylesheet,
  f as mergeObject,
  d as objectToEnum,
  E as observerWindowResize,
  A as pickBy,
  u2 as pluginArabicNumber,
  m as replaceKey,
  $ as setVariableByTemplate,
  a as softPercent,
  O as stringId,
  c2 as stringifyError,
  g3 as throttle,
  g2 as updateDefaultDateTime,
  u3 as urlJoin,
  a3 as waitForDocumentReady,
  d4 as waitForWindowReady
};
//# sourceMappingURL=@euroland_libs.js.map
