import { OhlcData } from 'lightweight-charts';

export interface OHLCVSimple extends OhlcData {
    volume: number;
}
export interface OHLCVData extends Omit<OHLCVSimple, 'customValues'> {
    customValues: OHLCVSimple;
}
export interface OHLCVExtraData extends OHLCVData {
    value: number;
}
export interface Destroyable {
    destroy(): void;
}
export type ArrayType<T> = T extends (infer U)[] ? U : never;
