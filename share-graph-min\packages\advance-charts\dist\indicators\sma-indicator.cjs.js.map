{"version": 3, "file": "sma-indicator.cjs.js", "sources": ["../../src/indicators/sma-indicator.ts"], "sourcesContent": ["import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from \"lightweight-charts\";\r\nimport {SMA} from \"technicalindicators\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {SeriesPrimitiveBase} from \"../custom-primitive/primitive-base\";\r\nimport {LineData, LinePrimitivePaneView} from \"../custom-primitive/pane-view/line\";\r\nimport {Context, IIndicatorBar} from \"../helpers/execution-indicator\";\r\n\r\nexport interface SMAIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  period: number\r\n}\r\n\r\nexport const defaultOptions: SMAIndicatorOptions = {\r\n  color: \"#2962ff\",\r\n  period: 9,\r\n  overlay: true\r\n}\r\n\r\nexport class SMAPrimitive extends SeriesPrimitiveBase<\r\n  SingleValueData | WhitespaceData\r\n> {\r\n  linePrimitive: LinePrimitivePaneView;\r\n  constructor(protected source: SMAIndicator) {\r\n    super();\r\n    this.linePrimitive = new LinePrimitivePaneView({\r\n      lineColor: this.source.options.color,\r\n    });\r\n    this._paneViews = [this.linePrimitive];\r\n  }\r\n\r\n  update(indicatorBars: IIndicatorBar<SMAData>[]) {\r\n    const lineData: LineData[] = []\r\n    for(const bar of indicatorBars) {\r\n      const value = bar.value\r\n      if(value) lineData.push({time: bar.time as Time, price: value[0]})\r\n    }\r\n\r\n    this.linePrimitive.update(lineData);\r\n  }\r\n}\r\n\r\nexport type SMAData = readonly [Nominal<number, 'SMA'>]\r\n\r\nexport default class SMAIndicator extends ChartIndicator<SMAIndicatorOptions, SMAData> {\r\n  smaPrimitive = new SMAPrimitive(this)\r\n  getDefaultOptions(): SMAIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n\r\n  _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {\r\n    series.attachPrimitive(this.smaPrimitive)\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove();\r\n    this.mainSeries?.detachPrimitive(this.smaPrimitive)\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n    this.smaPrimitive.update(\r\n      this._executionContext.data\r\n    )\r\n  }\r\n\r\n  formula(c: Context) {\r\n    const smaSeries = c.new_var(c.symbol.close, this.options.period)\r\n    if(!smaSeries.calculable()) return;\r\n    const sma = new SMA({\r\n      values: smaSeries.getAll(),\r\n      period: this.options.period\r\n    });\r\n\r\n    return sma.getResult()\r\n  }\r\n}"], "names": ["defaultOptions", "SMAPrimitive", "SeriesPrimitiveBase", "source", "__publicField", "LinePrimitivePaneView", "indicatorBars", "lineData", "bar", "value", "SMAIndicator", "ChartIndicator", "series", "_a", "c", "smaSeries", "SMA"], "mappings": "8cAYaA,EAAsC,CACjD,MAAO,UACP,OAAQ,EACR,QAAS,EACX,EAEO,MAAMC,UAAqBC,EAAAA,mBAEhC,CAEA,YAAsBC,EAAsB,CACpC,MAAA,EAFRC,EAAA,sBACsB,KAAA,OAAAD,EAEf,KAAA,cAAgB,IAAIE,wBAAsB,CAC7C,UAAW,KAAK,OAAO,QAAQ,KAAA,CAChC,EACI,KAAA,WAAa,CAAC,KAAK,aAAa,CAAA,CAGvC,OAAOC,EAAyC,CAC9C,MAAMC,EAAuB,CAAC,EAC9B,UAAUC,KAAOF,EAAe,CAC9B,MAAMG,EAAQD,EAAI,MACfC,GAAgBF,EAAA,KAAK,CAAC,KAAMC,EAAI,KAAc,MAAOC,EAAM,CAAC,CAAA,CAAE,CAAA,CAG9D,KAAA,cAAc,OAAOF,CAAQ,CAAA,CAEtC,CAIA,MAAqBG,UAAqBC,EAAAA,cAA6C,CAAvF,kCACEP,EAAA,oBAAe,IAAIH,EAAa,IAAI,GACpC,mBAAyC,CAChC,OAAAD,CAAA,CAGT,mBAAmBY,EAAsC,CAChDA,EAAA,gBAAgB,KAAK,YAAY,CAAA,CAG1C,QAAe,OACb,MAAM,OAAO,GACRC,EAAA,KAAA,aAAA,MAAAA,EAAY,gBAAgB,KAAK,aAAY,CAGpD,oBAA2B,CACzB,KAAK,aAAa,OAChB,KAAK,kBAAkB,IACzB,CAAA,CAGF,QAAQC,EAAY,CACZ,MAAAC,EAAYD,EAAE,QAAQA,EAAE,OAAO,MAAO,KAAK,QAAQ,MAAM,EAC5D,OAACC,EAAU,aACF,IAAIC,MAAI,CAClB,OAAQD,EAAU,OAAO,EACzB,OAAQ,KAAK,QAAQ,MAAA,CACtB,EAEU,UAAU,EANO,MAMP,CAEzB"}