{"version": 3, "file": "band.cjs.js", "sources": ["../../../src/custom-primitive/pane-view/band.ts"], "sourcesContent": ["import {CanvasRenderingTarget2D} from \"fancy-canvas\";\r\nimport {PrimitivePaneViewBase} from \"../primitive-base\";\r\nimport { Time} from \"lightweight-charts\";\r\nimport {ensureNotNull} from \"../../helpers/assertions\";\r\nimport {binarySearchIndex, timeToUnix} from \"../../helpers/utils\";\r\n\r\nexport interface BandPrimitivePaneViewOptions {\r\n  backgroundColor: string,\r\n}\r\n\r\nexport const BandPrimitiveOptionsDefault: BandPrimitivePaneViewOptions = {\r\n  backgroundColor: '#2196f31a',\r\n}\r\n\r\nexport type BandPrimitiveData = {\r\n  time: Time, \r\n  upper: number, \r\n  lower: number\r\n}\r\n\r\nexport class BandPrimitivePaneView extends PrimitivePaneViewBase<BandPrimitivePaneViewOptions, BandPrimitiveData> {\r\n\r\n  dataVisible() {\r\n    const visibleRange = this.getVisibleRange()\r\n    if(!visibleRange) return [];\r\n\r\n    const data = this.data;\r\n    let fromIndex = binarySearchIndex(data, timeToUnix(visibleRange.from), item => timeToUnix(item.time))\r\n    if(fromIndex === -1) fromIndex = 1\r\n    let toIndex = binarySearchIndex(data, timeToUnix(visibleRange.to), item => timeToUnix(item.time))\r\n    if(toIndex === -1) toIndex = data.length\r\n    return data.slice(fromIndex - 1, toIndex + 2)\r\n  }\r\n  drawBackground(target: CanvasRenderingTarget2D): void {\r\n    const data = this.dataVisible().map((item) => ({\r\n      x: ensureNotNull(this.timeToCoordinate(item.time)),\r\n      upperCoor: ensureNotNull(this.priceToCoordinate(item.upper)),\r\n      lowerCoor: ensureNotNull(this.priceToCoordinate(item.lower)),\r\n    }));\r\n\r\n    if(data.length < 2) return;\r\n    \r\n    target.useBitmapCoordinateSpace(scope => {\r\n      const ctx = scope.context;\r\n      ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);\r\n\r\n      ctx.beginPath();\r\n      const region = new Path2D();\r\n      const from = 0;\r\n      const to = data.length\r\n      const first = data[from];\r\n      region.moveTo(\r\n        first.x, \r\n        first.upperCoor\r\n      );\r\n\r\n      for (let i = from + 1; i < to; i++) {\r\n        const point = data[i];\r\n        region.lineTo(\r\n          point.x,\r\n          point.upperCoor\r\n        );\r\n      }\r\n\r\n      for (let i = to - 1; i >= from; i--) {\r\n        const point = data[i];\r\n        region.lineTo(point.x, point.lowerCoor);\r\n      }\r\n\r\n      region.lineTo(\r\n        first.x, \r\n        first.lowerCoor\r\n      );\r\n      region.closePath();\r\n      ctx.fillStyle = this.options.backgroundColor\r\n      ctx.fill(region)\r\n    })\r\n  }\r\n  defaultOptions(): BandPrimitivePaneViewOptions {\r\n    return BandPrimitiveOptionsDefault\r\n  }\r\n}"], "names": ["BandPrimitiveOptionsDefault", "BandPrimitivePaneView", "PrimitivePaneViewBase", "visibleRange", "data", "fromIndex", "binarySearchIndex", "timeToUnix", "item", "toIndex", "target", "ensureNotNull", "scope", "ctx", "region", "from", "to", "first", "i", "point"], "mappings": "iNAUaA,EAA4D,CACvE,gBAAiB,WACnB,EAQO,MAAMC,UAA8BC,EAAAA,qBAAuE,CAEhH,aAAc,CACN,MAAAC,EAAe,KAAK,gBAAgB,EACvC,GAAA,CAACA,EAAc,MAAO,CAAC,EAE1B,MAAMC,EAAO,KAAK,KACd,IAAAC,EAAYC,EAAAA,kBAAkBF,EAAMG,EAAW,WAAAJ,EAAa,IAAI,EAAWK,GAAAD,EAAAA,WAAWC,EAAK,IAAI,CAAC,EACjGH,IAAc,KAAgBA,EAAA,GAC7B,IAAAI,EAAUH,EAAAA,kBAAkBF,EAAMG,EAAW,WAAAJ,EAAa,EAAE,EAAWK,GAAAD,EAAAA,WAAWC,EAAK,IAAI,CAAC,EAC7F,OAAAC,IAAY,KAAIA,EAAUL,EAAK,QAC3BA,EAAK,MAAMC,EAAY,EAAGI,EAAU,CAAC,CAAA,CAE9C,eAAeC,EAAuC,CACpD,MAAMN,EAAO,KAAK,YAAc,EAAA,IAAKI,IAAU,CAC7C,EAAGG,EAAc,cAAA,KAAK,iBAAiBH,EAAK,IAAI,CAAC,EACjD,UAAWG,EAAc,cAAA,KAAK,kBAAkBH,EAAK,KAAK,CAAC,EAC3D,UAAWG,EAAAA,cAAc,KAAK,kBAAkBH,EAAK,KAAK,CAAC,CAAA,EAC3D,EAECJ,EAAK,OAAS,GAEjBM,EAAO,yBAAkCE,GAAA,CACvC,MAAMC,EAAMD,EAAM,QAClBC,EAAI,MAAMD,EAAM,qBAAsBA,EAAM,kBAAkB,EAE9DC,EAAI,UAAU,EACR,MAAAC,EAAS,IAAI,OACbC,EAAO,EACPC,EAAKZ,EAAK,OACVa,EAAQb,EAAKW,CAAI,EAChBD,EAAA,OACLG,EAAM,EACNA,EAAM,SACR,EAEA,QAASC,EAAIH,EAAO,EAAGG,EAAIF,EAAIE,IAAK,CAC5B,MAAAC,EAAQf,EAAKc,CAAC,EACbJ,EAAA,OACLK,EAAM,EACNA,EAAM,SACR,CAAA,CAGF,QAASD,EAAIF,EAAK,EAAGE,GAAKH,EAAMG,IAAK,CAC7B,MAAAC,EAAQf,EAAKc,CAAC,EACpBJ,EAAO,OAAOK,EAAM,EAAGA,EAAM,SAAS,CAAA,CAGjCL,EAAA,OACLG,EAAM,EACNA,EAAM,SACR,EACAH,EAAO,UAAU,EACbD,EAAA,UAAY,KAAK,QAAQ,gBAC7BA,EAAI,KAAKC,CAAM,CAAA,CAChB,CAAA,CAEH,gBAA+C,CACtC,OAAAd,CAAA,CAEX"}