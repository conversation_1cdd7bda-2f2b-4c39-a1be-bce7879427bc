{"version": 3, "file": "abstract-indicator.cjs.js", "sources": ["../../src/indicators/abstract-indicator.ts"], "sourcesContent": ["import type {<PERSON><PERSON>hart<PERSON><PERSON>, <PERSON>eries<PERSON>pi, MouseEventParams, OhlcData, SeriesDataItemTypeMap, SeriesType, SingleValueData, Time, WhitespaceData} from \"lightweight-charts\";\r\nimport {cloneDeep, merge} from \"es-toolkit\";\r\nimport {OHLCVData} from \"../interface\";\r\nimport {Delegate, IPublicDelegate} from \"../helpers/delegate\";\r\nimport {binarySearch, timeToDate} from \"../helpers/utils\";\r\nimport {NumberFormatter} from \"../helpers/number-formatter\";\r\nimport {Context, ExecutionContext, IIndicatorBar} from \"../helpers/execution-indicator\";\r\n\r\nexport const upColor = '#26a69a'; // Green for bullish candles\r\nexport const downColor = '#ef5350'; // Red for bearish candles\r\n\r\nexport type IndicatorData = OhlcData | SingleValueData\r\n\r\nexport type InputData = OhlcData | SingleValueData\r\nexport type SimpleData = WhitespaceData | SingleValueData\r\n\r\nexport interface ChartIndicatorOptions {\r\n  overlay: boolean,\r\n  upColor?: string;\r\n  downColor?: string;\r\n  numberFormatter?: () => NumberFormatter\r\n}\r\n\r\nexport abstract class ChartIndicator <\r\n  IOptions extends ChartIndicatorOptions = ChartIndicatorOptions, \r\n  IIndicatorData extends readonly number[] = number[],\r\n  IData extends OHLCVData = OHLCVData,\r\n> {\r\n  protected data: Array<IData> | null = null\r\n  options: IOptions;\r\n\r\n  mainSeries: ISeriesApi<SeriesType> | null = null\r\n\r\n  _dataHovered = new Delegate<IIndicatorBar<IIndicatorData> | undefined>()\r\n\r\n  indicatorData: Array<IIndicatorData> = []\r\n\r\n  _executionContext: ExecutionContext<IIndicatorData>\r\n\r\n  formula(c: Context): IIndicatorData | undefined\r\n  formula(): IIndicatorData | undefined {\r\n    return undefined\r\n  }\r\n\r\n  mainSeriesChanged(series: ISeriesApi<SeriesType>) {\r\n    this.mainSeries = series\r\n\r\n    this._mainSeriesChanged?.(series)\r\n  } \r\n\r\n\r\n\r\n  onCrosshairMove(param: MouseEventParams<Time>) {\r\n    if(param.time === undefined) return this._dataHovered.fire(undefined)\r\n    this._dataHovered.fire(this.dataByTime(param.time))\r\n  }\r\n\r\n  dataHovered() {\r\n    return this._dataHovered as IPublicDelegate<typeof this._dataHovered>\r\n  }\r\n\r\n  constructor(protected chart: IChartApi, options?: Partial<IOptions>) {\r\n    this.options = merge(cloneDeep(this.getDefaultOptions()), options ?? {})\r\n    this.onCrosshairMove = this.onCrosshairMove.bind(this)\r\n    this.chart.subscribeCrosshairMove(this.onCrosshairMove)\r\n\r\n    this._executionContext = new ExecutionContext<IIndicatorData>((c) => this.formula?.(c))\r\n  }\r\n\r\n  abstract getDefaultOptions(): IOptions\r\n\r\n  setData(data: Array<IData>) {\r\n    this.data = data;\r\n    this._executionContext.recalc(data.map(item => ({\r\n      open: item.open, \r\n      high: item.high, \r\n      low: item.low, \r\n      time: Math.floor(timeToDate(item.time).getTime() / 1000), \r\n      isNew: false, \r\n      volume: item.volume, \r\n      close: item.close\r\n    })))\r\n    this.calcIndicatorData();\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n  update() {\r\n    if(!this.data) return;\r\n    const lastData = this.data[this.data.length - 1];\r\n    this._executionContext.update({\r\n      open: lastData.open, \r\n      high: lastData.high, \r\n      low: lastData.low, \r\n      time: Math.floor(timeToDate(lastData.time).getTime() / 1000), \r\n      volume: lastData.volume, \r\n      close: lastData.close\r\n    })\r\n    this.recalc?.()\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n  applyOptions(options: Partial<IOptions>) {\r\n    this.options = merge(this.options, options)\r\n    this._applyOptions?.(options);\r\n  }\r\n\r\n  remove() {\r\n    if(this.onCrosshairMove) {\r\n      this.chart.unsubscribeCrosshairMove(this.onCrosshairMove)\r\n    }\r\n  }\r\n\r\n  getDataByCrosshair<TSeriesType extends SeriesType, TData extends SeriesDataItemTypeMap<Time>[TSeriesType]>({ logical }: {logical?: number}, series: ISeriesApi<TSeriesType, Time, TData>) {\r\n    if(logical !== undefined) {\r\n      return series.dataByIndex(logical) ?? undefined\r\n    }\r\n  }\r\n\r\n  dataByTime(time: Time) {\r\n    return binarySearch(this._executionContext.data, Math.floor(timeToDate(time).getTime() / 1000), item => item.time);\r\n  }\r\n\r\n  lastPoint() {\r\n    const data = this._executionContext.data;\r\n    if(data.length === 0) return;\r\n    return data[data.length - 1]\r\n  }\r\n\r\n  getData() {\r\n    return this.data\r\n  }\r\n\r\n  calcIndicatorData() {}\r\n  recalc?(): void\r\n  applyIndicatorData() {}\r\n  setPaneIndex(paneIndex: number): void\r\n  setPaneIndex() {}\r\n\r\n  getPaneIndex() {\r\n    return this.mainSeries?.getPane().paneIndex() ?? 0\r\n  }\r\n  _applyOptions?(options: Partial<IOptions>): void\r\n  _mainSeriesChanged?(series: ISeriesApi<SeriesType>): void\r\n}"], "names": ["upColor", "downColor", "ChartIndicator", "chart", "options", "__publicField", "Delegate", "merge", "cloneDeep", "ExecutionContext", "c", "_a", "series", "param", "data", "item", "timeToDate", "lastData", "logical", "time", "binarySearch"], "mappings": "kZAQaA,EAAU,UACVC,EAAY,UAclB,MAAeC,CAIpB,CAkCA,YAAsBC,EAAkBC,EAA6B,CAjC3DC,EAAA,YAA4B,MACtCA,EAAA,gBAEAA,EAAA,kBAA4C,MAE5CA,EAAA,oBAAe,IAAIC,EAAAA,UAEnBD,EAAA,qBAAuC,CAAC,GAExCA,EAAA,0BAwBsB,KAAA,MAAAF,EACf,KAAA,QAAUI,EAAAA,MAAMC,EAAU,UAAA,KAAK,kBAAmB,CAAA,EAAGJ,GAAW,EAAE,EACvE,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EAChD,KAAA,MAAM,uBAAuB,KAAK,eAAe,EAEjD,KAAA,kBAAoB,IAAIK,mBAAkCC,UAAM,OAAAC,EAAA,KAAK,UAAL,YAAAA,EAAA,UAAeD,GAAE,CAAA,CA1BxF,SAAsC,CAC7B,CAGT,kBAAkBE,EAAgC,OAChD,KAAK,WAAaA,GAElBD,EAAA,KAAK,qBAAL,MAAAA,EAAA,UAA0BC,EAAM,CAKlC,gBAAgBC,EAA+B,CAC7C,GAAGA,EAAM,OAAS,cAAkB,KAAK,aAAa,KAAK,MAAS,EACpE,KAAK,aAAa,KAAK,KAAK,WAAWA,EAAM,IAAI,CAAC,CAAA,CAGpD,aAAc,CACZ,OAAO,KAAK,YAAA,CAad,QAAQC,EAAoB,CAC1B,KAAK,KAAOA,EACZ,KAAK,kBAAkB,OAAOA,EAAK,IAAaC,IAAA,CAC9C,KAAMA,EAAK,KACX,KAAMA,EAAK,KACX,IAAKA,EAAK,IACV,KAAM,KAAK,MAAMC,EAAA,WAAWD,EAAK,IAAI,EAAE,QAAQ,EAAI,GAAI,EACvD,MAAO,GACP,OAAQA,EAAK,OACb,MAAOA,EAAK,OACZ,CAAC,EACH,KAAK,kBAAkB,EACvB,KAAK,mBAAmB,CAAA,CAG1B,QAAS,OACJ,GAAA,CAAC,KAAK,KAAM,OACf,MAAME,EAAW,KAAK,KAAK,KAAK,KAAK,OAAS,CAAC,EAC/C,KAAK,kBAAkB,OAAO,CAC5B,KAAMA,EAAS,KACf,KAAMA,EAAS,KACf,IAAKA,EAAS,IACd,KAAM,KAAK,MAAMD,EAAA,WAAWC,EAAS,IAAI,EAAE,QAAQ,EAAI,GAAI,EAC3D,OAAQA,EAAS,OACjB,MAAOA,EAAS,KAAA,CACjB,GACDN,EAAA,KAAK,SAAL,MAAAA,EAAA,WACA,KAAK,mBAAmB,CAAA,CAG1B,aAAaP,EAA4B,OACvC,KAAK,QAAUG,EAAAA,MAAM,KAAK,QAASH,CAAO,GAC1CO,EAAA,KAAK,gBAAL,MAAAA,EAAA,UAAqBP,EAAO,CAG9B,QAAS,CACJ,KAAK,iBACD,KAAA,MAAM,yBAAyB,KAAK,eAAe,CAC1D,CAGF,mBAA2G,CAAE,QAAAc,CAAQ,EAAuBN,EAA8C,CACxL,GAAGM,IAAY,OACN,OAAAN,EAAO,YAAYM,CAAO,GAAK,MACxC,CAGF,WAAWC,EAAY,CACrB,OAAOC,EAAAA,aAAa,KAAK,kBAAkB,KAAM,KAAK,MAAMJ,EAAA,WAAWG,CAAI,EAAE,QAAY,EAAA,GAAI,EAAGJ,GAAQA,EAAK,IAAI,CAAA,CAGnH,WAAY,CACJ,MAAAD,EAAO,KAAK,kBAAkB,KACjC,GAAAA,EAAK,SAAW,EACZ,OAAAA,EAAKA,EAAK,OAAS,CAAC,CAAA,CAG7B,SAAU,CACR,OAAO,KAAK,IAAA,CAGd,mBAAoB,CAAA,CAEpB,oBAAqB,CAAA,CAErB,cAAe,CAAA,CAEf,cAAe,OACb,QAAOH,EAAA,KAAK,aAAL,YAAAA,EAAiB,UAAU,cAAe,CAAA,CAIrD"}