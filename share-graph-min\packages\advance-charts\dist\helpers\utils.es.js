import "./dayjs-setup.es.js";
import m from "dayjs";
function f(e) {
  if (typeof e == "number") return new Date(e * 1e3);
  if (typeof e == "string") return new Date(e);
  if (typeof e == "object")
    return new Date(e.year, e.month - 1, e.day);
  throw new Error("Do not support time");
}
function y(e) {
  return Math.floor(e.getTime() / 1e3);
}
function d(e) {
  return m(f(e));
}
function x(e) {
  return typeof e == "number" ? e : Math.floor(f(e).getTime() / 1e3);
}
function T(e) {
  return e.unix();
}
const c = (e, n) => e === n ? 0 : e < n ? -1 : 1;
function p(e, n, t, r = c) {
  let o = 0, u = e.length - 1;
  for (; o <= u; ) {
    const i = Math.floor((o + u) / 2), l = t(e[i]), a = r(l, n);
    if (a === 0)
      return i;
    a < 0 ? o = i + 1 : u = i - 1;
  }
  return -1;
}
function V(e, n, t, r = c) {
  const o = p(e, n, t, r);
  return o !== -1 ? e[o] : void 0;
}
function g(e) {
  return (n) => {
    const t = n(), r = (t == null ? void 0 : t.priceRange) ?? e;
    return {
      priceRange: {
        maxValue: Math.max(e.maxValue, r.maxValue),
        minValue: Math.min(e.minValue, r.minValue)
      }
    };
  };
}
export {
  g as autoScaleInfoProviderCreator,
  V as binarySearch,
  p as binarySearchIndex,
  y as dateToTime,
  T as dayjsToTime,
  c as defaultCompare,
  f as timeToDate,
  d as timeToDayjs,
  x as timeToUnix
};
//# sourceMappingURL=utils.es.js.map
