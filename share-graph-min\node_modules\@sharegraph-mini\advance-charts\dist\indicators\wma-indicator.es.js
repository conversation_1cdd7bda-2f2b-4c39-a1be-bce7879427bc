var h = Object.defineProperty;
var p = (t, i, e) => i in t ? h(t, i, { enumerable: !0, configurable: !0, writable: !0, value: e }) : t[i] = e;
var m = (t, i, e) => p(t, typeof i != "symbol" ? i + "" : i, e);
import { SeriesPrimitiveBase as d } from "../custom-primitive/primitive-base.es.js";
import { LinePrimitivePaneView as v } from "../custom-primitive/pane-view/line.es.js";
import { ChartIndicator as w } from "./abstract-indicator.es.js";
const f = {
  color: "#03fc03",
  period: 9,
  overlay: !0
};
class P extends d {
  constructor(e) {
    super();
    m(this, "linePrimitive");
    this.source = e, this.linePrimitive = new v({
      lineColor: this.source.options.color
    }), this._paneViews = [this.linePrimitive];
  }
  update(e) {
    const o = [];
    for (const s of e) {
      const r = s.value;
      r && o.push({ time: s.time, price: r[0] });
    }
    this.linePrimitive.update(o);
  }
}
class b extends w {
  constructor() {
    super(...arguments);
    m(this, "wmaPrimitive", new P(this));
  }
  getDefaultOptions() {
    return f;
  }
  _mainSeriesChanged(e) {
    e.attachPrimitive(this.wmaPrimitive);
  }
  remove() {
    var e;
    super.remove(), (e = this.mainSeries) == null || e.detachPrimitive(this.wmaPrimitive);
  }
  applyIndicatorData() {
    this.wmaPrimitive.update(
      this._executionContext.data
    );
  }
  formula(e) {
    const o = e.new_var(e.symbol.close, this.options.period);
    if (!o.calculable()) return;
    const s = o.getAll(), r = this.options.period;
    if (s.length < r) return;
    const c = Array.from({ length: r }, (a, n) => r - n), l = c.reduce((a, n) => a + n, 0);
    return [s.slice(-r).reduce((a, n, u) => a + n * c[u], 0) / l];
  }
}
export {
  P as WMAPrimitive,
  b as default,
  f as defaultOptions
};
//# sourceMappingURL=wma-indicator.es.js.map
