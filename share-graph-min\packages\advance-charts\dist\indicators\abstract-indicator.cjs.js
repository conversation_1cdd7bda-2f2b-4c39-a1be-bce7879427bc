"use strict";var h=Object.defineProperty;var d=(i,t,e)=>t in i?h(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var o=(i,t,e)=>d(i,typeof t!="symbol"?t+"":t,e);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const r=require("es-toolkit"),c=require("../helpers/delegate.cjs.js"),a=require("../helpers/utils.cjs.js"),l=require("../helpers/execution-indicator.cjs.js"),u="#26a69a",p="#ef5350";class m{constructor(t,e){o(this,"data",null);o(this,"options");o(this,"mainSeries",null);o(this,"_dataHovered",new c.Delegate);o(this,"indicatorData",[]);o(this,"_executionContext");this.chart=t,this.options=r.merge(r.cloneDeep(this.getDefaultOptions()),e??{}),this.onCrosshairMove=this.onCrosshairMove.bind(this),this.chart.subscribeCrosshairMove(this.onCrosshairMove),this._executionContext=new l.ExecutionContext(n=>{var s;return(s=this.formula)==null?void 0:s.call(this,n)})}formula(){}mainSeriesChanged(t){var e;this.mainSeries=t,(e=this._mainSeriesChanged)==null||e.call(this,t)}onCrosshairMove(t){if(t.time===void 0)return this._dataHovered.fire(void 0);this._dataHovered.fire(this.dataByTime(t.time))}dataHovered(){return this._dataHovered}setData(t){this.data=t,this._executionContext.recalc(t.map(e=>({open:e.open,high:e.high,low:e.low,time:Math.floor(a.timeToDate(e.time).getTime()/1e3),isNew:!1,volume:e.volume,close:e.close}))),this.calcIndicatorData(),this.applyIndicatorData()}update(){var e;if(!this.data)return;const t=this.data[this.data.length-1];this._executionContext.update({open:t.open,high:t.high,low:t.low,time:Math.floor(a.timeToDate(t.time).getTime()/1e3),volume:t.volume,close:t.close}),(e=this.recalc)==null||e.call(this),this.applyIndicatorData()}applyOptions(t){var e;this.options=r.merge(this.options,t),(e=this._applyOptions)==null||e.call(this,t)}remove(){this.onCrosshairMove&&this.chart.unsubscribeCrosshairMove(this.onCrosshairMove)}getDataByCrosshair({logical:t},e){if(t!==void 0)return e.dataByIndex(t)??void 0}dataByTime(t){return a.binarySearch(this._executionContext.data,Math.floor(a.timeToDate(t).getTime()/1e3),e=>e.time)}lastPoint(){const t=this._executionContext.data;if(t.length!==0)return t[t.length-1]}getData(){return this.data}calcIndicatorData(){}applyIndicatorData(){}setPaneIndex(){}getPaneIndex(){var t;return((t=this.mainSeries)==null?void 0:t.getPane().paneIndex())??0}}exports.ChartIndicator=m;exports.downColor=p;exports.upColor=u;
//# sourceMappingURL=abstract-indicator.cjs.js.map
