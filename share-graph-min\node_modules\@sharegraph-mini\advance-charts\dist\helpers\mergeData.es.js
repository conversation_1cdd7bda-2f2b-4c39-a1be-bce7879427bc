function n(e, i) {
  if (e.length === 0) return i;
  if (i.length === 0) return e;
  let h = e.length - 1;
  for (; h >= 0 && e[h].time > i[0].time; )
    h--;
  const m = e.slice(0, h + 1);
  h >= 0 && e[h].time === i[0].time && m.pop(), m.push(...i);
  const g = i[i.length - 1].time;
  let r = h + 1;
  for (; r < e.length && e[r].time <= g; )
    r++;
  for (; r < e.length; )
    m.push(e[r]), r++;
  return m;
}
export {
  n as mergeOhlcData
};
//# sourceMappingURL=mergeData.es.js.map
