"use strict";var u=Object.defineProperty;var p=(n,t,e)=>t in n?u(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var a=(n,t,e)=>p(n,typeof t!="symbol"?t+"":t,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const c=require("./abstract-indicator.cjs.js"),m=require("technicalindicators"),h=require("../custom-primitive/primitive-base.cjs.js"),w=require("../custom-primitive/pane-view/band.cjs.js"),s=require("../custom-primitive/pane-view/line.cjs.js"),l={backgroundColor:"#2196f312",upperLineColor:"#2196f3",middleLineColor:"#ff6d00",lowerLineColor:"#2196f3",period:20,stdDev:2,overlay:!0};class d extends h.SeriesPrimitiveBase{constructor(e){super();a(this,"bandPaneView");a(this,"upperPaneView");a(this,"middlePaneView");a(this,"lowerPaneView");this.source=e,this.bandPaneView=new w.BandPrimitivePaneView({backgroundColor:this.source.options.backgroundColor}),this.upperPaneView=new s.LinePrimitivePaneView({lineWidth:1,lineColor:this.source.options.upperLineColor}),this.middlePaneView=new s.LinePrimitivePaneView({lineWidth:1,lineColor:this.source.options.middleLineColor}),this.lowerPaneView=new s.LinePrimitivePaneView({lineWidth:1,lineColor:this.source.options.lowerLineColor}),this._paneViews=[this.bandPaneView,this.upperPaneView,this.middlePaneView,this.lowerPaneView]}update(e){const r=[];for(const i of e){const o=i.value;o&&r.push({time:i.time,value:o})}this.bandPaneView.update(r.map(i=>({time:i.time,upper:i.value[0],lower:i.value[2]}))),this.upperPaneView.update(r.map(i=>({time:i.time,price:i.value[0]}))),this.middlePaneView.update(r.map(i=>({time:i.time,price:i.value[1]}))),this.lowerPaneView.update(r.map(i=>({time:i.time,price:i.value[2]})))}}class P extends c.ChartIndicator{constructor(){super(...arguments);a(this,"bbPrimitive",new d(this))}_mainSeriesChanged(e){e.attachPrimitive(this.bbPrimitive)}_applyOptions(e){(e.period||e.stdDev)&&this.applyIndicatorData()}applyIndicatorData(){this.bbPrimitive.update(this._executionContext.data)}formula(e){const r=e.new_var(e.symbol.close,this.options.period);if(!r.calculable())return;const o=new m.BollingerBands({values:r.getAll(),period:this.options.period,stdDev:this.options.stdDev}).getResult().at(0);return[o.upper,o.middle,o.lower]}remove(){var e;super.remove(),(e=this.mainSeries)==null||e.detachPrimitive(this.bbPrimitive)}getDefaultOptions(){return l}}exports.BBPrimitive=d;exports.default=P;exports.defaultOptions=l;
//# sourceMappingURL=bb-indicator.cjs.js.map
