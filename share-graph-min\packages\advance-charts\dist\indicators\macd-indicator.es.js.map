{"version": 3, "file": "macd-indicator.es.js", "sources": ["../../src/indicators/macd-indicator.ts"], "sourcesContent": ["import {HistogramData, HistogramSeries, IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions, downColor, upColor} from \"./abstract-indicator\";\r\nimport {EMA, SMA} from \"technicalindicators\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface MACDIndicatorOptions extends ChartIndicatorOptions {\r\n  fastPeriod: number,\r\n  slowPeriod: number,\r\n  signalPeriod: number,\r\n  SimpleMAOscillator: boolean,\r\n  SimpleMASignal: boolean,\r\n  upColor: string,\r\n  downColor: string,\r\n  macdLineColor: string,\r\n  signalLineColor: string\r\n};\r\n\r\nexport const defaultOptions: MACDIndicatorOptions = {\r\n  fastPeriod: 12,\r\n  slowPeriod: 26,\r\n  signalPeriod: 9,\r\n  SimpleMAOscillator: false,\r\n  SimpleMASignal: false,\r\n  upColor: upColor,\r\n  downColor: downColor,\r\n  macdLineColor: '#2b97f1',\r\n  signalLineColor: '#fd6c1c',\r\n  overlay: false\r\n}\r\n\r\nexport type MacdMACDData = Nominal<number, 'Macd'>\r\nexport type SignalMACDData = Nominal<number, 'Signal'>\r\nexport type HistogramMACDData = Nominal<number, 'Histogram'>\r\nexport type MACDData = [MacdMACDData, SignalMACDData, HistogramMACDData]\r\n\r\nexport default class MACDIndicator extends ChartIndicator<MACDIndicatorOptions, MACDData> {\r\n  histogramSeries: ISeriesApi<'Histogram'>\r\n  macdSeries: ISeriesApi<SeriesType>\r\n  signalSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<MACDIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options);\r\n\r\n    this.histogramSeries = chart.addSeries(HistogramSeries, {\r\n      priceLineVisible: false,\r\n      priceScaleId: 'macd'\r\n    }, paneIndex)\r\n\r\n    this.macdSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.macdLineColor,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'macd'\r\n    }, paneIndex)\r\n\r\n    this.signalSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.signalLineColor,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'macd'\r\n    }, paneIndex)\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n    const histogramData: HistogramData[] = []\r\n    const macdData: SingleValueData[] = []\r\n    const signalData: SingleValueData[] = []\r\n\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      const time = bar.time as Time;\r\n      \r\n      if(!value) continue;\r\n      const [macd, signal, histogram] = value;\r\n      if(!isNaN(macd)) macdData.push({time, value: macd})\r\n      if(!isNaN(signal)) signalData.push({time, value: signal})\r\n      if(!isNaN(histogram)) histogramData.push({time, value: histogram, color: (histogram ?? 0) >= 0 ? this.options.upColor : this.options.downColor})\r\n    }\r\n\r\n    this.histogramSeries.setData(histogramData)\r\n    this.macdSeries.setData(macdData)\r\n    this.signalSeries.setData(signalData)\r\n  }\r\n\r\n  formula(c: Context): MACDData | undefined {\r\n      const fastPeriodSeries = c.new_var(c.symbol.close, this.options.fastPeriod)\r\n      const slowPeriodSeries = c.new_var(c.symbol.close, this.options.slowPeriod)\r\n      const signalPeriodSeries = c.new_var(NaN, this.options.signalPeriod)\r\n\r\n      if(!fastPeriodSeries.calculable() || !slowPeriodSeries.calculable()) return;\r\n\r\n      const oscillatorMAtype = this.options.SimpleMAOscillator ? SMA : EMA;\r\n      const signalMAtype = this.options.SimpleMASignal ? SMA : EMA;\r\n      const [fastMA] = new oscillatorMAtype({ period: this.options.fastPeriod, values: fastPeriodSeries.getAll()}).result;\r\n      const [slowMA] = new oscillatorMAtype({ period: this.options.slowPeriod, values: slowPeriodSeries.getAll()}).result;\r\n      const macd = fastMA - slowMA\r\n      signalPeriodSeries.set(macd);\r\n      if(!signalPeriodSeries.calculable()) return;\r\n      const [signalMA] = new signalMAtype({ period: this.options.signalPeriod, values: signalPeriodSeries.getAll()}).result;\r\n\r\n      const histogram = macd - signalMA;\r\n\r\n      return [macd as MacdMACDData, signalMA as SignalMACDData, histogram as HistogramMACDData]\r\n  }\r\n\r\n  _applyOptions(options: Partial<MACDIndicatorOptions>): void {\r\n    if(\r\n      options.SimpleMAOscillator || \r\n      options.SimpleMASignal ||\r\n      options.fastPeriod ||\r\n      options.signalPeriod ||\r\n      options.slowPeriod\r\n    ) {\r\n      this.calcIndicatorData()\r\n    } \r\n\r\n    if(options.macdLineColor) this.macdSeries.applyOptions({color: options.macdLineColor})\r\n    if(options.signalLineColor) this.signalSeries.applyOptions({color: options.signalLineColor})\r\n    \r\n    if(options.downColor || options.upColor) this.applyIndicatorData()\r\n  }\r\n\r\n  getDefaultOptions() {\r\n    return defaultOptions\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove()\r\n    this.chart.removeSeries(this.histogramSeries)\r\n    this.chart.removeSeries(this.macdSeries)\r\n    this.chart.removeSeries(this.signalSeries)\r\n  }\r\n\r\n  setPaneIndex(paneIndex: number): void {\r\n    this.histogramSeries.moveToPane(paneIndex);\r\n    this.macdSeries.moveToPane(paneIndex);\r\n    this.signalSeries.moveToPane(paneIndex);\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.histogramSeries.getPane().paneIndex()\r\n  }\r\n}\r\n"], "names": ["defaultOptions", "upColor", "downColor", "MACDIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "HistogramSeries", "LineSeries", "histogramData", "macdData", "signalData", "bar", "value", "time", "macd", "signal", "histogram", "c", "fastPeriodSeries", "slowPeriodSeries", "signalPeriodSeries", "oscillatorMAtype", "SMA", "EMA", "signalMAtype", "fastMA", "slowMA", "signalMA"], "mappings": ";;;;;;AAiBO,MAAMA,IAAuC;AAAA,EAClD,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,SAAAC;AAAA,EACA,WAAAC;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,SAAS;AACX;AAOA,MAAqBC,UAAsBC,EAA+C;AAAA,EAKxF,YAAYC,GAAkBC,GAAyCC,GAAoB;AACzF,UAAMF,GAAOC,CAAO;AALtB,IAAAE,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA;AAKO,SAAA,kBAAkBH,EAAM,UAAUI,GAAiB;AAAA,MACtD,kBAAkB;AAAA,MAClB,cAAc;AAAA,OACbF,CAAS,GAEP,KAAA,aAAaF,EAAM,UAAUK,GAAY;AAAA,MAC5C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,OACbH,CAAS,GAEP,KAAA,eAAeF,EAAM,UAAUK,GAAY;AAAA,MAC9C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,OACbH,CAAS;AAAA,EAAA;AAAA,EAGd,qBAA2B;AACzB,UAAMI,IAAiC,CAAC,GAClCC,IAA8B,CAAC,GAC/BC,IAAgC,CAAC;AAE7B,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI,OACZE,IAAOF,EAAI;AAEjB,UAAG,CAACC,EAAO;AACX,YAAM,CAACE,GAAMC,GAAQC,CAAS,IAAIJ;AAC/B,MAAC,MAAME,CAAI,KAAGL,EAAS,KAAK,EAAC,MAAAI,GAAM,OAAOC,GAAK,GAC9C,MAAMC,CAAM,KAAGL,EAAW,KAAK,EAAC,MAAAG,GAAM,OAAOE,GAAO,GACpD,MAAMC,CAAS,OAAiB,KAAK,EAAC,MAAAH,GAAM,OAAOG,GAAW,QAAQA,KAAa,MAAM,IAAI,KAAK,QAAQ,UAAU,KAAK,QAAQ,WAAU;AAAA,IAAA;AAG5I,SAAA,gBAAgB,QAAQR,CAAa,GACrC,KAAA,WAAW,QAAQC,CAAQ,GAC3B,KAAA,aAAa,QAAQC,CAAU;AAAA,EAAA;AAAA,EAGtC,QAAQO,GAAkC;AAChC,UAAAC,IAAmBD,EAAE,QAAQA,EAAE,OAAO,OAAO,KAAK,QAAQ,UAAU,GACpEE,IAAmBF,EAAE,QAAQA,EAAE,OAAO,OAAO,KAAK,QAAQ,UAAU,GACpEG,IAAqBH,EAAE,QAAQ,KAAK,KAAK,QAAQ,YAAY;AAEnE,QAAG,CAACC,EAAiB,WAAA,KAAgB,CAACC,EAAiB,aAAc;AAErE,UAAME,IAAmB,KAAK,QAAQ,qBAAqBC,IAAMC,GAC3DC,IAAe,KAAK,QAAQ,iBAAiBF,IAAMC,GACnD,CAACE,CAAM,IAAI,IAAIJ,EAAiB,EAAE,QAAQ,KAAK,QAAQ,YAAY,QAAQH,EAAiB,OAAO,EAAA,CAAE,EAAE,QACvG,CAACQ,CAAM,IAAI,IAAIL,EAAiB,EAAE,QAAQ,KAAK,QAAQ,YAAY,QAAQF,EAAiB,OAAO,EAAA,CAAE,EAAE,QACvGL,IAAOW,IAASC;AAEnB,QADHN,EAAmB,IAAIN,CAAI,GACxB,CAACM,EAAmB,aAAc;AACrC,UAAM,CAACO,CAAQ,IAAI,IAAIH,EAAa,EAAE,QAAQ,KAAK,QAAQ,cAAc,QAAQJ,EAAmB,OAAO,EAAA,CAAE,EAAE,QAEzGJ,IAAYF,IAAOa;AAElB,WAAA,CAACb,GAAsBa,GAA4BX,CAA8B;AAAA,EAAA;AAAA,EAG5F,cAAcb,GAA8C;AAExD,KAAAA,EAAQ,sBACRA,EAAQ,kBACRA,EAAQ,cACRA,EAAQ,gBACRA,EAAQ,eAER,KAAK,kBAAkB,GAGtBA,EAAQ,iBAAoB,KAAA,WAAW,aAAa,EAAC,OAAOA,EAAQ,eAAc,GAClFA,EAAQ,mBAAsB,KAAA,aAAa,aAAa,EAAC,OAAOA,EAAQ,iBAAgB,IAExFA,EAAQ,aAAaA,EAAQ,iBAAc,mBAAmB;AAAA,EAAA;AAAA,EAGnE,oBAAoB;AACX,WAAAN;AAAA,EAAA;AAAA,EAGT,SAAe;AACb,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,eAAe,GACvC,KAAA,MAAM,aAAa,KAAK,UAAU,GAClC,KAAA,MAAM,aAAa,KAAK,YAAY;AAAA,EAAA;AAAA,EAG3C,aAAaO,GAAyB;AAC/B,SAAA,gBAAgB,WAAWA,CAAS,GACpC,KAAA,WAAW,WAAWA,CAAS,GAC/B,KAAA,aAAa,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGxC,eAAuB;AACrB,WAAO,KAAK,gBAAgB,QAAQ,EAAE,UAAU;AAAA,EAAA;AAEpD;"}