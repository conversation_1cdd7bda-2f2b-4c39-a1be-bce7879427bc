import { DataChangedScope, DeepPartial, IChartApi, IPrimitivePaneRenderer, IPrimitivePaneView, ISeriesApi, ISeriesPrimitive, SeriesAttachedParameter, SeriesType, SingleValueData, Time, Logical, WhitespaceData, ITimeScaleApi } from 'lightweight-charts';
import { BitmapCoordinatesRenderingScope, CanvasRenderingTarget2D } from 'fancy-canvas';
import { OHLCVData } from '../interface';

export declare abstract class SeriesPrimitiveBase<TData extends WhitespaceData = WhitespaceData> implements ISeriesPrimitive<Time> {
    private _chart;
    private _series;
    protected _paneViews: IPrimitivePaneViewApi[];
    indicatorData: TData[];
    protected _isDetached: boolean;
    updateAllViews(): void;
    _updateAllViews?(): void;
    paneViews(): readonly IPrimitivePaneView[];
    protected dataUpdated?(scope: DataChangedScope): void;
    protected requestUpdate(): void;
    private _requestUpdate?;
    attached({ chart, series, requestUpdate, }: SeriesAttachedParameter<Time>): void;
    _attached?(): void;
    get data(): OHLCVData[];
    detached(): void;
    get chart(): IChartApi;
    get series(): ISeriesApi<SeriesType>;
    private _fireDataUpdated;
    dataByTime(time: Time): TData | undefined;
    lastPoint(): TData | undefined;
}
export interface IPrimitivePaneViewApi extends IPrimitivePaneView, IPrimitivePaneRenderer {
    attached(series: ISeriesApi<SeriesType>, chartApi: IChartApi): void;
}
export declare abstract class PrimitivePaneViewBase<TOptions extends object = object, TData = SingleValueData> implements IPrimitivePaneViewApi {
    options: TOptions;
    _series: ISeriesApi<SeriesType> | null;
    _chartApi: IChartApi | null;
    _data: TData[] | null;
    constructor(options?: DeepPartial<TOptions>);
    renderer(): IPrimitivePaneRenderer | null;
    draw(target: CanvasRenderingTarget2D): void;
    drawBackground(target: CanvasRenderingTarget2D): void;
    get data(): TData[];
    get series(): ISeriesApi<keyof import('lightweight-charts').SeriesOptionsMap, Time, WhitespaceData<Time> | import('lightweight-charts').BarData<Time> | import('lightweight-charts').CandlestickData<Time> | import('lightweight-charts').AreaData<Time> | import('lightweight-charts').BaselineData<Time> | import('lightweight-charts').LineData<Time> | import('lightweight-charts').HistogramData<Time> | import('lightweight-charts').CustomData<Time> | import('lightweight-charts').CustomSeriesWhitespaceData<Time>, import('lightweight-charts').BarSeriesOptions | import('lightweight-charts').CandlestickSeriesOptions | import('lightweight-charts').AreaSeriesOptions | import('lightweight-charts').BaselineSeriesOptions | import('lightweight-charts').LineSeriesOptions | import('lightweight-charts').HistogramSeriesOptions | import('lightweight-charts').CustomSeriesOptions, DeepPartial<import('lightweight-charts').BarStyleOptions & import('lightweight-charts').SeriesOptionsCommon> | DeepPartial<import('lightweight-charts').CandlestickStyleOptions & import('lightweight-charts').SeriesOptionsCommon> | DeepPartial<import('lightweight-charts').AreaStyleOptions & import('lightweight-charts').SeriesOptionsCommon> | DeepPartial<import('lightweight-charts').BaselineStyleOptions & import('lightweight-charts').SeriesOptionsCommon> | DeepPartial<import('lightweight-charts').LineStyleOptions & import('lightweight-charts').SeriesOptionsCommon> | DeepPartial<import('lightweight-charts').HistogramStyleOptions & import('lightweight-charts').SeriesOptionsCommon> | DeepPartial<import('lightweight-charts').CustomStyleOptions & import('lightweight-charts').SeriesOptionsCommon>>;
    get chartApi(): IChartApi;
    _timeScale: ITimeScaleApi<Time> | undefined;
    get timeScale(): ITimeScaleApi<Time>;
    getVisibleLogicalRange(): {
        from: Logical;
        to: Logical;
    } | undefined;
    getVisibleRange(): import('lightweight-charts').IRange<Time> | null;
    coordinateToPrice(coordinate: number): import('lightweight-charts').BarPrice | null;
    priceToCoordinate(price: number): import('lightweight-charts').Coordinate | null;
    timeToCoordinate(time: Time): import('lightweight-charts').Coordinate | null;
    attached(series: ISeriesApi<SeriesType>, chartApi: IChartApi): void;
    update(data: TData[]): void;
    _update?(): void;
    _drawImpl?(renderingScope: BitmapCoordinatesRenderingScope): void;
    _drawBackgroundImpl?(renderingScope: BitmapCoordinatesRenderingScope): void;
    abstract defaultOptions(): TOptions;
}
