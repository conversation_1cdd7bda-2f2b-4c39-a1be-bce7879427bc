import { DeepPartial, IChartApi, ISeriesApi, Logical, SeriesType } from 'lightweight-charts';
import { ChartIndicator } from '../indicators/abstract-indicator';
import { OHLCVExtraData, OHLCVSimple } from '../interface';
import { Delegate, IPublicDelegate, ISubscription } from '../helpers/delegate';
import { VolumeIndicatorOptions } from '../indicators/volume-indicator';
import { IAdvanceChart, IAdvanceChartOptions, IAdvanceChartType, IGroupIndicatorByPane, Interval } from './i-advance-chart';

export declare const defaultAdvanceChartOptions: IAdvanceChartOptions;
export declare class AdvanceChart implements IAdvanceChart {
    options: IAdvanceChartOptions;
    chartApi: IChartApi;
    chartType: IAdvanceChartType | null;
    mainSeries: ISeriesApi<SeriesType> | null;
    dataInterval: Interval;
    __destroyed: boolean;
    private data;
    private indicators;
    private _volumeType;
    private _chartTypeChanged;
    private _indicatorChanged;
    private _destroyed;
    private _updated;
    private _crosshairMoved;
    private _chartHovered;
    private _dataSetChanged;
    private _loading;
    private _optionChanged;
    private _mainSeriesChanged;
    private _displayTimezone;
    constructor(container: HTMLElement, options?: DeepPartial<IAdvanceChartOptions>);
    get numberFormatter(): import('../helpers').NumberFormatter;
    get dataSet(): OHLCVExtraData[];
    getData(): OHLCVExtraData[];
    getIndicators(): ChartIndicator<import('../advance-charts').ChartIndicatorOptions, number[], import('../interface').OHLCVData>[];
    getPointFromIndex(index: number): readonly [OHLCVExtraData, OHLCVExtraData];
    lastPoint(): readonly [OHLCVExtraData, OHLCVExtraData];
    setChartType(type: IAdvanceChartType): void;
    updateBaselineChartType(): void;
    setData(data: OHLCVSimple[], interval: Interval): void;
    applyMainSeriesData(): void;
    applyData(): void;
    update(bar: OHLCVSimple, replaceLastPoint?: boolean): void;
    tryDrawUpDownLine(): void;
    applyOptions(options: DeepPartial<IAdvanceChartOptions>): void;
    isShowVolume(): boolean;
    showVolume(type?: typeof this._volumeType, options?: Partial<VolumeIndicatorOptions>): void;
    hiddenVolume(): void;
    listIndicators(): string[];
    addIndicator(name: string): ChartIndicator<import('../advance-charts').ChartIndicatorOptions, number[], import('../interface').OHLCVData>;
    removeIndicator(name: string): void;
    hasIndicator(name: string): boolean;
    remove(): void;
    groupIndicatorByPane(): Array<IGroupIndicatorByPane>;
    fitRange(range: {
        from: Logical;
        to: Logical;
    }): void;
    fitContent(): {
        from: Logical;
        to: Logical;
    };
    get maxBar(): number;
    get loading(): boolean;
    set loading(status: boolean);
    /** --------------- public delegate --------------- */
    updated(): IPublicDelegate<typeof this._updated>;
    chartTypeChanged(): IPublicDelegate<typeof this._chartTypeChanged>;
    indicatorChanged(): IPublicDelegate<typeof this._indicatorChanged>;
    crosshairMoved(): Delegate<OHLCVExtraData, OHLCVExtraData, void>;
    destroyed(): Delegate<void, void, void>;
    chartHovered(): Delegate<boolean, void, void>;
    onLoading(): Delegate<boolean, void, void>;
    optionChanged(): ISubscription;
    mainSeriesChanged(): ISubscription;
}
