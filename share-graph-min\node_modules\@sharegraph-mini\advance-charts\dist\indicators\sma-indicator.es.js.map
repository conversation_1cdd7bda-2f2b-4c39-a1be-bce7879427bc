{"version": 3, "file": "sma-indicator.es.js", "sources": ["../../src/indicators/sma-indicator.ts"], "sourcesContent": ["import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from \"lightweight-charts\";\r\nimport {SMA} from \"technicalindicators\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {SeriesPrimitiveBase} from \"../custom-primitive/primitive-base\";\r\nimport {LineData, LinePrimitivePaneView} from \"../custom-primitive/pane-view/line\";\r\nimport {Context, IIndicatorBar} from \"../helpers/execution-indicator\";\r\n\r\nexport interface SMAIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  period: number\r\n}\r\n\r\nexport const defaultOptions: SMAIndicatorOptions = {\r\n  color: \"#2962ff\",\r\n  period: 9,\r\n  overlay: true\r\n}\r\n\r\nexport class SMAPrimitive extends SeriesPrimitiveBase<\r\n  SingleValueData | WhitespaceData\r\n> {\r\n  linePrimitive: LinePrimitivePaneView;\r\n  constructor(protected source: SMAIndicator) {\r\n    super();\r\n    this.linePrimitive = new LinePrimitivePaneView({\r\n      lineColor: this.source.options.color,\r\n    });\r\n    this._paneViews = [this.linePrimitive];\r\n  }\r\n\r\n  update(indicatorBars: IIndicatorBar<SMAData>[]) {\r\n    const lineData: LineData[] = []\r\n    for(const bar of indicatorBars) {\r\n      const value = bar.value\r\n      if(value) lineData.push({time: bar.time as Time, price: value[0]})\r\n    }\r\n\r\n    this.linePrimitive.update(lineData);\r\n  }\r\n}\r\n\r\nexport type SMAData = readonly [Nominal<number, 'SMA'>]\r\n\r\nexport default class SMAIndicator extends ChartIndicator<SMAIndicatorOptions, SMAData> {\r\n  smaPrimitive = new SMAPrimitive(this)\r\n  getDefaultOptions(): SMAIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n\r\n  _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {\r\n    series.attachPrimitive(this.smaPrimitive)\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove();\r\n    this.mainSeries?.detachPrimitive(this.smaPrimitive)\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n    this.smaPrimitive.update(\r\n      this._executionContext.data\r\n    )\r\n  }\r\n\r\n  formula(c: Context) {\r\n    const smaSeries = c.new_var(c.symbol.close, this.options.period)\r\n    if(!smaSeries.calculable()) return;\r\n    const sma = new SMA({\r\n      values: smaSeries.getAll(),\r\n      period: this.options.period\r\n    });\r\n\r\n    return sma.getResult()\r\n  }\r\n}"], "names": ["defaultOptions", "SMAPrimitive", "SeriesPrimitiveBase", "source", "__publicField", "LinePrimitivePaneView", "indicatorBars", "lineData", "bar", "value", "SMAIndicator", "ChartIndicator", "series", "_a", "c", "smaSeries", "SMA"], "mappings": ";;;;;;;AAYO,MAAMA,IAAsC;AAAA,EACjD,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AACX;AAEO,MAAMC,UAAqBC,EAEhC;AAAA,EAEA,YAAsBC,GAAsB;AACpC,UAAA;AAFR,IAAAC,EAAA;AACsB,SAAA,SAAAD,GAEf,KAAA,gBAAgB,IAAIE,EAAsB;AAAA,MAC7C,WAAW,KAAK,OAAO,QAAQ;AAAA,IAAA,CAChC,GACI,KAAA,aAAa,CAAC,KAAK,aAAa;AAAA,EAAA;AAAA,EAGvC,OAAOC,GAAyC;AAC9C,UAAMC,IAAuB,CAAC;AAC9B,eAAUC,KAAOF,GAAe;AAC9B,YAAMG,IAAQD,EAAI;AACf,MAAAC,KAAgBF,EAAA,KAAK,EAAC,MAAMC,EAAI,MAAc,OAAOC,EAAM,CAAC,EAAA,CAAE;AAAA,IAAA;AAG9D,SAAA,cAAc,OAAOF,CAAQ;AAAA,EAAA;AAEtC;AAIA,MAAqBG,UAAqBC,EAA6C;AAAA,EAAvF;AAAA;AACE,IAAAP,EAAA,sBAAe,IAAIH,EAAa,IAAI;AAAA;AAAA,EACpC,oBAAyC;AAChC,WAAAD;AAAA,EAAA;AAAA,EAGT,mBAAmBY,GAAsC;AAChD,IAAAA,EAAA,gBAAgB,KAAK,YAAY;AAAA,EAAA;AAAA,EAG1C,SAAe;;AACb,UAAM,OAAO,IACRC,IAAA,KAAA,eAAA,QAAAA,EAAY,gBAAgB,KAAK;AAAA,EAAY;AAAA,EAGpD,qBAA2B;AACzB,SAAK,aAAa;AAAA,MAChB,KAAK,kBAAkB;AAAA,IACzB;AAAA,EAAA;AAAA,EAGF,QAAQC,GAAY;AACZ,UAAAC,IAAYD,EAAE,QAAQA,EAAE,OAAO,OAAO,KAAK,QAAQ,MAAM;AAC5D,WAACC,EAAU,eACF,IAAIC,EAAI;AAAA,MAClB,QAAQD,EAAU,OAAO;AAAA,MACzB,QAAQ,KAAK,QAAQ;AAAA,IAAA,CACtB,EAEU,UAAU,IANO;AAAA,EAMP;AAEzB;"}