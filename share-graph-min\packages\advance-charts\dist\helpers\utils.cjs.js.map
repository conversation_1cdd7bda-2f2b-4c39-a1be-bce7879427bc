{"version": 3, "file": "utils.cjs.js", "sources": ["../../src/helpers/utils.ts"], "sourcesContent": ["import {AutoscaleInfoProvider, Time} from \"lightweight-charts\";\r\nimport dayjs from './dayjs-setup'\r\nimport { Dayjs } from \"dayjs\";\r\nexport function timeToDate(time: Time) {\r\n  if(typeof time === 'number') return new Date(time * 1000);\r\n  if(typeof time === 'string') return new Date(time);\r\n  if(typeof time === 'object') {\r\n    return new Date(time.year, time.month - 1, time.day)\r\n  }\r\n\r\n  throw new Error('Do not support time')\r\n}\r\n\r\nexport function dateToTime(date: Date) {\r\n  return Math.floor(date.getTime() / 1000) as Time\r\n}\r\n\r\nexport function timeToDayjs(time: Time) {\r\n  return dayjs(timeToDate(time))\r\n} \r\n\r\nexport function timeToUnix(time: Time) {\r\n  if(typeof time === 'number') return time\r\n  return Math.floor(timeToDate(time).getTime() / 1000)\r\n}\r\n\r\nexport function dayjsToTime(djs: Dayjs): Time {\r\n  return djs.unix() as Time\r\n}\r\n\r\nexport const defaultCompare = (a: number | string, b: number | string) => a === b ? 0 : a < b ? -1 : 1\r\nexport function binarySearchIndex<T, V extends number | string>(\r\n  arr: T[],\r\n  target: V,\r\n  keyFn: (item: T) => V,\r\n  compare: (a: V, b: V) => number = defaultCompare\r\n): number {\r\n  let left = 0;\r\n  let right = arr.length - 1;\r\n\r\n  while (left <= right) {\r\n    const mid = Math.floor((left + right) / 2);\r\n    const midValue = keyFn(arr[mid]);\r\n    const comparison = compare(midValue, target);\r\n\r\n    if (comparison === 0) {\r\n      // Target found, return its index\r\n      return mid;\r\n    } else if (comparison < 0) {\r\n      // Search in the right half\r\n      left = mid + 1;\r\n    } else {\r\n      // Search in the left half\r\n      right = mid - 1;\r\n    }\r\n  }\r\n\r\n  // Target not found\r\n  return -1;\r\n}\r\n\r\nexport function binarySearch<T, V extends number | string>(\r\n  arr: T[],\r\n  target: V,\r\n  keyFn: (item: T) => V,\r\n  compare: (a: V, b: V) => number = defaultCompare\r\n): T | undefined {\r\n  const index = binarySearchIndex(arr, target, keyFn, compare);\r\n  return index !== -1 ? arr[index] : undefined;\r\n}\r\n\r\nexport function autoScaleInfoProviderCreator(defaultPriceRange: {maxValue: number, minValue: number}) {\r\n  return ((baseImplementation) => {\r\n    const base = baseImplementation();\r\n    const priceRange = base?.priceRange ?? defaultPriceRange\r\n\r\n    return {\r\n      priceRange: {\r\n        maxValue: Math.max(defaultPriceRange.maxValue, priceRange.maxValue),\r\n        minValue: Math.min(defaultPriceRange.minValue, priceRange.minValue),\r\n      }\r\n    }\r\n  }) satisfies AutoscaleInfoProvider\r\n}"], "names": ["timeToDate", "time", "dateToTime", "date", "timeToD<PERSON><PERSON><PERSON>", "dayjs", "timeToUnix", "dayjsToTime", "djs", "defaultCompare", "a", "b", "binarySearchIndex", "arr", "target", "keyFn", "compare", "left", "right", "mid", "midValue", "comparison", "binarySearch", "index", "autoScaleInfoProviderCreator", "defaultPriceRange", "baseImplementation", "base", "priceRange"], "mappings": "yIAGO,SAASA,EAAWC,EAAY,CACrC,GAAG,OAAOA,GAAS,gBAAiB,IAAI,KAAKA,EAAO,GAAI,EACxD,GAAG,OAAOA,GAAS,SAAiB,OAAA,IAAI,KAAKA,CAAI,EAC9C,GAAA,OAAOA,GAAS,SACV,OAAA,IAAI,KAAKA,EAAK,KAAMA,EAAK,MAAQ,EAAGA,EAAK,GAAG,EAG/C,MAAA,IAAI,MAAM,qBAAqB,CACvC,CAEO,SAASC,EAAWC,EAAY,CACrC,OAAO,KAAK,MAAMA,EAAK,QAAA,EAAY,GAAI,CACzC,CAEO,SAASC,EAAYH,EAAY,CAC/B,OAAAI,EAAML,EAAWC,CAAI,CAAC,CAC/B,CAEO,SAASK,EAAWL,EAAY,CAClC,OAAA,OAAOA,GAAS,SAAiBA,EAC7B,KAAK,MAAMD,EAAWC,CAAI,EAAE,UAAY,GAAI,CACrD,CAEO,SAASM,EAAYC,EAAkB,CAC5C,OAAOA,EAAI,KAAK,CAClB,CAEa,MAAAC,EAAiB,CAACC,EAAoBC,IAAuBD,IAAMC,EAAI,EAAID,EAAIC,EAAI,GAAK,EAC9F,SAASC,EACdC,EACAC,EACAC,EACAC,EAAkCP,EAC1B,CACR,IAAIQ,EAAO,EACPC,EAAQL,EAAI,OAAS,EAEzB,KAAOI,GAAQC,GAAO,CACpB,MAAMC,EAAM,KAAK,OAAOF,EAAOC,GAAS,CAAC,EACnCE,EAAWL,EAAMF,EAAIM,CAAG,CAAC,EACzBE,EAAaL,EAAQI,EAAUN,CAAM,EAE3C,GAAIO,IAAe,EAEV,OAAAF,EACEE,EAAa,EAEtBJ,EAAOE,EAAM,EAGbD,EAAQC,EAAM,CAChB,CAIK,MAAA,EACT,CAEO,SAASG,EACdT,EACAC,EACAC,EACAC,EAAkCP,EACnB,CACf,MAAMc,EAAQX,EAAkBC,EAAKC,EAAQC,EAAOC,CAAO,EAC3D,OAAOO,IAAU,GAAKV,EAAIU,CAAK,EAAI,MACrC,CAEO,SAASC,EAA6BC,EAAyD,CACpG,OAASC,GAAuB,CAC9B,MAAMC,EAAOD,EAAmB,EAC1BE,GAAaD,GAAA,YAAAA,EAAM,aAAcF,EAEhC,MAAA,CACL,WAAY,CACV,SAAU,KAAK,IAAIA,EAAkB,SAAUG,EAAW,QAAQ,EAClE,SAAU,KAAK,IAAIH,EAAkB,SAAUG,EAAW,QAAQ,CAAA,CAEtE,CACF,CACF"}