{"version": 3, "file": "volume-indicator.cjs.js", "sources": ["../../src/indicators/volume-indicator.ts"], "sourcesContent": ["import {\r\n  ISeriesApi,\r\n  SeriesType,\r\n  HistogramSeries,\r\n  IChartApi,\r\n  Nominal,\r\n  Time,\r\n} from 'lightweight-charts';\r\nimport { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';\r\nimport { Color } from '../helpers/color';\r\nimport {Context} from '../helpers/execution-indicator';\r\nimport {ensureDefined} from '../helpers/assertions';\r\n\r\nexport interface VolumeIndicatorOptions extends ChartIndicatorOptions {\r\n  upColor: string;\r\n  downColor: string;\r\n}\r\n\r\nexport const defaultOptions: VolumeIndicatorOptions = {\r\n  upColor: '#26a69a',\r\n  downColor: '#ef5350',\r\n  overlay: false,\r\n};\r\n\r\nexport type VolumePoint = Nominal<number, 'VolumePoint'>\r\nexport type VolumePositive = Nominal<0 | 1, 'Positive'>\r\n\r\nexport type VolumeIndicatorData = [VolumePoint, VolumePositive]\r\n\r\nexport default class VolumeIndicator extends ChartIndicator<\r\n  VolumeIndicatorOptions,\r\n  VolumeIndicatorData\r\n> {\r\n  volumeSeries: ISeriesApi<SeriesType>;\r\n\r\n  constructor(\r\n    protected chart: IChartApi,\r\n    options?: Partial<VolumeIndicatorOptions>,\r\n    paneIndex?: number\r\n  ) {\r\n    super(chart, options);\r\n    const numberFormatter = this.options.numberFormatter;\r\n\r\n    this.volumeSeries = this.chart.addSeries(\r\n      HistogramSeries,\r\n      {\r\n        priceLineVisible: false,\r\n        priceFormat: numberFormatter\r\n          ? {\r\n              type: 'custom',\r\n              formatter: (volume: number) => numberFormatter().volume(volume),\r\n            }\r\n          : { type: 'volume' },\r\n        priceScaleId: 'volume',\r\n      },\r\n      this.options.overlay ? 0 : paneIndex\r\n    );\r\n\r\n    this.applyPriceScaleMargins()\r\n  }\r\n\r\n  applyPriceScaleMargins() {\r\n    if (this.options.overlay) {\r\n      this.volumeSeries.priceScale().applyOptions({\r\n        scaleMargins: {\r\n          top: 0.7,\r\n          bottom: 0,\r\n        },\r\n      });\r\n    } else {\r\n      this.volumeSeries.priceScale().applyOptions({\r\n        scaleMargins: {\r\n          top: 0.1,\r\n          bottom: 0,\r\n        },\r\n      });\r\n    }\r\n  }\r\n  _applyOptions(options: Partial<VolumeIndicatorOptions>): void {\r\n    if (options.downColor || options.upColor) {\r\n      this.applyIndicatorData();\r\n    }\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n    const volumeData = this._executionContext.data.filter(item => item.value)\r\n    this.volumeSeries.setData(\r\n      volumeData.map((item) => ({\r\n        time: item.time as Time,\r\n        value: ensureDefined(item.value)[0],\r\n        color: Color.applyAlpha(\r\n          ensureDefined(item.value)[1] === 1\r\n            ? this.options.upColor\r\n            : this.options.downColor,\r\n          this.options.overlay ? 0.6 : 1\r\n        ),\r\n      }))\r\n    );\r\n  }\r\n\r\n  formula(c: Context): VolumeIndicatorData | undefined {\r\n    const closeSeries = c.new_var(c.symbol.close, 2);\r\n    if(!closeSeries.calculable()) return;\r\n    const positive = closeSeries.get(0) > closeSeries.get(1) ? 1 : 0\r\n    return [c.symbol.volume as VolumePoint, positive as VolumePositive]\r\n  }\r\n\r\n  getDefaultOptions(): VolumeIndicatorOptions {\r\n    return defaultOptions;\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove();\r\n    this.chart.removeSeries(this.volumeSeries);\r\n  }\r\n\r\n  setPaneIndex(paneIndex: number): void {\r\n    this.volumeSeries.moveToPane(paneIndex);\r\n    this.applyPriceScaleMargins()\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.volumeSeries.getPane().paneIndex();\r\n  }\r\n}\r\n"], "names": ["defaultOptions", "VolumeIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "numberF<PERSON>atter", "HistogramSeries", "volume", "volumeData", "item", "ensureDefined", "Color", "c", "closeSeries", "positive"], "mappings": "8aAkBaA,EAAyC,CACpD,QAAS,UACT,UAAW,UACX,QAAS,EACX,EAOA,MAAqBC,UAAwBC,EAAAA,cAG3C,CAGA,YACYC,EACVC,EACAC,EACA,CACA,MAAMF,EAAOC,CAAO,EAPtBE,EAAA,qBAGY,KAAA,MAAAH,EAKJ,MAAAI,EAAkB,KAAK,QAAQ,gBAEhC,KAAA,aAAe,KAAK,MAAM,UAC7BC,EAAA,gBACA,CACE,iBAAkB,GAClB,YAAaD,EACT,CACE,KAAM,SACN,UAAYE,GAAmBF,EAAgB,EAAE,OAAOE,CAAM,CAAA,EAEhE,CAAE,KAAM,QAAS,EACrB,aAAc,QAChB,EACA,KAAK,QAAQ,QAAU,EAAIJ,CAC7B,EAEA,KAAK,uBAAuB,CAAA,CAG9B,wBAAyB,CACnB,KAAK,QAAQ,QACV,KAAA,aAAa,WAAW,EAAE,aAAa,CAC1C,aAAc,CACZ,IAAK,GACL,OAAQ,CAAA,CACV,CACD,EAEI,KAAA,aAAa,WAAW,EAAE,aAAa,CAC1C,aAAc,CACZ,IAAK,GACL,OAAQ,CAAA,CACV,CACD,CACH,CAEF,cAAcD,EAAgD,EACxDA,EAAQ,WAAaA,EAAQ,UAC/B,KAAK,mBAAmB,CAC1B,CAGF,oBAA2B,CACzB,MAAMM,EAAa,KAAK,kBAAkB,KAAK,OAAOC,GAAQA,EAAK,KAAK,EACxE,KAAK,aAAa,QAChBD,EAAW,IAAKC,IAAU,CACxB,KAAMA,EAAK,KACX,MAAOC,EAAAA,cAAcD,EAAK,KAAK,EAAE,CAAC,EAClC,MAAOE,EAAM,MAAA,WACXD,EAAAA,cAAcD,EAAK,KAAK,EAAE,CAAC,IAAM,EAC7B,KAAK,QAAQ,QACb,KAAK,QAAQ,UACjB,KAAK,QAAQ,QAAU,GAAM,CAAA,CAC/B,EACA,CACJ,CAAA,CAGF,QAAQG,EAA6C,CACnD,MAAMC,EAAcD,EAAE,QAAQA,EAAE,OAAO,MAAO,CAAC,EAC5C,GAAA,CAACC,EAAY,aAAc,OACxB,MAAAC,EAAWD,EAAY,IAAI,CAAC,EAAIA,EAAY,IAAI,CAAC,EAAI,EAAI,EAC/D,MAAO,CAACD,EAAE,OAAO,OAAuBE,CAA0B,CAAA,CAGpE,mBAA4C,CACnC,OAAAhB,CAAA,CAGT,QAAe,CACb,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,YAAY,CAAA,CAG3C,aAAaK,EAAyB,CAC/B,KAAA,aAAa,WAAWA,CAAS,EACtC,KAAK,uBAAuB,CAAA,CAG9B,cAAuB,CACrB,OAAO,KAAK,aAAa,QAAQ,EAAE,UAAU,CAAA,CAEjD"}