{"version": 3, "file": "primitive-base.cjs.js", "sources": ["../../src/custom-primitive/primitive-base.ts"], "sourcesContent": ["import {\r\n  DataChangedScope,\r\n  DeepPartial,\r\n  IChartApi,\r\n  IPrimitivePaneRenderer,\r\n  IPrimitivePaneView,\r\n  ISeriesApi,\r\n  ISeriesPrimitive,\r\n  SeriesAttachedParameter,\r\n  SeriesType,\r\n  SingleValueData,\r\n  Time,\r\n  LogicalRange,\r\n  Logical,\r\n  WhitespaceData,\r\n  ITimeScaleApi,\r\n} from \"lightweight-charts\";\r\nimport { ensureDefined, ensureNotNull } from \"../helpers/assertions\";\r\nimport {\r\n  BitmapCoordinatesRenderingScope,\r\n  CanvasRenderingTarget2D,\r\n} from \"fancy-canvas\";\r\nimport { cloneDeep, merge } from \"es-toolkit\";\r\nimport { binarySearch, timeToUnix } from \"../helpers/utils\";\r\nimport { OHLCVData } from \"../interface\";\r\n\r\nexport abstract class SeriesPrimitiveBase<\r\n  TData extends WhitespaceData = WhitespaceData,\r\n> implements ISeriesPrimitive<Time>\r\n{\r\n  private _chart: IChartApi | undefined = undefined;\r\n  private _series: ISeriesApi<SeriesType> | undefined = undefined;\r\n  protected _paneViews: IPrimitivePaneViewApi[] = [];\r\n  indicatorData: TData[] = [];\r\n  protected _isDetached = false;\r\n\r\n  updateAllViews(): void {\r\n    if (this._isDetached) return;\r\n    this._updateAllViews?.();\r\n  }\r\n\r\n  _updateAllViews?() {}\r\n\r\n  paneViews(): readonly IPrimitivePaneView[] {\r\n    return this._paneViews;\r\n  }\r\n\r\n  protected dataUpdated?(scope: DataChangedScope): void;\r\n  protected requestUpdate(): void {\r\n    if (this._requestUpdate) this._requestUpdate();\r\n  }\r\n  private _requestUpdate?: () => void;\r\n\r\n  public attached({\r\n    chart,\r\n    series,\r\n    requestUpdate,\r\n  }: SeriesAttachedParameter<Time>) {\r\n    this._chart = chart;\r\n    this._series = series;\r\n    this._series.subscribeDataChanged(this._fireDataUpdated);\r\n    this._requestUpdate = requestUpdate;\r\n    this.requestUpdate();\r\n    this._paneViews.map((item) => item.attached(series, chart));\r\n\r\n    this._attached?.();\r\n  }\r\n\r\n  _attached?(): void;\r\n\r\n  get data() {\r\n    const data = this.series.data();\r\n    if (data.length > 0) {\r\n      ensureDefined(data[0].customValues);\r\n    } else {\r\n      return [];\r\n    }\r\n    return data.map((item) => item.customValues as unknown as OHLCVData);\r\n  }\r\n\r\n  public detached() {\r\n    this._series?.unsubscribeDataChanged(this._fireDataUpdated);\r\n    this._chart = undefined;\r\n    this._series = undefined;\r\n    this._paneViews = [];\r\n    this._requestUpdate = undefined;\r\n    this._isDetached = true;\r\n  }\r\n\r\n  public get chart(): IChartApi {\r\n    return ensureDefined(this._chart);\r\n  }\r\n\r\n  public get series(): ISeriesApi<SeriesType> {\r\n    return ensureDefined(this._series);\r\n  }\r\n\r\n  // This method is a class property to maintain the\r\n  // lexical 'this' scope (due to the use of the arrow function)\r\n  // and to ensure its reference stays the same, so we can unsubscribe later.\r\n  private _fireDataUpdated = (scope: DataChangedScope) => {\r\n    if (this.dataUpdated) {\r\n      this.dataUpdated(scope);\r\n    }\r\n  };\r\n\r\n  dataByTime(time: Time): TData | undefined {\r\n    return binarySearch(this.indicatorData, timeToUnix(time), (item) =>\r\n      timeToUnix(item.time)\r\n    );\r\n  }\r\n\r\n  lastPoint(): TData | undefined {\r\n    return this.indicatorData.at(-1);\r\n  }\r\n}\r\n\r\nexport interface IPrimitivePaneViewApi\r\n  extends IPrimitivePaneView,\r\n    IPrimitivePaneRenderer {\r\n  attached(series: ISeriesApi<SeriesType>, chartApi: IChartApi): void;\r\n}\r\n\r\nexport abstract class PrimitivePaneViewBase<\r\n  TOptions extends object = object,\r\n  TData = SingleValueData,\r\n> implements IPrimitivePaneViewApi\r\n{\r\n  options: TOptions;\r\n\r\n  _series: ISeriesApi<SeriesType> | null = null;\r\n  _chartApi: IChartApi | null = null;\r\n  _data: TData[] | null = null;\r\n\r\n  constructor(options?: DeepPartial<TOptions>) {\r\n    this.options = merge(cloneDeep(this.defaultOptions()), options ?? {});\r\n  }\r\n\r\n  renderer(): IPrimitivePaneRenderer | null {\r\n    return this;\r\n  }\r\n\r\n  draw(target: CanvasRenderingTarget2D) {\r\n    if (!this._chartApi) return;\r\n    if (!this._series) return;\r\n\r\n    target.useBitmapCoordinateSpace((scope) => this._drawImpl?.(scope));\r\n  }\r\n\r\n  drawBackground(target: CanvasRenderingTarget2D) {\r\n    if (!this._chartApi) return;\r\n    if (!this._series) return;\r\n\r\n    target.useBitmapCoordinateSpace((scope) =>\r\n      this._drawBackgroundImpl?.(scope)\r\n    );\r\n  }\r\n\r\n  get data() {\r\n    return ensureNotNull(this._data);\r\n  }\r\n\r\n  get series() {\r\n    return ensureNotNull(this._series);\r\n  }\r\n\r\n  get chartApi() {\r\n    return ensureNotNull(this._chartApi);\r\n  }\r\n\r\n  _timeScale: ITimeScaleApi<Time> | undefined;\r\n  get timeScale() {\r\n    if (!this._timeScale) this._timeScale = this.chartApi.timeScale();\r\n    return this._timeScale;\r\n  }\r\n\r\n  getVisibleLogicalRange() {\r\n    const range = this.timeScale.getVisibleLogicalRange();\r\n    if (!range) return;\r\n    return {\r\n      from: Math.floor(range.from) as Logical,\r\n      to: Math.ceil(range.to) as Logical,\r\n    } satisfies LogicalRange;\r\n  }\r\n\r\n  getVisibleRange() {\r\n    return this.timeScale.getVisibleRange();\r\n  }\r\n\r\n  coordinateToPrice(coordinate: number) {\r\n    return this.series.coordinateToPrice(coordinate);\r\n  }\r\n  priceToCoordinate(price: number) {\r\n    return this.series.priceToCoordinate(price);\r\n  }\r\n\r\n  timeToCoordinate(time: Time) {\r\n    return this.timeScale.timeToCoordinate(time);\r\n  }\r\n\r\n  attached(series: ISeriesApi<SeriesType>, chartApi: IChartApi) {\r\n    this._series = series;\r\n    this._chartApi = chartApi;\r\n  }\r\n\r\n  update(data: TData[]) {\r\n    this._data = data;\r\n    this._update?.();\r\n  }\r\n\r\n  _update?(): void;\r\n  _drawImpl?(renderingScope: BitmapCoordinatesRenderingScope): void;\r\n  _drawBackgroundImpl?(renderingScope: BitmapCoordinatesRenderingScope): void;\r\n\r\n  abstract defaultOptions(): TOptions;\r\n}\r\n"], "names": ["SeriesPrimitiveBase", "__publicField", "scope", "_a", "chart", "series", "requestUpdate", "item", "data", "ensureDefined", "time", "binarySearch", "timeToUnix", "PrimitivePaneViewBase", "options", "merge", "cloneDeep", "target", "ensureNotNull", "range", "coordinate", "price", "chartApi"], "mappings": "iWA0BO,MAAeA,CAGtB,CAHO,cAIGC,EAAA,eACAA,EAAA,gBACEA,EAAA,kBAAsC,CAAC,GACjDA,EAAA,qBAAyB,CAAC,GAChBA,EAAA,mBAAc,IAiBhBA,EAAA,uBAiDAA,EAAA,wBAAoBC,GAA4B,CAClD,KAAK,aACP,KAAK,YAAYA,CAAK,CAE1B,GApEA,gBAAuB,OACjB,KAAK,cACTC,EAAA,KAAK,kBAAL,MAAAA,EAAA,UAAuB,CAGzB,iBAAmB,CAAA,CAEnB,WAA2C,CACzC,OAAO,KAAK,UAAA,CAIJ,eAAsB,CAC1B,KAAK,gBAAgB,KAAK,eAAe,CAAA,CAIxC,SAAS,CACd,MAAAC,EACA,OAAAC,EACA,cAAAC,CAAA,EACgC,OAChC,KAAK,OAASF,EACd,KAAK,QAAUC,EACV,KAAA,QAAQ,qBAAqB,KAAK,gBAAgB,EACvD,KAAK,eAAiBC,EACtB,KAAK,cAAc,EACd,KAAA,WAAW,IAAKC,GAASA,EAAK,SAASF,EAAQD,CAAK,CAAC,GAE1DD,EAAA,KAAK,YAAL,MAAAA,EAAA,UAAiB,CAKnB,IAAI,MAAO,CACH,MAAAK,EAAO,KAAK,OAAO,KAAK,EAC1B,GAAAA,EAAK,OAAS,EACFC,EAAAA,cAAAD,EAAK,CAAC,EAAE,YAAY,MAElC,OAAO,CAAC,EAEV,OAAOA,EAAK,IAAKD,GAASA,EAAK,YAAoC,CAAA,CAG9D,UAAW,QACXJ,EAAA,KAAA,UAAA,MAAAA,EAAS,uBAAuB,KAAK,kBAC1C,KAAK,OAAS,OACd,KAAK,QAAU,OACf,KAAK,WAAa,CAAC,EACnB,KAAK,eAAiB,OACtB,KAAK,YAAc,EAAA,CAGrB,IAAW,OAAmB,CACrB,OAAAM,EAAA,cAAc,KAAK,MAAM,CAAA,CAGlC,IAAW,QAAiC,CACnC,OAAAA,EAAA,cAAc,KAAK,OAAO,CAAA,CAYnC,WAAWC,EAA+B,CACjC,OAAAC,EAAA,aAAa,KAAK,cAAeC,EAAAA,WAAWF,CAAI,EAAIH,GACzDK,aAAWL,EAAK,IAAI,CACtB,CAAA,CAGF,WAA+B,CACtB,OAAA,KAAK,cAAc,GAAG,EAAE,CAAA,CAEnC,CAQO,MAAeM,CAItB,CAOE,YAAYC,EAAiC,CAN7Cb,EAAA,gBAEAA,EAAA,eAAyC,MACzCA,EAAA,iBAA8B,MAC9BA,EAAA,aAAwB,MAsCxBA,EAAA,mBAnCO,KAAA,QAAUc,EAAAA,MAAMC,EAAU,UAAA,KAAK,eAAgB,CAAA,EAAGF,GAAW,EAAE,CAAA,CAGtE,UAA0C,CACjC,OAAA,IAAA,CAGT,KAAKG,EAAiC,CAC/B,KAAK,WACL,KAAK,SAEVA,EAAO,yBAA0Bf,GAAU,OAAA,OAAAC,EAAA,KAAK,YAAL,YAAAA,EAAA,UAAiBD,GAAM,CAAA,CAGpE,eAAee,EAAiC,CACzC,KAAK,WACL,KAAK,SAEHA,EAAA,yBAA0Bf,GAAA,OAC/B,OAAAC,EAAA,KAAK,sBAAL,YAAAA,EAAA,UAA2BD,GAC7B,CAAA,CAGF,IAAI,MAAO,CACF,OAAAgB,EAAA,cAAc,KAAK,KAAK,CAAA,CAGjC,IAAI,QAAS,CACJ,OAAAA,EAAA,cAAc,KAAK,OAAO,CAAA,CAGnC,IAAI,UAAW,CACN,OAAAA,EAAA,cAAc,KAAK,SAAS,CAAA,CAIrC,IAAI,WAAY,CACd,OAAK,KAAK,kBAAiB,WAAa,KAAK,SAAS,UAAU,GACzD,KAAK,UAAA,CAGd,wBAAyB,CACjB,MAAAC,EAAQ,KAAK,UAAU,uBAAuB,EACpD,GAAKA,EACE,MAAA,CACL,KAAM,KAAK,MAAMA,EAAM,IAAI,EAC3B,GAAI,KAAK,KAAKA,EAAM,EAAE,CACxB,CAAA,CAGF,iBAAkB,CACT,OAAA,KAAK,UAAU,gBAAgB,CAAA,CAGxC,kBAAkBC,EAAoB,CAC7B,OAAA,KAAK,OAAO,kBAAkBA,CAAU,CAAA,CAEjD,kBAAkBC,EAAe,CACxB,OAAA,KAAK,OAAO,kBAAkBA,CAAK,CAAA,CAG5C,iBAAiBX,EAAY,CACpB,OAAA,KAAK,UAAU,iBAAiBA,CAAI,CAAA,CAG7C,SAASL,EAAgCiB,EAAqB,CAC5D,KAAK,QAAUjB,EACf,KAAK,UAAYiB,CAAA,CAGnB,OAAOd,EAAe,OACpB,KAAK,MAAQA,GACbL,EAAA,KAAK,UAAL,MAAAA,EAAA,UAAe,CAQnB"}