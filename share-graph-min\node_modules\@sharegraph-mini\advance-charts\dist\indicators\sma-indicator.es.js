var n = Object.defineProperty;
var m = (t, e, i) => e in t ? n(t, e, { enumerable: !0, configurable: !0, writable: !0, value: i }) : t[e] = i;
var o = (t, e, i) => m(t, typeof e != "symbol" ? e + "" : e, i);
import { SMA as l } from "technicalindicators";
import { ChartIndicator as p } from "./abstract-indicator.es.js";
import { SeriesPrimitiveBase as c } from "../custom-primitive/primitive-base.es.js";
import { LinePrimitivePaneView as u } from "../custom-primitive/pane-view/line.es.js";
const v = {
  color: "#2962ff",
  period: 9,
  overlay: !0
};
class h extends c {
  constructor(i) {
    super();
    o(this, "linePrimitive");
    this.source = i, this.linePrimitive = new u({
      lineColor: this.source.options.color
    }), this._paneViews = [this.linePrimitive];
  }
  update(i) {
    const r = [];
    for (const s of i) {
      const a = s.value;
      a && r.push({ time: s.time, price: a[0] });
    }
    this.linePrimitive.update(r);
  }
}
class x extends p {
  constructor() {
    super(...arguments);
    o(this, "smaPrimitive", new h(this));
  }
  getDefaultOptions() {
    return v;
  }
  _mainSeriesChanged(i) {
    i.attachPrimitive(this.smaPrimitive);
  }
  remove() {
    var i;
    super.remove(), (i = this.mainSeries) == null || i.detachPrimitive(this.smaPrimitive);
  }
  applyIndicatorData() {
    this.smaPrimitive.update(
      this._executionContext.data
    );
  }
  formula(i) {
    const r = i.new_var(i.symbol.close, this.options.period);
    return r.calculable() ? new l({
      values: r.getAll(),
      period: this.options.period
    }).getResult() : void 0;
  }
}
export {
  h as SMAPrimitive,
  x as default,
  v as defaultOptions
};
//# sourceMappingURL=sma-indicator.es.js.map
