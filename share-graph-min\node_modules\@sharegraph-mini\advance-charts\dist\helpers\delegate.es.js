var h = Object.defineProperty;
var o = (i, s, e) => s in i ? h(i, s, { enumerable: !0, configurable: !0, writable: !0, value: e }) : i[s] = e;
var n = (i, s, e) => o(i, typeof s != "symbol" ? s + "" : s, e);
class _ {
  constructor() {
    n(this, "_listeners", []);
    n(this, "_params", [void 0, void 0, void 0]);
  }
  fire(s, e, t) {
    this._params = [s, e, t];
    const r = [...this._listeners];
    this._listeners = this._listeners.filter((l) => !l.singleshot), r.forEach((l) => l.callback(s, e, t));
  }
  lastParams() {
    return this._params;
  }
  subscribe(s, e, t) {
    this._listeners.push({ callback: s, linkedObject: e, singleshot: !!t });
  }
  unsubscribe(s) {
    const e = this._listeners.findIndex((t) => t.callback === s);
    e > -1 && this._listeners.splice(e, 1);
  }
  unsubscribeAll(s) {
    this._listeners = this._listeners.filter((e) => e.linkedObject !== s);
  }
  hasListener() {
    return !!this._listeners.length;
  }
  destroy() {
    this._listeners = [];
  }
}
export {
  _ as Delegate
};
//# sourceMappingURL=delegate.es.js.map
