{"watch": ["app/src/**/*", "app/index.html", "app/vite.config.ts", "app/tsconfig.json", "packages/advance-charts/src/**/*"], "ext": "ts,tsx,js,jsx,json,html,css,scss,sass", "ignore": ["node_modules/**/*", "*/node_modules/**/*", "dist/**/*", "*/dist/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", ".turbo/**/*"], "exec": "cd app && npm run dev", "env": {"NODE_ENV": "development"}, "delay": 1000, "verbose": true, "restartable": "rs", "colours": true}