{"version": 3, "file": "time-key.es.js", "sources": ["../../src/advance-chart/time-key.ts"], "sourcesContent": ["import {Time, defaultHorzScaleBehavior} from \"lightweight-charts\"\r\nimport {Interval, Period} from \"./i-advance-chart\"\r\n\r\nconst horzScaleBehavior = new (defaultHorzScaleBehavior())\r\n\r\nconst MINUTE_IN_SECONDS = 60\r\nconst HOUR_IN_SECONDS = MINUTE_IN_SECONDS * 60\r\nconst DAY_IN_SECONDS = HOUR_IN_SECONDS * 24\r\nconst WEEK_IN_SECONDS = DAY_IN_SECONDS * 7\r\nconst START_OF_WEEK_OFFSET = DAY_IN_SECONDS * 3 // because timestamp 0 is 1/1/1970 which is a Thursday\r\n\r\nexport function timeKey(time: Time, interval: Interval) {\r\n  const unixTime = horzScaleBehavior.key(time)\r\n  const period = interval.period\r\n  const times = interval.times\r\n  \r\n  switch(period) {\r\n    case Period.minute: {\r\n      return unixTime - (unixTime % (MINUTE_IN_SECONDS * times))\r\n    }\r\n    case Period.hour: {\r\n      return unixTime - (unixTime % (HOUR_IN_SECONDS * times))\r\n    }\r\n    case Period.day: {\r\n        return unixTime - (unixTime % (DAY_IN_SECONDS * times))\r\n    }\r\n    case Period.week: {\r\n      const newUnixTime = unixTime + START_OF_WEEK_OFFSET\r\n      return unixTime - (newUnixTime % (WEEK_IN_SECONDS * times))\r\n    }\r\n    case Period.month: {\r\n      const monthDate = new Date(unixTime * 1000)\r\n      return Math.floor(Date.UTC(monthDate.getUTCFullYear(), monthDate.getUTCMonth(), 1, 0, 0, 0) / 1000)\r\n    }\r\n    default: \r\n      throw new Error('Invalid period')\r\n  }\r\n}   "], "names": ["horzScaleBehavior", "defaultHorzScaleBehavior", "MINUTE_IN_SECONDS", "HOUR_IN_SECONDS", "DAY_IN_SECONDS", "WEEK_IN_SECONDS", "START_OF_WEEK_OFFSET", "<PERSON><PERSON><PERSON>", "time", "interval", "unixTime", "period", "times", "Period", "newUnixTime", "monthDate"], "mappings": ";;AAGA,MAAMA,IAAoB,KAAKC,KAAyB,GAElDC,IAAoB,IACpBC,IAAkBD,IAAoB,IACtCE,IAAiBD,IAAkB,IACnCE,IAAkBD,IAAiB,GACnCE,IAAuBF,IAAiB;AAE9B,SAAAG,EAAQC,GAAYC,GAAoB;AAChD,QAAAC,IAAWV,EAAkB,IAAIQ,CAAI,GACrCG,IAASF,EAAS,QAClBG,IAAQH,EAAS;AAEvB,UAAOE,GAAQ;AAAA,IACb,KAAKE,EAAO;AACH,aAAAH,IAAYA,KAAYR,IAAoBU;AAAA,IAErD,KAAKC,EAAO;AACH,aAAAH,IAAYA,KAAYP,IAAkBS;AAAA,IAEnD,KAAKC,EAAO;AACD,aAAAH,IAAYA,KAAYN,IAAiBQ;AAAA,IAEpD,KAAKC,EAAO,MAAM;AAChB,YAAMC,IAAcJ,IAAWJ;AACxB,aAAAI,IAAYI,KAAeT,IAAkBO;AAAA,IAAA;AAAA,IAEtD,KAAKC,EAAO,OAAO;AACjB,YAAME,IAAY,IAAI,KAAKL,IAAW,GAAI;AAC1C,aAAO,KAAK,MAAM,KAAK,IAAIK,EAAU,eAAe,GAAGA,EAAU,YAAA,GAAe,GAAG,GAAG,GAAG,CAAC,IAAI,GAAI;AAAA,IAAA;AAAA,IAEpG;AACQ,YAAA,IAAI,MAAM,gBAAgB;AAAA,EAAA;AAEtC;"}