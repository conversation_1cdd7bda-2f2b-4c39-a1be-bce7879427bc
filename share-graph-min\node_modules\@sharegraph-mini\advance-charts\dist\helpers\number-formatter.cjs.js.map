{"version": 3, "file": "number-formatter.cjs.js", "sources": ["../../src/helpers/number-formatter.ts"], "sourcesContent": ["export type INumberParam = number | undefined | null\r\n\r\nexport class NumberFormatter {\r\n  constructor(public locale: string = 'en-gb'){}\r\n\r\n  private _volumeFormatter: Intl.NumberFormat | undefined;\r\n  get volumeFormatter() {\r\n    if(!this._volumeFormatter) {\r\n      this._volumeFormatter = new Intl.NumberFormat(this.locale, { notation: 'compact', compactDisplay: 'short', maximumFractionDigits: 2})\r\n    }\r\n\r\n    return this._volumeFormatter\r\n  }\r\n\r\n  private _decimalFormatter: Intl.NumberFormat | undefined;\r\n  get decimalFormatter() {\r\n    if(!this._decimalFormatter) {\r\n      this._decimalFormatter = new Intl.NumberFormat(this.locale)\r\n    }\r\n\r\n    return this._decimalFormatter\r\n  }\r\n\r\n  private _percentFormatter: Intl.NumberFormat | undefined;\r\n  get percentFormatter() {\r\n    if(!this._percentFormatter) {\r\n      this._percentFormatter = new Intl.NumberFormat(this.locale, { \r\n        style: \"percent\",\r\n        minimumFractionDigits: 2, // Ensure 2 decimal places\r\n        maximumFractionDigits: 2, // Restrict to 2 decimal places\r\n      })\r\n    }\r\n\r\n    return this._percentFormatter\r\n  }\r\n\r\n  volume(num: INumberParam) {\r\n    if(num == null) return ''\r\n    if(Number.isNaN(num)) return \"NaN\"\r\n    return this.volumeFormatter.format(num)\r\n  }\r\n\r\n  decimal(num: INumberParam) {\r\n    if(num == null) return ''\r\n    if(Number.isNaN(num)) return \"NaN\"\r\n    return this.decimalFormatter.format(num)\r\n  }\r\n\r\n  percent(num: INumberParam) {\r\n    if(num == null) return ''\r\n    if(Number.isNaN(num)) return \"NaN\"\r\n    return this.percentFormatter.format(num)\r\n  }\r\n}\r\n\r\nexport const NumberFormatterFactory = {\r\n  _cache: new Map<string, NumberFormatter>(),\r\n\r\n  formatter(locale: string) {\r\n    if(this._cache.has(locale)) this._cache.get(locale) as NumberFormatter;\r\n\r\n    const instance = new NumberFormatter(locale);\r\n    this._cache.set(locale, instance)\r\n    return instance;\r\n  }\r\n}\r\n\r\n\r\n"], "names": ["NumberFormatter", "locale", "__publicField", "num", "NumberFormatterFactory", "instance"], "mappings": "oPAEO,MAAMA,CAAgB,CAC3B,YAAmBC,EAAiB,QAAQ,CAEpCC,EAAA,yBASAA,EAAA,0BASAA,EAAA,0BApBW,KAAA,OAAAD,CAAA,CAGnB,IAAI,iBAAkB,CACjB,OAAC,KAAK,mBACP,KAAK,iBAAmB,IAAI,KAAK,aAAa,KAAK,OAAQ,CAAE,SAAU,UAAW,eAAgB,QAAS,sBAAuB,EAAE,GAG/H,KAAK,gBAAA,CAId,IAAI,kBAAmB,CAClB,OAAC,KAAK,oBACP,KAAK,kBAAoB,IAAI,KAAK,aAAa,KAAK,MAAM,GAGrD,KAAK,iBAAA,CAId,IAAI,kBAAmB,CAClB,OAAC,KAAK,oBACP,KAAK,kBAAoB,IAAI,KAAK,aAAa,KAAK,OAAQ,CAC1D,MAAO,UACP,sBAAuB,EACvB,sBAAuB,CAAA,CACxB,GAGI,KAAK,iBAAA,CAGd,OAAOE,EAAmB,CACrB,OAAAA,GAAO,KAAa,GACpB,OAAO,MAAMA,CAAG,EAAU,MACtB,KAAK,gBAAgB,OAAOA,CAAG,CAAA,CAGxC,QAAQA,EAAmB,CACtB,OAAAA,GAAO,KAAa,GACpB,OAAO,MAAMA,CAAG,EAAU,MACtB,KAAK,iBAAiB,OAAOA,CAAG,CAAA,CAGzC,QAAQA,EAAmB,CACtB,OAAAA,GAAO,KAAa,GACpB,OAAO,MAAMA,CAAG,EAAU,MACtB,KAAK,iBAAiB,OAAOA,CAAG,CAAA,CAE3C,CAEO,MAAMC,EAAyB,CACpC,WAAY,IAEZ,UAAUH,EAAgB,CACrB,KAAK,OAAO,IAAIA,CAAM,GAAQ,KAAA,OAAO,IAAIA,CAAM,EAE5C,MAAAI,EAAW,IAAIL,EAAgBC,CAAM,EACtC,YAAA,OAAO,IAAIA,EAAQI,CAAQ,EACzBA,CAAA,CAEX"}