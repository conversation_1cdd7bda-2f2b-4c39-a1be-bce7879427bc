{"version": 3, "file": "momentum-indicator.cjs.js", "sources": ["../../src/indicators/momentum-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface MomentumIndicatorOptions extends ChartIndicatorOptions {\r\n  period: number,\r\n  usePercentage: boolean,\r\n  color: string,\r\n  zeroLineColor: string\r\n};\r\n\r\nexport const defaultOptions: MomentumIndicatorOptions = {\r\n  period: 14,\r\n  usePercentage: false,\r\n  color: '#2b97f1',\r\n  zeroLineColor: '#808080',\r\n  overlay: false\r\n}\r\n\r\nexport type MomentumData = readonly [Nominal<number, 'Momentum'>]\r\n\r\nexport default class MomentumIndicator extends ChartIndicator<MomentumIndicatorOptions, MomentumData> {\r\n  momentumSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<MomentumIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options);\r\n\r\n    this.momentumSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 2,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'momentum'\r\n    }, paneIndex)\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n    const momentumData: SingleValueData[] = []\r\n\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      const time = bar.time as Time;\r\n\r\n      if(!value) continue;\r\n      const [momentum] = value;\r\n      if(!isNaN(momentum)) momentumData.push({time, value: momentum})\r\n    }\r\n\r\n    this.momentumSeries.setData(momentumData)\r\n  }\r\n\r\n  formula(c: Context) {\r\n      const period = this.options.period;\r\n      const currentClose = c.symbol.close;\r\n\r\n      const closeSeries = c.new_var(currentClose, period + 1);\r\n      if (!closeSeries.calculable()) return;\r\n\r\n      const closeNPeriodsAgo = closeSeries.get(period);\r\n\r\n      let momentum: number;\r\n\r\n      if (this.options.usePercentage) {\r\n          momentum = ((currentClose - closeNPeriodsAgo) / closeNPeriodsAgo) * 100;\r\n      } else {\r\n          momentum = currentClose - closeNPeriodsAgo;\r\n      }\r\n\r\n      return [momentum as Nominal<number, 'Momentum'>] as MomentumData;\r\n  }\r\n\r\n  _applyOptions(options: Partial<MomentumIndicatorOptions>): void {\r\n    if(options.period || options.usePercentage) {\r\n      this.calcIndicatorData()\r\n    }\r\n\r\n    if(options.color) this.momentumSeries.applyOptions({color: options.color})\r\n\r\n    this.applyIndicatorData()\r\n  }\r\n\r\n  getDefaultOptions() {\r\n    return defaultOptions\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove()\r\n    this.chart.removeSeries(this.momentumSeries)\r\n  }\r\n\r\n  setPaneIndex(paneIndex: number): void {\r\n    this.momentumSeries.moveToPane(paneIndex);\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.momentumSeries.getPane().paneIndex()\r\n  }\r\n}\r\n"], "names": ["defaultOptions", "MomentumIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "momentumData", "bar", "value", "time", "momentum", "c", "period", "currentClose", "closeSeries", "closeNPeriodsAgo"], "mappings": "+VAWaA,EAA2C,CACtD,OAAQ,GACR,cAAe,GACf,MAAO,UACP,cAAe,UACf,QAAS,EACX,EAIA,MAAqBC,UAA0BC,EAAAA,cAAuD,CAGpG,YAAYC,EAAkBC,EAA6CC,EAAoB,CAC7F,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,uBAKO,KAAA,eAAiBH,EAAM,UAAUI,EAAAA,WAAY,CAChD,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,YACbF,CAAS,CAAA,CAGd,oBAA2B,CACzB,MAAMG,EAAkC,CAAC,EAE/B,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MACZE,EAAOF,EAAI,KAEjB,GAAG,CAACC,EAAO,SACL,KAAA,CAACE,CAAQ,EAAIF,EACf,MAAME,CAAQ,GAAGJ,EAAa,KAAK,CAAC,KAAAG,EAAM,MAAOC,EAAS,CAAA,CAG3D,KAAA,eAAe,QAAQJ,CAAY,CAAA,CAG1C,QAAQK,EAAY,CACV,MAAAC,EAAS,KAAK,QAAQ,OACtBC,EAAeF,EAAE,OAAO,MAExBG,EAAcH,EAAE,QAAQE,EAAcD,EAAS,CAAC,EAClD,GAAA,CAACE,EAAY,aAAc,OAEzB,MAAAC,EAAmBD,EAAY,IAAIF,CAAM,EAE3C,IAAAF,EAEA,OAAA,KAAK,QAAQ,cACAA,GAAAG,EAAeE,GAAoBA,EAAoB,IAEpEL,EAAWG,EAAeE,EAGvB,CAACL,CAAuC,CAAA,CAGnD,cAAcR,EAAkD,EAC3DA,EAAQ,QAAUA,EAAQ,gBAC3B,KAAK,kBAAkB,EAGtBA,EAAQ,OAAY,KAAA,eAAe,aAAa,CAAC,MAAOA,EAAQ,MAAM,EAEzE,KAAK,mBAAmB,CAAA,CAG1B,mBAAoB,CACX,OAAAJ,CAAA,CAGT,QAAe,CACb,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,cAAc,CAAA,CAG7C,aAAaK,EAAyB,CAC/B,KAAA,eAAe,WAAWA,CAAS,CAAA,CAG1C,cAAuB,CACrB,OAAO,KAAK,eAAe,QAAQ,EAAE,UAAU,CAAA,CAEnD"}