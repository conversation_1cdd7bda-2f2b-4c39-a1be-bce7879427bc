import { CanvasRenderingTarget2D } from 'fancy-canvas';
import { PrimitivePaneViewBase } from '../primitive-base';
import { Time } from 'lightweight-charts';

export interface BandPrimitivePaneViewOptions {
    backgroundColor: string;
}
export declare const BandPrimitiveOptionsDefault: BandPrimitivePaneViewOptions;
export type BandPrimitiveData = {
    time: Time;
    upper: number;
    lower: number;
};
export declare class BandPrimitivePaneView extends PrimitivePaneViewBase<BandPrimitivePaneViewOptions, BandPrimitiveData> {
    dataVisible(): BandPrimitiveData[];
    drawBackground(target: CanvasRenderingTarget2D): void;
    defaultOptions(): BandPrimitivePaneViewOptions;
}
