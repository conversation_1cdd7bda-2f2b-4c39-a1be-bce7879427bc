import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface MACDIndicatorOptions extends ChartIndicatorOptions {
    fastPeriod: number;
    slowPeriod: number;
    signalPeriod: number;
    SimpleMAOscillator: boolean;
    SimpleMASignal: boolean;
    upColor: string;
    downColor: string;
    macdLineColor: string;
    signalLineColor: string;
}
export declare const defaultOptions: MACDIndicatorOptions;
export type MacdMACDData = Nominal<number, 'Macd'>;
export type SignalMACDData = Nominal<number, 'Signal'>;
export type HistogramMACDData = Nominal<number, 'Histogram'>;
export type MACDData = [MacdMACDData, SignalMACDData, HistogramMACDData];
export default class MACDIndicator extends ChartIndicator<MACDIndicatorOptions, MACDData> {
    histogramSeries: ISeriesApi<'Histogram'>;
    macdSeries: ISeriesApi<SeriesType>;
    signalSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<MACDIndicatorOptions>, paneIndex?: number);
    applyIndicatorData(): void;
    formula(c: Context): MACDData | undefined;
    _applyOptions(options: Partial<MACDIndicatorOptions>): void;
    getDefaultOptions(): MACDIndicatorOptions;
    remove(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
