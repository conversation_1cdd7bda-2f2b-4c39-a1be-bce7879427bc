import { ISeriesApi, Nominal, SeriesType, WhitespaceData } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { SeriesPrimitiveBase } from '../custom-primitive/primitive-base';
import { BandPrimitivePaneView } from '../custom-primitive/pane-view/band';
import { LinePrimitivePaneView } from '../custom-primitive/pane-view/line';
import { Context, IIndicatorBar } from '../helpers/execution-indicator';

export type UpperBBData = Nominal<number, 'Upper'>;
export type MiddleBBData = Nominal<number, 'Middle'>;
export type LowerBBData = Nominal<number, 'Lower'>;
export type BBData = [UpperBBData, MiddleBBData, LowerBBData];
export interface BBIndicatorOptions extends ChartIndicatorOptions {
    backgroundColor: string;
    upperLineColor: string;
    middleLineColor: string;
    lowerLineColor: string;
    period: number;
    stdDev: number;
}
export declare const defaultOptions: BBIndicatorOptions;
export type BBIndicatorData = WhitespaceData & {
    middle?: number;
    upper?: number;
    lower?: number;
    pb?: number;
};
export declare class BBPrimitive extends SeriesPrimitiveBase<BBIndicatorData> {
    protected source: BBIndicator;
    bandPaneView: BandPrimitivePaneView;
    upperPaneView: LinePrimitivePaneView;
    middlePaneView: LinePrimitivePaneView;
    lowerPaneView: LinePrimitivePaneView;
    constructor(source: BBIndicator);
    update(data: IIndicatorBar<BBData>[]): void;
}
export default class BBIndicator extends ChartIndicator<BBIndicatorOptions, BBData> {
    bbPrimitive: BBPrimitive;
    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void;
    _applyOptions(options: Partial<BBIndicatorOptions>): void;
    applyIndicatorData(): void;
    formula(c: Context): BBData | undefined;
    remove(): void;
    getDefaultOptions(): BBIndicatorOptions;
}
