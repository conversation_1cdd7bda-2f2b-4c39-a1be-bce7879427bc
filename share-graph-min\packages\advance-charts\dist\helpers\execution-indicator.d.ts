import { OHLCVSimple } from '../interface';

export interface ISymbol extends Omit<OHLCVSimple, 'time'> {
    time: number;
    isNew: boolean;
}
export type ISymbolData = Omit<ISymbol, 'isNew'>;
export interface IIndicatorBar<D extends readonly number[]> {
    time: number;
    value?: D;
}
export interface IVar {
    /**
     * Gets the historical value at the specified depth.
     *
     * Depth represents how many periods back to look:
     * - depth=0: current/most recent value
     * - depth=1: previous value
     * - depth=N: value from N periods ago
     *
     * @param depth - Number of periods to look back (0-based)
     * @returns The value at requested depth or NaN if insufficient history
     */
    get(depth: number): number;
    /**
     * Sets the current value (depth=0) and shifts historical values.
     *
     * When set, the value becomes the new current value (depth=0),
     * and all previous values shift deeper (depth increases by 1).
     *
     * @param num - The new current value to set
     */
    set(num: number): void;
    /**
     * Gets all historical values in depth order (newest first).
     *
     * The array represents the complete history buffer where:
     * - index 0: current value (depth=0)
     * - index 1: previous value (depth=1)
     * - index N: value from N periods ago (depth=N)
     *
     * @returns Array of historical values sorted by depth (shallow to deep)
     */
    getAll(): number[];
    /**
     * Checks if sufficient historical depth is available for calculations.
     *
     * A variable is calculable when it has collected enough historical
     * values to satisfy its minimum depth requirement.
     *
     * @returns True if minimum required depth is available
     */
    calculable(): boolean;
    /**
     * Prepares the variable for a new data point.
     *
     * Called when processing new market data. Resets internal state
     * and updates the variable based on the new symbol information.
     *
     * @param d - The new symbol data point
     */
    prepare(d: ISymbol): void;
    valueOf(): number;
}
export interface IContext {
    new_var(value: number, depth: number): IVar;
    prepare(d: ISymbol): void;
    symbol: ISymbol;
}
export declare class Context implements IContext {
    _varIndex: number;
    _vars: IVar[];
    symbol: ISymbol;
    new_var(value: number, depth: number): IVar;
    prepare(d: ISymbol): void;
}
export declare class Var implements IVar {
    _his: Array<number> | null;
    _hisPosition: number;
    _minDepth: number;
    origin: number;
    modified: boolean;
    constructor(depth: number);
    valueOf(): number;
    get(depth: number): number;
    getAll(): number[];
    calculable(): boolean;
    set(num: number): void;
    prepare(d: ISymbol): void;
    private addHist;
}
/**
 * Execution context for calculating technical indicators.
 *
 * Key points:
 * - Must process data points in chronological order (time ascending)
 * - Maintains calculation context between points
 * - Supports two operations:
 *   1. Full recalculation (recalc) - processes entire dataset
 *   2. Update (update) - processes only the last/new point
 *
 * Important:
 * - Cannot update random points - will break calculation context
 * - Only supports appending new points or updating the last point
 */
export declare class ExecutionContext<T extends readonly number[]> {
    protected formula: (c: Context) => IIndicatorBar<T>['value'] | undefined;
    data: IIndicatorBar<T>[];
    _context: Context;
    _isCalc: boolean;
    constructor(formula: (c: Context) => IIndicatorBar<T>['value'] | undefined);
    private init;
    private calcLasPoint;
    recalc(data: ISymbolData[]): void;
    update(data: ISymbolData): void;
}
