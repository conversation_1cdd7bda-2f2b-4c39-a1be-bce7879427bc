export declare const parseColor: ((color: string) => number[]) & {
    cache: import('es-toolkit').MemoizeCache<any, number[]>;
};
export declare class Color {
    /**
    * We fallback to RGBA here since supporting alpha transformations
    * on wider color gamuts would currently be a lot of extra code
    * for very little benefit due to actual usage.
    */
    static applyAlpha(color: string, alpha: number): string;
    static parseColor(color: string): number[];
}
