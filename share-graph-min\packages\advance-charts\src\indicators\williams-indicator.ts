import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {WilliamsR} from "technicalindicators";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface WilliamsIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  period: number
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: WilliamsIndicatorOptions = {
  color: "rgba(108, 80, 175, 1)",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: '#7e57c21a',
  period: 14,
  overlay: false
}

export type WilliamsLine = Nominal<number, 'Williams'>

export type WilliamsData = [WilliamsLine]

export default class WilliamsIndicator extends ChartIndicator<WilliamsIndicatorOptions, WilliamsData> {
  williamsSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<WilliamsIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    this.williamsSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'williams',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 0, minValue: -100})
    }, paneIndex);

    this.williamsSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: -20,
        lowPrice: -80,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): WilliamsIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): WilliamsData | undefined {
    const highSeries = c.new_var(c.symbol.high, this.options.period);
    const lowSeries = c.new_var(c.symbol.low, this.options.period);
    const closeSeries = c.new_var(c.symbol.close, this.options.period);

    if(!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) return;

    const williamsR = new WilliamsR({
      period: this.options.period,
      high: highSeries.getAll(),
      low: lowSeries.getAll(),
      close: closeSeries.getAll()
    });

    const result = williamsR.getResult();
    if (result.length === 0) return;

    return [result[result.length - 1] as WilliamsLine];
  }


  applyIndicatorData() {
    const williamsData: SingleValueData[] = [];
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      williamsData.push({time: bar.time as Time, value: value[0]})
    }

    this.williamsSeries.setData(williamsData)
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.williamsSeries);
  }

  _applyOptions() {
    this.williamsSeries.applyOptions({color: this.options.color})
    this.applyIndicatorData();
  }


  setPaneIndex(paneIndex: number) {
    this.williamsSeries.moveToPane(paneIndex)
  }

  getPaneIndex(): number {
    return this.williamsSeries.getPane().paneIndex()
  }
}