var g = Object.defineProperty;
var d = (e, t, r) => t in e ? g(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : e[t] = r;
var o = (e, t, r) => d(e, typeof t != "symbol" ? t + "" : t, r);
import { merge as l } from "es-toolkit";
class y {
  static registerIndicator(t, r, i) {
    this.registry.set(t, [r, i]);
  }
  static indicatorRegistered(t) {
    return this.registry.has(t);
  }
  static createIndicator(t, r, i, c) {
    const s = this.registry.get(t);
    if (!s)
      throw new Error(`Indicator "${t}" not registered. Available: ${Array.from(this.registry.keys()).join(", ")}`);
    const [n, a] = s;
    return new n(r, i || a ? l(structuredClone(a) ?? {}, i ?? {}) : void 0, c);
  }
}
o(y, "registry", /* @__PURE__ */ new Map());
export {
  y as IndicatorFactory
};
//# sourceMappingURL=indicator-factory.es.js.map
