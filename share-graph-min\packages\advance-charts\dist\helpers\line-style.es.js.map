{"version": 3, "file": "line-style.es.js", "sources": ["../../src/helpers/line-style.ts"], "sourcesContent": ["import {LineStyle} from \"lightweight-charts\";\r\n\r\nexport function setLineStyle(ctx: CanvasRenderingContext2D, style: LineStyle): void {\r\n\tconst dashPatterns = {\r\n\t\t[LineStyle.Solid]: [],\r\n\t\t[LineStyle.Dotted]: [ctx.lineWidth, ctx.lineWidth],\r\n\t\t[LineStyle.Dashed]: [2 * ctx.lineWidth, 2 * ctx.lineWidth],\r\n\t\t[LineStyle.LargeDashed]: [6 * ctx.lineWidth, 6 * ctx.lineWidth],\r\n\t\t[LineStyle.SparseDotted]: [ctx.lineWidth, 4 * ctx.lineWidth],\r\n\t};\r\n\r\n\tconst dashPattern = dashPatterns[style];\r\n\tctx.setLineDash(dashPattern);\r\n}"], "names": ["setLineStyle", "ctx", "style", "dashPattern", "LineStyle"], "mappings": ";AAEgB,SAAAA,EAAaC,GAA+BC,GAAwB;AAS7E,QAAAC,IARe;AAAA,IACpB,CAACC,EAAU,KAAK,GAAG,CAAC;AAAA,IACpB,CAACA,EAAU,MAAM,GAAG,CAACH,EAAI,WAAWA,EAAI,SAAS;AAAA,IACjD,CAACG,EAAU,MAAM,GAAG,CAAC,IAAIH,EAAI,WAAW,IAAIA,EAAI,SAAS;AAAA,IACzD,CAACG,EAAU,WAAW,GAAG,CAAC,IAAIH,EAAI,WAAW,IAAIA,EAAI,SAAS;AAAA,IAC9D,CAACG,EAAU,YAAY,GAAG,CAACH,EAAI,WAAW,IAAIA,EAAI,SAAS;AAAA,EAC5D,EAEiCC,CAAK;AACtC,EAAAD,EAAI,YAAYE,CAAW;AAC5B;"}