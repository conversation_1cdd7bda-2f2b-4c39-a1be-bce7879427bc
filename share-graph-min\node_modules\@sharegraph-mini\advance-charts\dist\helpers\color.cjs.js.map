{"version": 3, "file": "color.cjs.js", "sources": ["../../src/helpers/color.ts"], "sourcesContent": ["import {memoize} from \"es-toolkit\";\r\n\r\n/**\r\n * For colors which fall within the sRGB space, the browser can\r\n * be used to convert the color string into a rgb /rgba string.\r\n *\r\n * For other colors, it will be returned as specified (i.e. for\r\n * newer formats like display-p3)\r\n *\r\n * See: https://www.w3.org/TR/css-color-4/#serializing-sRGB-values\r\n */\r\nfunction getRgbStringViaBrowser(color: string): string {\r\n\tconst element = document.createElement('div');\r\n\telement.style.display = 'none';\r\n\t// We append to the body as it is the most reliable way to get a color reading\r\n\t// appending to the chart container or similar element can result in the following\r\n\t// getComputedStyle returning empty strings on each check.\r\n\tdocument.body.appendChild(element);\r\n\telement.style.color = color;\r\n\tconst computed = window.getComputedStyle(element).color;\r\n\tdocument.body.removeChild(element);\r\n\treturn computed;\r\n}\r\n\r\nexport const parseColor = memoize((color: string) => {\r\n  const computed = getRgbStringViaBrowser(color);\r\n\r\n  const match = computed.match(\r\n    /^rgba?\\s*\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d+))?\\)$/\r\n  );\r\n\r\n  if (!match) {\r\n    throw new Error(`Failed to parse color: ${color}`);\r\n  }\r\n\r\n  return [\r\n    parseInt(match[1], 10) as number,\r\n    parseInt(match[2], 10) as number,\r\n    parseInt(match[3], 10) as number,\r\n    (match[4] ? parseFloat(match[4]) : 1) as number,\r\n  ];\r\n})\r\n\r\nexport class Color {\r\n  /**\r\n  * We fallback to RGBA here since supporting alpha transformations\r\n  * on wider color gamuts would currently be a lot of extra code\r\n  * for very little benefit due to actual usage.\r\n  */\r\n  static applyAlpha(color: string, alpha: number): string {\r\n   // special case optimization\r\n   if (color === 'transparent') {\r\n     return color;\r\n   }\r\n   if(alpha === 1) return color;\r\n\r\n   const originRgba = parseColor(color);\r\n   const originAlpha = originRgba[3];\r\n   return `rgba(${originRgba[0]}, ${originRgba[1]}, ${originRgba[2]}, ${\r\n     alpha * originAlpha\r\n   })`;\r\n }\r\n\r\n static parseColor(color: string) {\r\n  return parseColor(color)\r\n }\r\n}\r\n"], "names": ["getRgbStringViaBrowser", "color", "element", "computed", "parseColor", "memoize", "match", "Color", "alpha", "originRgba", "originAlpha"], "mappings": "8GAWA,SAASA,EAAuBC,EAAuB,CAChD,MAAAC,EAAU,SAAS,cAAc,KAAK,EAC5CA,EAAQ,MAAM,QAAU,OAIf,SAAA,KAAK,YAAYA,CAAO,EACjCA,EAAQ,MAAM,MAAQD,EACtB,MAAME,EAAW,OAAO,iBAAiBD,CAAO,EAAE,MACzC,gBAAA,KAAK,YAAYA,CAAO,EAC1BC,CACR,CAEa,MAAAC,EAAaC,EAAAA,QAASJ,GAAkB,CAGnD,MAAMK,EAFWN,EAAuBC,CAAK,EAEtB,MACrB,2DACF,EAEA,GAAI,CAACK,EACH,MAAM,IAAI,MAAM,0BAA0BL,CAAK,EAAE,EAG5C,MAAA,CACL,SAASK,EAAM,CAAC,EAAG,EAAE,EACrB,SAASA,EAAM,CAAC,EAAG,EAAE,EACrB,SAASA,EAAM,CAAC,EAAG,EAAE,EACpBA,EAAM,CAAC,EAAI,WAAWA,EAAM,CAAC,CAAC,EAAI,CACrC,CACF,CAAC,EAEM,MAAMC,CAAM,CAMjB,OAAO,WAAWN,EAAeO,EAAuB,CAKpD,GAHCP,IAAU,eAGXO,IAAU,EAAU,OAAAP,EAEjB,MAAAQ,EAAaL,EAAWH,CAAK,EAC7BS,EAAcD,EAAW,CAAC,EAChC,MAAO,QAAQA,EAAW,CAAC,CAAC,KAAKA,EAAW,CAAC,CAAC,KAAKA,EAAW,CAAC,CAAC,KAC9DD,EAAQE,CACV,GAAA,CAGF,OAAO,WAAWT,EAAe,CAChC,OAAOG,EAAWH,CAAK,CAAA,CAEzB"}