{"version": 3, "file": "bb-indicator.es.js", "sources": ["../../src/indicators/bb-indicator.ts"], "sourcesContent": ["import {\r\n  ISeriesApi,\r\n  Nominal,\r\n  SeriesType,\r\n  Time,\r\n  WhitespaceData,\r\n} from \"lightweight-charts\";\r\nimport { ChartIndicator, ChartIndicatorOptions } from \"./abstract-indicator\";\r\nimport { BollingerBands } from \"technicalindicators\";\r\nimport {SeriesPrimitiveBase} from \"../custom-primitive/primitive-base\";\r\nimport {BandPrimitivePaneView} from \"../custom-primitive/pane-view/band\";\r\nimport {LinePrimitivePaneView} from \"../custom-primitive/pane-view/line\";\r\nimport {Context, IIndicatorBar} from \"../helpers/execution-indicator\";\r\nimport type {BollingerBandsOutput} from \"technicalindicators/declarations/volatility/BollingerBands\";\r\n\r\nexport type UpperBBData = Nominal<number, 'Upper'>\r\nexport type MiddleBBData = Nominal<number, 'Middle'>\r\nexport type LowerBBData = Nominal<number, 'Lower'>\r\nexport type BBData = [UpperBBData, MiddleBBData, LowerBBData]\r\n\r\nexport interface BBIndicatorOptions extends ChartIndicatorOptions {\r\n  backgroundColor: string;\r\n  upperLineColor: string;\r\n  middleLineColor: string;\r\n  lowerLineColor: string;\r\n  period: number;\r\n  stdDev: number;\r\n};\r\n\r\nexport const defaultOptions: BBIndicatorOptions = {\r\n  backgroundColor: \"#2196f312\",\r\n  upperLineColor: \"#2196f3\",\r\n  middleLineColor: \"#ff6d00\",\r\n  lowerLineColor: \"#2196f3\",\r\n  period: 20,\r\n  stdDev: 2,\r\n  overlay: true\r\n};\r\n\r\n\r\nexport type BBIndicatorData = WhitespaceData & {\r\n  middle?: number;\r\n  upper?: number;\r\n  lower?: number;\r\n  pb?: number;\r\n}\r\n\r\nexport class BBPrimitive extends SeriesPrimitiveBase<BBIndicatorData> {\r\n  bandPaneView: BandPrimitivePaneView;\r\n  upperPaneView: LinePrimitivePaneView;\r\n  middlePaneView: LinePrimitivePaneView;\r\n  lowerPaneView: LinePrimitivePaneView;\r\n\r\n  constructor(protected source: BBIndicator) {\r\n    super();\r\n    this.bandPaneView = new BandPrimitivePaneView({\r\n      backgroundColor: this.source.options.backgroundColor,\r\n    });\r\n    this.upperPaneView = new LinePrimitivePaneView({\r\n      lineWidth: 1,\r\n      lineColor: this.source.options.upperLineColor,\r\n    });\r\n    this.middlePaneView = new LinePrimitivePaneView({\r\n      lineWidth: 1,\r\n      lineColor: this.source.options.middleLineColor,\r\n    });\r\n    this.lowerPaneView = new LinePrimitivePaneView({\r\n      lineWidth: 1,\r\n      lineColor: this.source.options.lowerLineColor,\r\n    });\r\n    this._paneViews = [\r\n      this.bandPaneView,\r\n      this.upperPaneView,\r\n      this.middlePaneView,\r\n      this.lowerPaneView\r\n    ];\r\n  }\r\n\r\n  update(data: IIndicatorBar<BBData>[]) {\r\n    const definedData: Array<{time: Time, value: BBData}> = [];\r\n\r\n    for(const bar of data) {\r\n      const value = bar.value;\r\n      if(!value) continue;\r\n      definedData.push({time: bar.time as Time, value})\r\n    }\r\n\r\n    this.bandPaneView.update(\r\n      definedData.map((item) => ({\r\n        time: item.time,\r\n        upper: item.value[0],\r\n        lower: item.value[2],\r\n      }))\r\n    );\r\n    this.upperPaneView.update(\r\n      definedData.map((item) => ({\r\n        time: item.time,\r\n        price: item.value[0],\r\n      }))\r\n    );\r\n    this.middlePaneView.update(\r\n      definedData.map((item) => ({\r\n        time: item.time,\r\n        price: item.value[1],\r\n      }))\r\n    );\r\n    this.lowerPaneView.update(\r\n      definedData.map((item) => ({\r\n        time: item.time,\r\n        price: item.value[2],\r\n      }))\r\n    );\r\n  }\r\n}\r\n\r\nexport default class BBIndicator extends ChartIndicator<BBIndicatorOptions, BBData> {\r\n  bbPrimitive = new BBPrimitive(this)\r\n  _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {\r\n    series.attachPrimitive(this.bbPrimitive)\r\n  }\r\n\r\n  _applyOptions(options: Partial<BBIndicatorOptions>): void {\r\n    if(options.period || options.stdDev) {\r\n      this.applyIndicatorData()\r\n    }\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n      this.bbPrimitive.update(this._executionContext.data)\r\n  }\r\n\r\n  formula(c: Context): BBData | undefined {\r\n    const closeSeries = c.new_var(c.symbol.close, this.options.period);\r\n    if(!closeSeries.calculable()) return;\r\n\r\n    const result = new BollingerBands({\r\n      values: closeSeries.getAll(),\r\n      period: this.options.period,\r\n      stdDev: this.options.stdDev,\r\n    });\r\n\r\n    const item = result.getResult().at(0) as BollingerBandsOutput\r\n    \r\n    return [item.upper as UpperBBData, item.middle as MiddleBBData, item.lower as LowerBBData]\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove()\r\n    this.mainSeries?.detachPrimitive(this.bbPrimitive)\r\n  }\r\n\r\n  getDefaultOptions(): BBIndicatorOptions {\r\n    return defaultOptions;\r\n  }\r\n}\r\n"], "names": ["defaultOptions", "BBPrimitive", "SeriesPrimitiveBase", "source", "__publicField", "BandPrimitivePaneView", "LinePrimitivePaneView", "data", "definedData", "bar", "value", "item", "BBIndicator", "ChartIndicator", "series", "options", "c", "closeSeries", "BollingerBands", "_a"], "mappings": ";;;;;;;;AA6BO,MAAMA,IAAqC;AAAA,EAChD,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AACX;AAUO,MAAMC,UAAoBC,EAAqC;AAAA,EAMpE,YAAsBC,GAAqB;AACnC,UAAA;AANR,IAAAC,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA;AAEsB,SAAA,SAAAD,GAEf,KAAA,eAAe,IAAIE,EAAsB;AAAA,MAC5C,iBAAiB,KAAK,OAAO,QAAQ;AAAA,IAAA,CACtC,GACI,KAAA,gBAAgB,IAAIC,EAAsB;AAAA,MAC7C,WAAW;AAAA,MACX,WAAW,KAAK,OAAO,QAAQ;AAAA,IAAA,CAChC,GACI,KAAA,iBAAiB,IAAIA,EAAsB;AAAA,MAC9C,WAAW;AAAA,MACX,WAAW,KAAK,OAAO,QAAQ;AAAA,IAAA,CAChC,GACI,KAAA,gBAAgB,IAAIA,EAAsB;AAAA,MAC7C,WAAW;AAAA,MACX,WAAW,KAAK,OAAO,QAAQ;AAAA,IAAA,CAChC,GACD,KAAK,aAAa;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EAAA;AAAA,EAGF,OAAOC,GAA+B;AACpC,UAAMC,IAAkD,CAAC;AAEzD,eAAUC,KAAOF,GAAM;AACrB,YAAMG,IAAQD,EAAI;AAClB,MAAIC,KACJF,EAAY,KAAK,EAAC,MAAMC,EAAI,MAAc,OAAAC,GAAM;AAAA,IAAA;AAGlD,SAAK,aAAa;AAAA,MAChBF,EAAY,IAAI,CAACG,OAAU;AAAA,QACzB,MAAMA,EAAK;AAAA,QACX,OAAOA,EAAK,MAAM,CAAC;AAAA,QACnB,OAAOA,EAAK,MAAM,CAAC;AAAA,MAAA,EACnB;AAAA,IACJ,GACA,KAAK,cAAc;AAAA,MACjBH,EAAY,IAAI,CAACG,OAAU;AAAA,QACzB,MAAMA,EAAK;AAAA,QACX,OAAOA,EAAK,MAAM,CAAC;AAAA,MAAA,EACnB;AAAA,IACJ,GACA,KAAK,eAAe;AAAA,MAClBH,EAAY,IAAI,CAACG,OAAU;AAAA,QACzB,MAAMA,EAAK;AAAA,QACX,OAAOA,EAAK,MAAM,CAAC;AAAA,MAAA,EACnB;AAAA,IACJ,GACA,KAAK,cAAc;AAAA,MACjBH,EAAY,IAAI,CAACG,OAAU;AAAA,QACzB,MAAMA,EAAK;AAAA,QACX,OAAOA,EAAK,MAAM,CAAC;AAAA,MAAA,EACnB;AAAA,IACJ;AAAA,EAAA;AAEJ;AAEA,MAAqBC,UAAoBC,EAA2C;AAAA,EAApF;AAAA;AACE,IAAAT,EAAA,qBAAc,IAAIH,EAAY,IAAI;AAAA;AAAA,EAClC,mBAAmBa,GAAsC;AAChD,IAAAA,EAAA,gBAAgB,KAAK,WAAW;AAAA,EAAA;AAAA,EAGzC,cAAcC,GAA4C;AACrD,KAAAA,EAAQ,UAAUA,EAAQ,WAC3B,KAAK,mBAAmB;AAAA,EAC1B;AAAA,EAGF,qBAA2B;AACvB,SAAK,YAAY,OAAO,KAAK,kBAAkB,IAAI;AAAA,EAAA;AAAA,EAGvD,QAAQC,GAAgC;AAChC,UAAAC,IAAcD,EAAE,QAAQA,EAAE,OAAO,OAAO,KAAK,QAAQ,MAAM;AAC9D,QAAA,CAACC,EAAY,aAAc;AAQ9B,UAAMN,IANS,IAAIO,EAAe;AAAA,MAChC,QAAQD,EAAY,OAAO;AAAA,MAC3B,QAAQ,KAAK,QAAQ;AAAA,MACrB,QAAQ,KAAK,QAAQ;AAAA,IAAA,CACtB,EAEmB,UAAU,EAAE,GAAG,CAAC;AAEpC,WAAO,CAACN,EAAK,OAAsBA,EAAK,QAAwBA,EAAK,KAAoB;AAAA,EAAA;AAAA,EAG3F,SAAe;;AACb,UAAM,OAAO,IACRQ,IAAA,KAAA,eAAA,QAAAA,EAAY,gBAAgB,KAAK;AAAA,EAAW;AAAA,EAGnD,oBAAwC;AAC/B,WAAAnB;AAAA,EAAA;AAEX;"}