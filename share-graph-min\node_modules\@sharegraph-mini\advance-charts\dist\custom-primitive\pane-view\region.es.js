import { PrimitivePaneViewBase as d } from "../primitive-base.es.js";
import { ensureDefined as c } from "../../helpers/assertions.es.js";
const x = {
  backgroundColor: "#2196f31a",
  lineColor: "#787b86",
  lineWidth: 1,
  lineStyle: []
};
class v extends d {
  drawBackground(h) {
    const n = this.data, f = this.getVisibleLogicalRange();
    n.length < 2 || f && h.useBitmapCoordinateSpace((l) => {
      const o = l.context;
      o.scale(l.horizontalPixelRatio, l.verticalPixelRatio), o.beginPath();
      const s = new Path2D(), a = 0, r = n.length, e = n[a];
      s.moveTo(
        e.x,
        c(e.points.at(0))
      );
      for (let t = a + 1; t < r; t++) {
        const i = n[t];
        s.lineTo(
          i.x,
          i.points.at(0)
        );
      }
      for (let t = r - 1; t >= a; t--) {
        const i = n[t];
        s.lineTo(i.x, i.points.at(-1));
      }
      s.lineTo(
        e.x,
        c(e.points.at(0))
      ), s.closePath(), o.fillStyle = this.options.backgroundColor, o.fill(s);
      const p = e.points.map(() => new Path2D());
      e.points.map((t, i) => p[i].moveTo(e.x, t));
      for (let t = a + 1; t < r; t++) {
        const i = n[t];
        i.points.map((m, g) => p[g].lineTo(i.x, m));
      }
      o.setLineDash(this.options.lineStyle), o.lineWidth = this.options.lineWidth, o.strokeStyle = this.options.lineColor, p.map((t) => o.stroke(t)), o.setLineDash([]);
    });
  }
  defaultOptions() {
    return x;
  }
}
export {
  x as RegionPrimitiveOptionsDefault,
  v as RegionPrimitivePaneView
};
//# sourceMappingURL=region.es.js.map
