var s = /* @__PURE__ */ ((r) => (r[r.NONE = 0] = "NONE", r[r.ERROR = 1] = "ERROR", r[r.WARN = 2] = "WARN", r[r.INFO = 3] = "INFO", r[r.DEBUG = 4] = "DEBUG", r))(s || {});
let t = 0, o = {
  debug: (r, ...e) => console.debug(`[${r}]`, ...e),
  info: (r, ...e) => console.info(`[${r}]`, ...e),
  warn: (r, ...e) => console.warn(`[${r}]`, ...e),
  error: (r, ...e) => console.error(`[${r}]`, ...e)
};
const a = {
  reset: () => {
    t = 3;
  },
  setLevel: (r) => {
    t = r;
  },
  setLogger: (r) => {
    o = r;
  }
};
class i {
  constructor(e, n) {
    this.name = e, this.method = n;
  }
  debug(...e) {
    t >= 4 && o.debug(this.format(), ...e);
  }
  info(...e) {
    t >= 3 && o.info(this.format(), ...e);
  }
  warn(...e) {
    t >= 2 && o.warn(this.format(), ...e);
  }
  error(...e) {
    t >= 1 && o.error(this.format(), ...e);
  }
  format() {
    return this.method ? `${this.name}.${this.method}` : this.name;
  }
}
const h = new i("Chart");
export {
  s as Log,
  a as LogManager,
  i as Logger,
  h as log
};
//# sourceMappingURL=log.es.js.map
