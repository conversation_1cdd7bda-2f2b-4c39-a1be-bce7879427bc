{"version": 3, "file": "utils.es.js", "sources": ["../../src/helpers/utils.ts"], "sourcesContent": ["import {AutoscaleInfoProvider, Time} from \"lightweight-charts\";\r\nimport dayjs from './dayjs-setup'\r\nimport { Dayjs } from \"dayjs\";\r\nexport function timeToDate(time: Time) {\r\n  if(typeof time === 'number') return new Date(time * 1000);\r\n  if(typeof time === 'string') return new Date(time);\r\n  if(typeof time === 'object') {\r\n    return new Date(time.year, time.month - 1, time.day)\r\n  }\r\n\r\n  throw new Error('Do not support time')\r\n}\r\n\r\nexport function dateToTime(date: Date) {\r\n  return Math.floor(date.getTime() / 1000) as Time\r\n}\r\n\r\nexport function timeToDayjs(time: Time) {\r\n  return dayjs(timeToDate(time))\r\n} \r\n\r\nexport function timeToUnix(time: Time) {\r\n  if(typeof time === 'number') return time\r\n  return Math.floor(timeToDate(time).getTime() / 1000)\r\n}\r\n\r\nexport function dayjsToTime(djs: Dayjs): Time {\r\n  return djs.unix() as Time\r\n}\r\n\r\nexport const defaultCompare = (a: number | string, b: number | string) => a === b ? 0 : a < b ? -1 : 1\r\nexport function binarySearchIndex<T, V extends number | string>(\r\n  arr: T[],\r\n  target: V,\r\n  keyFn: (item: T) => V,\r\n  compare: (a: V, b: V) => number = defaultCompare\r\n): number {\r\n  let left = 0;\r\n  let right = arr.length - 1;\r\n\r\n  while (left <= right) {\r\n    const mid = Math.floor((left + right) / 2);\r\n    const midValue = keyFn(arr[mid]);\r\n    const comparison = compare(midValue, target);\r\n\r\n    if (comparison === 0) {\r\n      // Target found, return its index\r\n      return mid;\r\n    } else if (comparison < 0) {\r\n      // Search in the right half\r\n      left = mid + 1;\r\n    } else {\r\n      // Search in the left half\r\n      right = mid - 1;\r\n    }\r\n  }\r\n\r\n  // Target not found\r\n  return -1;\r\n}\r\n\r\nexport function binarySearch<T, V extends number | string>(\r\n  arr: T[],\r\n  target: V,\r\n  keyFn: (item: T) => V,\r\n  compare: (a: V, b: V) => number = defaultCompare\r\n): T | undefined {\r\n  const index = binarySearchIndex(arr, target, keyFn, compare);\r\n  return index !== -1 ? arr[index] : undefined;\r\n}\r\n\r\nexport function autoScaleInfoProviderCreator(defaultPriceRange: {maxValue: number, minValue: number}) {\r\n  return ((baseImplementation) => {\r\n    const base = baseImplementation();\r\n    const priceRange = base?.priceRange ?? defaultPriceRange\r\n\r\n    return {\r\n      priceRange: {\r\n        maxValue: Math.max(defaultPriceRange.maxValue, priceRange.maxValue),\r\n        minValue: Math.min(defaultPriceRange.minValue, priceRange.minValue),\r\n      }\r\n    }\r\n  }) satisfies AutoscaleInfoProvider\r\n}"], "names": ["timeToDate", "time", "dateToTime", "date", "timeToD<PERSON><PERSON><PERSON>", "dayjs", "timeToUnix", "dayjsToTime", "djs", "defaultCompare", "a", "b", "binarySearchIndex", "arr", "target", "keyFn", "compare", "left", "right", "mid", "midValue", "comparison", "binarySearch", "index", "autoScaleInfoProviderCreator", "defaultPriceRange", "baseImplementation", "base", "priceRange"], "mappings": ";;AAGO,SAASA,EAAWC,GAAY;AACrC,MAAG,OAAOA,KAAS,iBAAiB,IAAI,KAAKA,IAAO,GAAI;AACxD,MAAG,OAAOA,KAAS,SAAiB,QAAA,IAAI,KAAKA,CAAI;AAC9C,MAAA,OAAOA,KAAS;AACV,WAAA,IAAI,KAAKA,EAAK,MAAMA,EAAK,QAAQ,GAAGA,EAAK,GAAG;AAG/C,QAAA,IAAI,MAAM,qBAAqB;AACvC;AAEO,SAASC,EAAWC,GAAY;AACrC,SAAO,KAAK,MAAMA,EAAK,QAAA,IAAY,GAAI;AACzC;AAEO,SAASC,EAAYH,GAAY;AAC/B,SAAAI,EAAML,EAAWC,CAAI,CAAC;AAC/B;AAEO,SAASK,EAAWL,GAAY;AAClC,SAAA,OAAOA,KAAS,WAAiBA,IAC7B,KAAK,MAAMD,EAAWC,CAAI,EAAE,YAAY,GAAI;AACrD;AAEO,SAASM,EAAYC,GAAkB;AAC5C,SAAOA,EAAI,KAAK;AAClB;AAEa,MAAAC,IAAiB,CAACC,GAAoBC,MAAuBD,MAAMC,IAAI,IAAID,IAAIC,IAAI,KAAK;AAC9F,SAASC,EACdC,GACAC,GACAC,GACAC,IAAkCP,GAC1B;AACR,MAAIQ,IAAO,GACPC,IAAQL,EAAI,SAAS;AAEzB,SAAOI,KAAQC,KAAO;AACpB,UAAMC,IAAM,KAAK,OAAOF,IAAOC,KAAS,CAAC,GACnCE,IAAWL,EAAMF,EAAIM,CAAG,CAAC,GACzBE,IAAaL,EAAQI,GAAUN,CAAM;AAE3C,QAAIO,MAAe;AAEV,aAAAF;AACT,IAAWE,IAAa,IAEtBJ,IAAOE,IAAM,IAGbD,IAAQC,IAAM;AAAA,EAChB;AAIK,SAAA;AACT;AAEO,SAASG,EACdT,GACAC,GACAC,GACAC,IAAkCP,GACnB;AACf,QAAMc,IAAQX,EAAkBC,GAAKC,GAAQC,GAAOC,CAAO;AAC3D,SAAOO,MAAU,KAAKV,EAAIU,CAAK,IAAI;AACrC;AAEO,SAASC,EAA6BC,GAAyD;AACpG,SAAQ,CAACC,MAAuB;AAC9B,UAAMC,IAAOD,EAAmB,GAC1BE,KAAaD,KAAA,gBAAAA,EAAM,eAAcF;AAEhC,WAAA;AAAA,MACL,YAAY;AAAA,QACV,UAAU,KAAK,IAAIA,EAAkB,UAAUG,EAAW,QAAQ;AAAA,QAClE,UAAU,KAAK,IAAIH,EAAkB,UAAUG,EAAW,QAAQ;AAAA,MAAA;AAAA,IAEtE;AAAA,EACF;AACF;"}