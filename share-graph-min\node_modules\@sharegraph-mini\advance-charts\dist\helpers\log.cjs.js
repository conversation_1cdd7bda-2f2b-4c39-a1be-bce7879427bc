"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});var n=(e=>(e[e.NONE=0]="NONE",e[e.ERROR=1]="ERROR",e[e.WARN=2]="WARN",e[e.INFO=3]="INFO",e[e.DEBUG=4]="DEBUG",e))(n||{});let t=0,o={debug:(e,...r)=>console.debug(`[${e}]`,...r),info:(e,...r)=>console.info(`[${e}]`,...r),warn:(e,...r)=>console.warn(`[${e}]`,...r),error:(e,...r)=>console.error(`[${e}]`,...r)};const i={reset:()=>{t=3},setLevel:e=>{t=e},setLogger:e=>{o=e}};class s{constructor(r,a){this.name=r,this.method=a}debug(...r){t>=4&&o.debug(this.format(),...r)}info(...r){t>=3&&o.info(this.format(),...r)}warn(...r){t>=2&&o.warn(this.format(),...r)}error(...r){t>=1&&o.error(this.format(),...r)}format(){return this.method?`${this.name}.${this.method}`:this.name}}const f=new s("Chart");exports.Log=n;exports.LogManager=i;exports.Logger=s;exports.log=f;
//# sourceMappingURL=log.cjs.js.map
