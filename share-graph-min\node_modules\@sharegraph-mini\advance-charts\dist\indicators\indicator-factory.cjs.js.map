{"version": 3, "file": "indicator-factory.cjs.js", "sources": ["../../src/indicators/indicator-factory.ts"], "sourcesContent": ["import {I<PERSON>hart<PERSON>pi} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {merge} from \"es-toolkit\";\r\n\r\ninterface IChartIndicatorConstructor<IOptions extends ChartIndicatorOptions = ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[]> {\r\n  new (\r\n    chart: IChartApi,\r\n    options?: Partial<IOptions>,\r\n    paneIndex?: number\r\n  ): ChartIndicator<IOptions, IIndicatorData >;\r\n}\r\n\r\nexport class IndicatorFactory {\r\n  private static registry = new Map<string, [IChartIndicatorConstructor, object | undefined]>();\r\n\r\n  static registerIndicator<IOptions extends ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[]>(name: string, indicatorClass: IChartIndicatorConstructor<IOptions, IIndicatorData>, defaultOptions?: Partial<IOptions>) {\r\n    this.registry.set(name, [indicatorClass as unknown as IChartIndicatorConstructor, defaultOptions]);\r\n  }\r\n\r\n  static indicatorRegistered(name: string) {\r\n    return this.registry.has(name);\r\n  }\r\n\r\n  static createIndicator(name: string, chartApi: IChartApi, options?: Partial<ChartIndicatorOptions>, paneIndex?: number) {\r\n    const result = this.registry.get(name);\r\n    if (!result) {\r\n      throw new Error(`Indicator \"${name}\" not registered. Available: ${Array.from(this.registry.keys()).join(', ')}`);\r\n    }\r\n\r\n    const [IndicatorClass, defaultSettings] = result;\r\n    return new IndicatorClass(chartApi, (options || defaultSettings) ? merge(structuredClone(defaultSettings) ?? {}, options ?? {}) : undefined, paneIndex);\r\n  }\r\n}\r\n"], "names": ["IndicatorFactory", "name", "indicatorClass", "defaultOptions", "chartApi", "options", "paneIndex", "result", "IndicatorClass", "defaultSettings", "merge", "__publicField"], "mappings": "kRAYO,MAAMA,CAAiB,CAG5B,OAAO,kBAA+GC,EAAcC,EAAsEC,EAAoC,CAC5O,KAAK,SAAS,IAAIF,EAAM,CAACC,EAAyDC,CAAc,CAAC,CAAA,CAGnG,OAAO,oBAAoBF,EAAc,CAChC,OAAA,KAAK,SAAS,IAAIA,CAAI,CAAA,CAG/B,OAAO,gBAAgBA,EAAcG,EAAqBC,EAA0CC,EAAoB,CACtH,MAAMC,EAAS,KAAK,SAAS,IAAIN,CAAI,EACrC,GAAI,CAACM,EACH,MAAM,IAAI,MAAM,cAAcN,CAAI,gCAAgC,MAAM,KAAK,KAAK,SAAS,KAAM,CAAA,EAAE,KAAK,IAAI,CAAC,EAAE,EAG3G,KAAA,CAACO,EAAgBC,CAAe,EAAIF,EAC1C,OAAO,IAAIC,EAAeJ,EAAWC,GAAWI,EAAmBC,EAAAA,MAAM,gBAAgBD,CAAe,GAAK,GAAIJ,GAAW,CAAE,CAAA,EAAI,OAAWC,CAAS,CAAA,CAE1J,CAnBEK,EADWX,EACI,WAAW,IAAI"}