{"version": 3, "file": "time-key.cjs.js", "sources": ["../../src/advance-chart/time-key.ts"], "sourcesContent": ["import {Time, defaultHorzScaleBehavior} from \"lightweight-charts\"\r\nimport {Interval, Period} from \"./i-advance-chart\"\r\n\r\nconst horzScaleBehavior = new (defaultHorzScaleBehavior())\r\n\r\nconst MINUTE_IN_SECONDS = 60\r\nconst HOUR_IN_SECONDS = MINUTE_IN_SECONDS * 60\r\nconst DAY_IN_SECONDS = HOUR_IN_SECONDS * 24\r\nconst WEEK_IN_SECONDS = DAY_IN_SECONDS * 7\r\nconst START_OF_WEEK_OFFSET = DAY_IN_SECONDS * 3 // because timestamp 0 is 1/1/1970 which is a Thursday\r\n\r\nexport function timeKey(time: Time, interval: Interval) {\r\n  const unixTime = horzScaleBehavior.key(time)\r\n  const period = interval.period\r\n  const times = interval.times\r\n  \r\n  switch(period) {\r\n    case Period.minute: {\r\n      return unixTime - (unixTime % (MINUTE_IN_SECONDS * times))\r\n    }\r\n    case Period.hour: {\r\n      return unixTime - (unixTime % (HOUR_IN_SECONDS * times))\r\n    }\r\n    case Period.day: {\r\n        return unixTime - (unixTime % (DAY_IN_SECONDS * times))\r\n    }\r\n    case Period.week: {\r\n      const newUnixTime = unixTime + START_OF_WEEK_OFFSET\r\n      return unixTime - (newUnixTime % (WEEK_IN_SECONDS * times))\r\n    }\r\n    case Period.month: {\r\n      const monthDate = new Date(unixTime * 1000)\r\n      return Math.floor(Date.UTC(monthDate.getUTCFullYear(), monthDate.getUTCMonth(), 1, 0, 0, 0) / 1000)\r\n    }\r\n    default: \r\n      throw new Error('Invalid period')\r\n  }\r\n}   "], "names": ["horzScaleBehavior", "defaultHorzScaleBehavior", "MINUTE_IN_SECONDS", "HOUR_IN_SECONDS", "DAY_IN_SECONDS", "WEEK_IN_SECONDS", "START_OF_WEEK_OFFSET", "<PERSON><PERSON><PERSON>", "time", "interval", "unixTime", "period", "times", "Period", "newUnixTime", "monthDate"], "mappings": "4JAGMA,EAAoB,IAAKC,EAAAA,4BAEzBC,EAAoB,GACpBC,EAAkBD,EAAoB,GACtCE,EAAiBD,EAAkB,GACnCE,EAAkBD,EAAiB,EACnCE,EAAuBF,EAAiB,EAE9B,SAAAG,EAAQC,EAAYC,EAAoB,CAChD,MAAAC,EAAWV,EAAkB,IAAIQ,CAAI,EACrCG,EAASF,EAAS,OAClBG,EAAQH,EAAS,MAEvB,OAAOE,EAAQ,CACb,KAAKE,EAAAA,OAAO,OACH,OAAAH,EAAYA,GAAYR,EAAoBU,GAErD,KAAKC,EAAAA,OAAO,KACH,OAAAH,EAAYA,GAAYP,EAAkBS,GAEnD,KAAKC,EAAAA,OAAO,IACD,OAAAH,EAAYA,GAAYN,EAAiBQ,GAEpD,KAAKC,EAAAA,OAAO,KAAM,CAChB,MAAMC,EAAcJ,EAAWJ,EACxB,OAAAI,EAAYI,GAAeT,EAAkBO,EAAA,CAEtD,KAAKC,EAAAA,OAAO,MAAO,CACjB,MAAME,EAAY,IAAI,KAAKL,EAAW,GAAI,EAC1C,OAAO,KAAK,MAAM,KAAK,IAAIK,EAAU,eAAe,EAAGA,EAAU,YAAA,EAAe,EAAG,EAAG,EAAG,CAAC,EAAI,GAAI,CAAA,CAEpG,QACQ,MAAA,IAAI,MAAM,gBAAgB,CAAA,CAEtC"}