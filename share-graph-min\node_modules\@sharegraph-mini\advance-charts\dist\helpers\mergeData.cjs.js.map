{"version": 3, "file": "mergeData.cjs.js", "sources": ["../../src/helpers/mergeData.ts"], "sourcesContent": ["import type { Time } from 'lightweight-charts';\r\n\r\nexport function mergeOhlcData<D extends {time: Time } >(\r\n  oldData: D[],\r\n  newData: D[]\r\n): D[] {\r\n  // Handle trivial edge cases\r\n  if (oldData.length === 0) return newData;\r\n  if (newData.length === 0) return oldData;\r\n\r\n  /**\r\n   * 1. Find where oldData might overlap newData:\r\n   *    Walk backward until we find the last item in oldData whose time <= newData[0].time\r\n   */\r\n  let overlapIndex = oldData.length - 1;\r\n  while (overlapIndex >= 0 && oldData[overlapIndex].time > newData[0].time) {\r\n    overlapIndex--;\r\n  }\r\n\r\n  /**\r\n   * 2. Start our merged result up to that overlap point.\r\n   *    If overlapIndex is -1, that means oldData is all strictly > newData[0].time,\r\n   *    so we effectively start with an empty merged array.\r\n   */\r\n  const merged: D[] = oldData.slice(0, overlapIndex + 1);\r\n\r\n  /**\r\n   * 3. If there's an exact time match at overlapIndex, pop it so\r\n   *    it can be fully replaced by newData below.\r\n   */\r\n  if (overlapIndex >= 0 && oldData[overlapIndex].time === newData[0].time) {\r\n    merged.pop();\r\n  }\r\n\r\n  /**\r\n   * 4. Insert all the newData (this overwrites any collided times).\r\n   */\r\n  merged.push(...newData);\r\n\r\n  /**\r\n   * 5. Skip over any oldData that is overlapped or replaced by newData's range.\r\n   *    i.e. any oldData whose time <= newData[newData.length - 1].time\r\n   */\r\n  const lastNewTime = newData[newData.length - 1].time;\r\n  let i = overlapIndex + 1;\r\n  while (i < oldData.length && oldData[i].time <= lastNewTime) {\r\n    i++;\r\n  }\r\n\r\n  /**\r\n   * 6. Append oldData beyond that time (if any).\r\n   */\r\n  while (i < oldData.length) {\r\n    merged.push(oldData[i]);\r\n    i++;\r\n  }\r\n\r\n  return merged;\r\n}\r\n"], "names": ["mergeOhlcData", "oldData", "newData", "overlapIndex", "merged", "lastNewTime", "i"], "mappings": "gFAEgB,SAAAA,EACdC,EACAC,EACK,CAED,GAAAD,EAAQ,SAAW,EAAU,OAAAC,EAC7B,GAAAA,EAAQ,SAAW,EAAU,OAAAD,EAM7B,IAAAE,EAAeF,EAAQ,OAAS,EAC7B,KAAAE,GAAgB,GAAKF,EAAQE,CAAY,EAAE,KAAOD,EAAQ,CAAC,EAAE,MAClEC,IAQF,MAAMC,EAAcH,EAAQ,MAAM,EAAGE,EAAe,CAAC,EAMjDA,GAAgB,GAAKF,EAAQE,CAAY,EAAE,OAASD,EAAQ,CAAC,EAAE,MACjEE,EAAO,IAAI,EAMNA,EAAA,KAAK,GAAGF,CAAO,EAMtB,MAAMG,EAAcH,EAAQA,EAAQ,OAAS,CAAC,EAAE,KAChD,IAAII,EAAIH,EAAe,EACvB,KAAOG,EAAIL,EAAQ,QAAUA,EAAQK,CAAC,EAAE,MAAQD,GAC9CC,IAMK,KAAAA,EAAIL,EAAQ,QACVG,EAAA,KAAKH,EAAQK,CAAC,CAAC,EACtBA,IAGK,OAAAF,CACT"}