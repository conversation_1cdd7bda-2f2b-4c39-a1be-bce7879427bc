var i = Object.defineProperty;
var m = (r, t, e) => t in r ? i(r, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : r[t] = e;
var a = (r, t, e) => m(r, typeof t != "symbol" ? t + "" : t, e);
class o {
  constructor(t = "en-gb") {
    a(this, "_volumeFormatter");
    a(this, "_decimalFormatter");
    a(this, "_percentFormatter");
    this.locale = t;
  }
  get volumeFormatter() {
    return this._volumeFormatter || (this._volumeFormatter = new Intl.NumberFormat(this.locale, { notation: "compact", compactDisplay: "short", maximumFractionDigits: 2 })), this._volumeFormatter;
  }
  get decimalFormatter() {
    return this._decimalFormatter || (this._decimalFormatter = new Intl.NumberFormat(this.locale)), this._decimalFormatter;
  }
  get percentFormatter() {
    return this._percentFormatter || (this._percentFormatter = new Intl.NumberFormat(this.locale, {
      style: "percent",
      minimumFractionDigits: 2,
      // Ensure 2 decimal places
      maximumFractionDigits: 2
      // Restrict to 2 decimal places
    })), this._percentFormatter;
  }
  volume(t) {
    return t == null ? "" : Number.isNaN(t) ? "NaN" : this.volumeFormatter.format(t);
  }
  decimal(t) {
    return t == null ? "" : Number.isNaN(t) ? "NaN" : this.decimalFormatter.format(t);
  }
  percent(t) {
    return t == null ? "" : Number.isNaN(t) ? "NaN" : this.percentFormatter.format(t);
  }
}
const n = {
  _cache: /* @__PURE__ */ new Map(),
  formatter(r) {
    this._cache.has(r) && this._cache.get(r);
    const t = new o(r);
    return this._cache.set(r, t), t;
  }
};
export {
  o as NumberFormatter,
  n as NumberFormatterFactory
};
//# sourceMappingURL=number-formatter.es.js.map
