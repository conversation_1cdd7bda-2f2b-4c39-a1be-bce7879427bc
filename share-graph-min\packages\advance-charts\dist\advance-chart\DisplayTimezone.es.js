var s = Object.defineProperty;
var n = (o, e, r) => e in o ? s(o, e, { enumerable: !0, configurable: !0, writable: !0, value: r }) : o[e] = r;
var a = (o, e, r) => n(o, typeof e != "symbol" ? e + "" : e, r);
import { Delegate as c } from "../helpers/delegate.es.js";
import { Period as m } from "./i-advance-chart.es.js";
import { TickMarkType as i } from "lightweight-charts";
import "../helpers/dayjs-setup.es.js";
import h from "dayjs";
class z {
  constructor(e) {
    a(this, "_timezoneChanged", new c());
    a(this, "_timezone");
    a(this, "_browserTimezone", Intl.DateTimeFormat().resolvedOptions().timeZone);
    this._source = e, this._timezone = this._source.options.tzDisplay;
  }
  formatDateTime(e) {
    return e = this.convertDateToTimezoneDate(e), new Intl.DateTimeFormat(this._source.options.locale, {
      dateStyle: "medium",
      timeStyle: "medium",
      hourCycle: "h23"
    }).format(e);
  }
  formatDate(e) {
    return e = this.convertDateToTimezoneDate(e), new Intl.DateTimeFormat(this._source.options.locale, {
      dateStyle: "medium"
    }).format(e);
  }
  convertDateToTimezoneDate(e) {
    return this._timezone === this._browserTimezone ? e : h.tz(e, this._timezone).tz(this._browserTimezone, !0).toDate();
  }
  format(e) {
    switch (this._source.dataInterval.period) {
      case m.day:
      case m.month:
      case m.week:
        return this.formatDate(e);
      default:
        return this.formatDateTime(e);
    }
  }
  tickMarkFormatter(e, r) {
    e = this.convertDateToTimezoneDate(e);
    const t = {};
    switch (r) {
      case i.Year:
        t.year = "numeric";
        break;
      case i.Month:
        t.month = "short";
        break;
      case i.DayOfMonth:
        t.day = "numeric";
        break;
      case i.Time:
        t.hour12 = !1, t.hour = "2-digit", t.minute = "2-digit";
        break;
      case i.TimeWithSeconds:
        t.hour12 = !1, t.hour = "2-digit", t.minute = "2-digit", t.second = "2-digit";
        break;
    }
    return e.toLocaleString(this._source.options.locale, t);
  }
}
export {
  z as DisplayTimezone
};
//# sourceMappingURL=DisplayTimezone.es.js.map
