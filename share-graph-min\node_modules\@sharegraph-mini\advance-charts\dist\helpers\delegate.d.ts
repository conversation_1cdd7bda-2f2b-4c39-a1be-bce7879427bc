export type Callback<T1 = void, T2 = void, T3 = void> = (param1: T1, param2: T2, param3: T3) => void;
export interface ISubscription<T1 = void, T2 = void, T3 = void> {
    subscribe(callback: Callback<T1, T2, T3>, linkedObject?: unknown, singleshot?: boolean): void;
    unsubscribe(callback: Callback<T1, T2, T3>): void;
    unsubscribeAll(linkedObject: unknown): void;
    lastParams(): [T1, T2, T3];
}
export declare class Delegate<T1 = void, T2 = void, T3 = void> implements ISubscription<T1, T2, T3> {
    private _listeners;
    private _params;
    fire(param1: T1, param2: T2, param3: T3): void;
    lastParams(): [T1, T2, T3];
    subscribe(callback: Callback<T1, T2, T3>, linkedObject?: unknown, singleshot?: boolean): void;
    unsubscribe(callback: Callback<T1, T2, T3>): void;
    unsubscribeAll(linkedObject: unknown): void;
    hasListener(): boolean;
    destroy(): void;
}
export type IPublicDelegate<T extends Delegate<any, any, any>> = T extends Delegate<infer T1, infer T2, infer T3> ? ISubscription<T1, T2, T3> : never;
