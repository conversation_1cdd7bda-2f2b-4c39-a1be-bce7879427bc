{"version": 3, "file": "execution-indicator.es.js", "sources": ["../../src/helpers/execution-indicator.ts"], "sourcesContent": ["import { OHLCVSimple } from '../interface';\r\n\r\nexport interface ISymbol extends Omit<OHLCVSimple, 'time'> {\r\n  time: number\r\n  isNew: boolean;\r\n}\r\n\r\nexport type ISymbolData = Omit<ISymbol, 'isNew'>;\r\n\r\nexport interface IIndicatorBar<D extends readonly number[]> {\r\n  time: number, \r\n  value?: D\r\n}\r\n\r\nexport interface IVar {\r\n  /**\r\n   * Gets the historical value at the specified depth.\r\n   * \r\n   * Depth represents how many periods back to look:\r\n   * - depth=0: current/most recent value\r\n   * - depth=1: previous value\r\n   * - depth=N: value from N periods ago\r\n   * \r\n   * @param depth - Number of periods to look back (0-based)\r\n   * @returns The value at requested depth or NaN if insufficient history\r\n   */\r\n  get(depth: number): number;\r\n  \r\n  /**\r\n   * Sets the current value (depth=0) and shifts historical values.\r\n   * \r\n   * When set, the value becomes the new current value (depth=0),\r\n   * and all previous values shift deeper (depth increases by 1).\r\n   * \r\n   * @param num - The new current value to set\r\n   */\r\n  set(num: number): void;\r\n\r\n  /**\r\n   * Gets all historical values in depth order (newest first).\r\n   * \r\n   * The array represents the complete history buffer where:\r\n   * - index 0: current value (depth=0)\r\n   * - index 1: previous value (depth=1)\r\n   * - index N: value from N periods ago (depth=N)\r\n   * \r\n   * @returns Array of historical values sorted by depth (shallow to deep)\r\n   */\r\n  getAll(): number[];\r\n\r\n  /**\r\n   * Checks if sufficient historical depth is available for calculations.\r\n   * \r\n   * A variable is calculable when it has collected enough historical\r\n   * values to satisfy its minimum depth requirement.\r\n   * \r\n   * @returns True if minimum required depth is available\r\n   */\r\n  calculable(): boolean;\r\n  \r\n  /**\r\n   * Prepares the variable for a new data point.\r\n   * \r\n   * Called when processing new market data. Resets internal state\r\n   * and updates the variable based on the new symbol information.\r\n   * \r\n   * @param d - The new symbol data point\r\n   */\r\n  prepare(d: ISymbol): void;\r\n\r\n  valueOf(): number;\r\n}\r\n\r\nexport interface IContext {\r\n  new_var(value: number, depth: number): IVar;\r\n  prepare(d: ISymbol): void;\r\n  symbol: ISymbol;\r\n}\r\n\r\nexport class Context implements IContext {\r\n  _varIndex = 0;\r\n  _vars: IVar[] = [];\r\n  symbol: ISymbol = {\r\n    time: NaN,\r\n    close: NaN,\r\n    high: NaN,\r\n    low: NaN,\r\n    open: NaN,\r\n    volume: NaN,\r\n    isNew: true,\r\n  };\r\n  new_var(value: number, depth: number): IVar {\r\n    if (this._varIndex >= this._vars.length) {\r\n      this._vars.push(new Var(depth));\r\n    }\r\n\r\n    const instance = this._vars[this._varIndex++];\r\n    instance.set(value);\r\n    return instance;\r\n  }\r\n\r\n  prepare(d: ISymbol): void {\r\n    this._varIndex = 0;\r\n    this.symbol = d;\r\n    this._vars.forEach((item) => item.prepare(d));\r\n  }\r\n}\r\n\r\nexport class Var implements IVar {\r\n  _his: Array<number> | null = null;\r\n  _hisPosition = 0;\r\n  _minDepth = 0;\r\n  origin: number = NaN;\r\n  modified = false;\r\n\r\n  constructor(depth: number) {\r\n    this._his = Array(depth).fill(NaN)\r\n    this._hisPosition = depth - 1;\r\n    this._minDepth = depth\r\n  }\r\n\r\n  public valueOf(): number {\r\n    return this.get(0);\r\n  }\r\n\r\n  public get(depth: number): number {\r\n    if (this._his) {\r\n      const index = this._hisPosition - depth;\r\n      return this._his[index];\r\n    }\r\n    this._minDepth = Math.max(this._minDepth, depth);\r\n    return NaN;\r\n  }\r\n\r\n  public getAll() {\r\n    return Array.from(this._his || []).reverse()\r\n  }\r\n\r\n  public calculable() {\r\n    if(!this._his) return false;\r\n    return !this._his.some(item => isNaN(item))\r\n  }\r\n\r\n  public set(num: number): void {\r\n    if (!this._his) return;\r\n    this._his[this._hisPosition] = num;\r\n    this.modified = true;\r\n  }\r\n\r\n  public prepare(d: ISymbol): void {\r\n    if (d.isNew) {\r\n      this.origin = this.get(0);\r\n      if (this.modified || !this._his) {\r\n        this.addHist();\r\n      }\r\n    } else {\r\n      this.set(this.origin);\r\n    }\r\n  }\r\n\r\n  private addHist() {\r\n    if (!this._his) {\r\n      if (!this._minDepth) throw new Error('error');\r\n      this._his = Array(this._minDepth + 1).fill(NaN);\r\n    }\r\n\r\n    this._hisPosition = Math.min(this._hisPosition + 1, this._his.length);\r\n    if (this._hisPosition === this._his.length) {\r\n      this._hisPosition = this._his.length - 1;\r\n      this._his.shift();\r\n      this._his.push(NaN);\r\n    }\r\n\r\n    this._his[this._hisPosition] = this.origin;\r\n  }\r\n}\r\n\r\n/**\r\n * Execution context for calculating technical indicators.\r\n * \r\n * Key points:\r\n * - Must process data points in chronological order (time ascending)\r\n * - Maintains calculation context between points\r\n * - Supports two operations:\r\n *   1. Full recalculation (recalc) - processes entire dataset\r\n *   2. Update (update) - processes only the last/new point\r\n * \r\n * Important: \r\n * - Cannot update random points - will break calculation context\r\n * - Only supports appending new points or updating the last point\r\n */\r\nexport class ExecutionContext<T extends readonly number[]> {\r\n  data: IIndicatorBar<T>[] = [];\r\n  _context = new Context();\r\n  _isCalc = false;\r\n  constructor(protected formula: (c: Context) => IIndicatorBar<T>['value'] | undefined) {\r\n    this.init();\r\n  }\r\n\r\n  private init() {\r\n    // Initialize context and formula variables by calling formula once with empty data\r\n    // This ensures all Var instances are properly set up before calculations begin\r\n    this._context = new Context();\r\n    this.formula(this._context);\r\n    this.data = [];\r\n  }\r\n\r\n  private calcLasPoint(symbol: ISymbolData) {\r\n    const lastCalc = this.data[this.data.length - 1]\r\n    const isNew = lastCalc?.time !== symbol.time;\r\n    this._context.prepare({ ...symbol, isNew });\r\n    const result = {time: symbol.time, value: this.formula(this._context)};\r\n\r\n    if(isNew) {\r\n      this.data.push(result)\r\n    } else {\r\n      this.data[this.data.length - 1] = result\r\n    }\r\n  }\r\n\r\n  recalc(data: ISymbolData[]) {\r\n    this.init();\r\n    for (const item of data) {\r\n      // treat any new item as last point\r\n      this.calcLasPoint(item);\r\n    }\r\n    this._isCalc = true;\r\n  }\r\n\r\n  update(data: ISymbolData) {\r\n    this.calcLasPoint(data);\r\n  }\r\n}\r\n"], "names": ["Context", "__publicField", "value", "depth", "Var", "instance", "d", "item", "index", "num", "ExecutionContext", "formula", "symbol", "lastCalc", "isNew", "result", "data"], "mappings": ";;;AA+EO,MAAMA,EAA4B;AAAA,EAAlC;AACL,IAAAC,EAAA,mBAAY;AACZ,IAAAA,EAAA,eAAgB,CAAC;AACjB,IAAAA,EAAA,gBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA;AAAA,EACA,QAAQC,GAAeC,GAAqB;AAC1C,IAAI,KAAK,aAAa,KAAK,MAAM,UAC/B,KAAK,MAAM,KAAK,IAAIC,EAAID,CAAK,CAAC;AAGhC,UAAME,IAAW,KAAK,MAAM,KAAK,WAAW;AAC5C,WAAAA,EAAS,IAAIH,CAAK,GACXG;AAAA,EAAA;AAAA,EAGT,QAAQC,GAAkB;AACxB,SAAK,YAAY,GACjB,KAAK,SAASA,GACd,KAAK,MAAM,QAAQ,CAACC,MAASA,EAAK,QAAQD,CAAC,CAAC;AAAA,EAAA;AAEhD;AAEO,MAAMF,EAAoB;AAAA,EAO/B,YAAYD,GAAe;AAN3B,IAAAF,EAAA,cAA6B;AAC7B,IAAAA,EAAA,sBAAe;AACf,IAAAA,EAAA,mBAAY;AACZ,IAAAA,EAAA,gBAAiB;AACjB,IAAAA,EAAA,kBAAW;AAGT,SAAK,OAAO,MAAME,CAAK,EAAE,KAAK,GAAG,GACjC,KAAK,eAAeA,IAAQ,GAC5B,KAAK,YAAYA;AAAA,EAAA;AAAA,EAGZ,UAAkB;AAChB,WAAA,KAAK,IAAI,CAAC;AAAA,EAAA;AAAA,EAGZ,IAAIA,GAAuB;AAChC,QAAI,KAAK,MAAM;AACP,YAAAK,IAAQ,KAAK,eAAeL;AAC3B,aAAA,KAAK,KAAKK,CAAK;AAAA,IAAA;AAExB,gBAAK,YAAY,KAAK,IAAI,KAAK,WAAWL,CAAK,GACxC;AAAA,EAAA;AAAA,EAGF,SAAS;AACd,WAAO,MAAM,KAAK,KAAK,QAAQ,CAAA,CAAE,EAAE,QAAQ;AAAA,EAAA;AAAA,EAGtC,aAAa;AACf,WAAC,KAAK,OACF,CAAC,KAAK,KAAK,KAAK,CAAQI,MAAA,MAAMA,CAAI,CAAC,IADpB;AAAA,EACoB;AAAA,EAGrC,IAAIE,GAAmB;AACxB,IAAC,KAAK,SACL,KAAA,KAAK,KAAK,YAAY,IAAIA,GAC/B,KAAK,WAAW;AAAA,EAAA;AAAA,EAGX,QAAQH,GAAkB;AAC/B,IAAIA,EAAE,SACC,KAAA,SAAS,KAAK,IAAI,CAAC,IACpB,KAAK,YAAY,CAAC,KAAK,SACzB,KAAK,QAAQ,KAGV,KAAA,IAAI,KAAK,MAAM;AAAA,EACtB;AAAA,EAGM,UAAU;AACZ,QAAA,CAAC,KAAK,MAAM;AACd,UAAI,CAAC,KAAK,UAAiB,OAAA,IAAI,MAAM,OAAO;AAC5C,WAAK,OAAO,MAAM,KAAK,YAAY,CAAC,EAAE,KAAK,GAAG;AAAA,IAAA;AAG3C,SAAA,eAAe,KAAK,IAAI,KAAK,eAAe,GAAG,KAAK,KAAK,MAAM,GAChE,KAAK,iBAAiB,KAAK,KAAK,WAC7B,KAAA,eAAe,KAAK,KAAK,SAAS,GACvC,KAAK,KAAK,MAAM,GACX,KAAA,KAAK,KAAK,GAAG,IAGpB,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK;AAAA,EAAA;AAExC;AAgBO,MAAMI,EAA8C;AAAA,EAIzD,YAAsBC,GAAgE;AAHtF,IAAAV,EAAA,cAA2B,CAAC;AAC5B,IAAAA,EAAA,kBAAW,IAAID,EAAQ;AACvB,IAAAC,EAAA,iBAAU;AACY,SAAA,UAAAU,GACpB,KAAK,KAAK;AAAA,EAAA;AAAA,EAGJ,OAAO;AAGR,SAAA,WAAW,IAAIX,EAAQ,GACvB,KAAA,QAAQ,KAAK,QAAQ,GAC1B,KAAK,OAAO,CAAC;AAAA,EAAA;AAAA,EAGP,aAAaY,GAAqB;AACxC,UAAMC,IAAW,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,GACzCC,KAAQD,KAAA,gBAAAA,EAAU,UAASD,EAAO;AACxC,SAAK,SAAS,QAAQ,EAAE,GAAGA,GAAQ,OAAAE,GAAO;AACpC,UAAAC,IAAS,EAAC,MAAMH,EAAO,MAAM,OAAO,KAAK,QAAQ,KAAK,QAAQ,EAAC;AAErE,IAAGE,IACI,KAAA,KAAK,KAAKC,CAAM,IAErB,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,IAAIA;AAAA,EACpC;AAAA,EAGF,OAAOC,GAAqB;AAC1B,SAAK,KAAK;AACV,eAAWT,KAAQS;AAEjB,WAAK,aAAaT,CAAI;AAExB,SAAK,UAAU;AAAA,EAAA;AAAA,EAGjB,OAAOS,GAAmB;AACxB,SAAK,aAAaA,CAAI;AAAA,EAAA;AAE1B;"}