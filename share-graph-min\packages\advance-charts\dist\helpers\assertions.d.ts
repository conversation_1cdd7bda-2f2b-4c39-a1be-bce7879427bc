/**
 * Ensures that value is defined.
 * Throws if the value is undefined, returns the original value otherwise.
 *
 * @param value - The value, or undefined.
 * @returns The passed value, if it is not undefined
 */
export declare function ensureDefined(value: undefined): never;
export declare function ensureDefined<T>(value: T | undefined): T;
/**
 * Ensures that value is not null.
 * Throws if the value is null, returns the original value otherwise.
 *
 * @param value - The value, or null.
 * @returns The passed value, if it is not null
 */
export declare function ensureNotNull(value: null): never;
export declare function ensureNotNull<T>(value: T | null): T;
