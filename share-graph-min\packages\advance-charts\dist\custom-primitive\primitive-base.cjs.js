"use strict";var u=Object.defineProperty;var c=(s,e,t)=>e in s?u(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var i=(s,e,t)=>c(s,typeof e!="symbol"?e+"":e,t);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const r=require("../helpers/assertions.cjs.js"),d=require("es-toolkit"),n=require("../helpers/utils.cjs.js");class l{constructor(){i(this,"_chart");i(this,"_series");i(this,"_paneViews",[]);i(this,"indicatorData",[]);i(this,"_isDetached",!1);i(this,"_requestUpdate");i(this,"_fireDataUpdated",e=>{this.dataUpdated&&this.dataUpdated(e)})}updateAllViews(){var e;this._isDetached||(e=this._updateAllViews)==null||e.call(this)}_updateAllViews(){}paneViews(){return this._paneViews}requestUpdate(){this._requestUpdate&&this._requestUpdate()}attached({chart:e,series:t,requestUpdate:a}){var h;this._chart=e,this._series=t,this._series.subscribeDataChanged(this._fireDataUpdated),this._requestUpdate=a,this.requestUpdate(),this._paneViews.map(o=>o.attached(t,e)),(h=this._attached)==null||h.call(this)}get data(){const e=this.series.data();if(e.length>0)r.ensureDefined(e[0].customValues);else return[];return e.map(t=>t.customValues)}detached(){var e;(e=this._series)==null||e.unsubscribeDataChanged(this._fireDataUpdated),this._chart=void 0,this._series=void 0,this._paneViews=[],this._requestUpdate=void 0,this._isDetached=!0}get chart(){return r.ensureDefined(this._chart)}get series(){return r.ensureDefined(this._series)}dataByTime(e){return n.binarySearch(this.indicatorData,n.timeToUnix(e),t=>n.timeToUnix(t.time))}lastPoint(){return this.indicatorData.at(-1)}}class _{constructor(e){i(this,"options");i(this,"_series",null);i(this,"_chartApi",null);i(this,"_data",null);i(this,"_timeScale");this.options=d.merge(d.cloneDeep(this.defaultOptions()),e??{})}renderer(){return this}draw(e){this._chartApi&&this._series&&e.useBitmapCoordinateSpace(t=>{var a;return(a=this._drawImpl)==null?void 0:a.call(this,t)})}drawBackground(e){this._chartApi&&this._series&&e.useBitmapCoordinateSpace(t=>{var a;return(a=this._drawBackgroundImpl)==null?void 0:a.call(this,t)})}get data(){return r.ensureNotNull(this._data)}get series(){return r.ensureNotNull(this._series)}get chartApi(){return r.ensureNotNull(this._chartApi)}get timeScale(){return this._timeScale||(this._timeScale=this.chartApi.timeScale()),this._timeScale}getVisibleLogicalRange(){const e=this.timeScale.getVisibleLogicalRange();if(e)return{from:Math.floor(e.from),to:Math.ceil(e.to)}}getVisibleRange(){return this.timeScale.getVisibleRange()}coordinateToPrice(e){return this.series.coordinateToPrice(e)}priceToCoordinate(e){return this.series.priceToCoordinate(e)}timeToCoordinate(e){return this.timeScale.timeToCoordinate(e)}attached(e,t){this._series=e,this._chartApi=t}update(e){var t;this._data=e,(t=this._update)==null||t.call(this)}}exports.PrimitivePaneViewBase=_;exports.SeriesPrimitiveBase=l;
//# sourceMappingURL=primitive-base.cjs.js.map
