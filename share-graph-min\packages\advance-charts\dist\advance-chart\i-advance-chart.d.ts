import { <PERSON>ChartApi, IPaneApi, ISeriesApi, PriceScaleMode, SeriesType, Time } from 'lightweight-charts';
import { ISubscription } from '../helpers/delegate';
import { ChartIndicator } from '../indicators/abstract-indicator';
import { NumberFormatter } from '../helpers/number-formatter';
import { OHLCVExtraData } from '../interface';

export declare enum Period {
    minute = "minute",
    hour = "hour",
    day = "day",
    week = "week",
    month = "month"
}
export interface Interval {
    period: Period;
    times: number;
}
export type IGroupIndicatorByPane = {
    pane: IPaneApi<Time>;
    indicators: ChartIndicator[];
};
export type IAdvanceChartType = 'line' | 'candle' | 'mountain' | 'bar' | 'baseline' | 'base-mountain';
export interface IAdvanceChartOptions {
    upColor: string;
    downColor: string;
    mainColor: string;
    highLowLineVisible: boolean;
    highLineColor: string;
    lowLineColor: string;
    priceScaleMode: PriceScaleMode;
    priceLineVisible: boolean;
    locale: string;
    gridColor: string;
    axesColor: string;
    fontSize?: number;
    fontFamily?: string;
    tzDisplay: string;
    height: number;
}
export interface IAdvanceChart {
    chartApi: IChartApi;
    chartType: IAdvanceChartType | null;
    options: IAdvanceChartOptions;
    numberFormatter: NumberFormatter;
    loading: boolean;
    mainSeries: ISeriesApi<SeriesType> | null;
    dataInterval: Interval;
    updated(): ISubscription;
    chartTypeChanged(): ISubscription;
    indicatorChanged(): ISubscription<string, 'add' | 'remove'>;
    crosshairMoved(): ISubscription<OHLCVExtraData, OHLCVExtraData>;
    destroyed(): ISubscription;
    chartHovered(): ISubscription<boolean>;
    onLoading(): ISubscription<boolean>;
    optionChanged(): ISubscription;
    mainSeriesChanged(): ISubscription;
}
