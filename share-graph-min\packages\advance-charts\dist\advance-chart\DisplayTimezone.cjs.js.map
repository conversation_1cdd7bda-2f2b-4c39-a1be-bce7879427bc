{"version": 3, "file": "DisplayTimezone.cjs.js", "sources": ["../../src/advance-chart/DisplayTimezone.ts"], "sourcesContent": ["import { Delegate } from '../helpers/delegate';\r\nimport { IAdvanceChart, Period } from './i-advance-chart';\r\nimport {TickMarkType} from 'lightweight-charts';\r\nimport dayjs from '../helpers/dayjs-setup'\r\nexport class DisplayTimezone {\r\n  _timezoneChanged = new Delegate<string>();\r\n  _timezone: string\r\n  _browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone\r\n  constructor(\r\n    private _source: IAdvanceChart\r\n  ) {\r\n    this._timezone = this._source.options.tzDisplay\r\n\r\n    // const now = dayjs(new Date).tz(this._timezone).format('YYYY-MM-DD HH:mm:ss Z');\r\n    // console.log('timezone display now: ' + now)\r\n  }\r\n\r\n  formatDateTime(date: Date) {\r\n    date = this.convertDateToTimezoneDate(date);\r\n    return new Intl.DateTimeFormat(this._source.options.locale, {\r\n      dateStyle: 'medium',\r\n      timeStyle: 'medium',\r\n      hourCycle: 'h23', \r\n    }).format(date);\r\n  }\r\n  formatDate(date: Date) {\r\n    date = this.convertDateToTimezoneDate(date);\r\n    return new Intl.DateTimeFormat(this._source.options.locale, {\r\n      dateStyle: 'medium',\r\n    }).format(date);\r\n  }\r\n\r\n  convertDateToTimezoneDate(date: Date) {\r\n    if(this._timezone === this._browserTimezone) return date;\r\n    return dayjs.tz(date, this._timezone).tz(this._browserTimezone, true).toDate()\r\n  }\r\n\r\n  format(date: Date) {\r\n    switch(this._source.dataInterval.period) {\r\n      case Period.day:\r\n      case Period.month:\r\n      case Period.week:\r\n        return this.formatDate(date);\r\n      default:\r\n        return this.formatDateTime(date)\r\n    }\r\n  }\r\n\r\n  tickMarkFormatter(date: Date, tickMarkType: TickMarkType) {\r\n    date = this.convertDateToTimezoneDate(date);\r\n    const formatOptions: Intl.DateTimeFormatOptions = {};\r\n\r\n    switch (tickMarkType) {\r\n      case TickMarkType.Year:\r\n        formatOptions.year = 'numeric';\r\n        break;\r\n\r\n      case TickMarkType.Month:\r\n        formatOptions.month = 'short';\r\n        break;\r\n\r\n      case TickMarkType.DayOfMonth:\r\n        formatOptions.day = 'numeric';\r\n        break;\r\n\r\n      case TickMarkType.Time:\r\n        formatOptions.hour12 = false;\r\n        formatOptions.hour = '2-digit';\r\n        formatOptions.minute = '2-digit';\r\n        break;\r\n\r\n      case TickMarkType.TimeWithSeconds:\r\n        formatOptions.hour12 = false;\r\n        formatOptions.hour = '2-digit';\r\n        formatOptions.minute = '2-digit';\r\n        formatOptions.second = '2-digit';\r\n        break;\r\n    }\r\n\r\n    return date.toLocaleString(this._source.options.locale, formatOptions)\r\n  }\r\n}\r\n"], "names": ["DisplayTimezone", "_source", "__publicField", "Delegate", "date", "dayjs", "Period", "tickMarkType", "formatOptions", "TickMarkType"], "mappings": "0aAIO,MAAMA,CAAgB,CAI3B,YACUC,EACR,CALFC,EAAA,wBAAmB,IAAIC,EAAAA,UACvBD,EAAA,kBACAA,EAAA,wBAAmB,KAAK,iBAAiB,gBAAkB,EAAA,UAEjD,KAAA,QAAAD,EAEH,KAAA,UAAY,KAAK,QAAQ,QAAQ,SAAA,CAMxC,eAAeG,EAAY,CAClB,OAAAA,EAAA,KAAK,0BAA0BA,CAAI,EACnC,IAAI,KAAK,eAAe,KAAK,QAAQ,QAAQ,OAAQ,CAC1D,UAAW,SACX,UAAW,SACX,UAAW,KAAA,CACZ,EAAE,OAAOA,CAAI,CAAA,CAEhB,WAAWA,EAAY,CACd,OAAAA,EAAA,KAAK,0BAA0BA,CAAI,EACnC,IAAI,KAAK,eAAe,KAAK,QAAQ,QAAQ,OAAQ,CAC1D,UAAW,QAAA,CACZ,EAAE,OAAOA,CAAI,CAAA,CAGhB,0BAA0BA,EAAY,CACpC,OAAG,KAAK,YAAc,KAAK,iBAAyBA,EAC7CC,EAAM,GAAGD,EAAM,KAAK,SAAS,EAAE,GAAG,KAAK,iBAAkB,EAAI,EAAE,OAAO,CAAA,CAG/E,OAAOA,EAAY,CACV,OAAA,KAAK,QAAQ,aAAa,OAAQ,CACvC,KAAKE,EAAO,OAAA,IACZ,KAAKA,EAAO,OAAA,MACZ,KAAKA,EAAO,OAAA,KACH,OAAA,KAAK,WAAWF,CAAI,EAC7B,QACS,OAAA,KAAK,eAAeA,CAAI,CAAA,CACnC,CAGF,kBAAkBA,EAAYG,EAA4B,CACjDH,EAAA,KAAK,0BAA0BA,CAAI,EAC1C,MAAMI,EAA4C,CAAC,EAEnD,OAAQD,EAAc,CACpB,KAAKE,EAAa,aAAA,KAChBD,EAAc,KAAO,UACrB,MAEF,KAAKC,EAAa,aAAA,MAChBD,EAAc,MAAQ,QACtB,MAEF,KAAKC,EAAa,aAAA,WAChBD,EAAc,IAAM,UACpB,MAEF,KAAKC,EAAa,aAAA,KAChBD,EAAc,OAAS,GACvBA,EAAc,KAAO,UACrBA,EAAc,OAAS,UACvB,MAEF,KAAKC,EAAa,aAAA,gBAChBD,EAAc,OAAS,GACvBA,EAAc,KAAO,UACrBA,EAAc,OAAS,UACvBA,EAAc,OAAS,UACvB,KAAA,CAGJ,OAAOJ,EAAK,eAAe,KAAK,QAAQ,QAAQ,OAAQI,CAAa,CAAA,CAEzE"}