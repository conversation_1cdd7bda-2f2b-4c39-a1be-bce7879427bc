import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface StochasticIndicatorOptions extends ChartIndicatorOptions {
    color: string;
    signalColor: string;
    period: number;
    priceLineColor: string;
    backgroundColor: string;
    signalPeriod: number;
}
export declare const defaultOptions: StochasticIndicatorOptions;
export type KStochasticLine = Nominal<number, 'K_Stochastic'>;
export type DStochasticLine = Nominal<number, 'D_Stochastic'>;
export type StochasticData = [KStochasticLine, DStochasticLine];
export default class StochasticIndicator extends ChartIndicator<StochasticIndicatorOptions, StochasticData> {
    kSeries: ISeriesApi<SeriesType>;
    dSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<StochasticIndicatorOptions>, paneIndex?: number);
    getDefaultOptions(): StochasticIndicatorOptions;
    formula(c: Context): StochasticData | undefined;
    applyIndicatorData(): void;
    remove(): void;
    _applyOptions(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
