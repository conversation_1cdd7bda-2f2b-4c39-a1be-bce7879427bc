{"version": 3, "file": "data-feed.es.js", "sources": ["../../src/advance-chart/data-feed.ts"], "sourcesContent": ["import { Logical, LogicalRange, Time } from 'lightweight-charts';\r\nimport { Destroyable, OHLCVSimple } from '../interface';\r\nimport { dayjsToTime, timeToDate, timeToDayjs } from '../helpers/utils';\r\nimport { AdvanceChart } from './advance-chart';\r\nimport {mergeOhlcData} from '../helpers/mergeData';\r\nimport {Delegate, IPublicDelegate} from '../helpers/delegate';\r\nimport {Interval, Period} from './i-advance-chart';\r\nimport {groupBy} from 'es-toolkit';\r\nimport dayjs from '../helpers/dayjs-setup';\r\nimport {timeKey} from './time-key';\r\nimport {log} from '../helpers/log';\r\nimport {Dayjs} from 'dayjs';\r\n\r\nexport interface IDataFetchQuery {\r\n  from: Date;\r\n  to: Date;\r\n  interval: Interval;\r\n}\r\n\r\nexport type IDataFetchUtils = {\r\n  forward: (time: Time, step: number) => Dayjs\r\n}\r\n\r\nexport interface IDataFetch {\r\n  refeshTime?: number;\r\n\r\n  /**\r\n   * Fetches the initial data when:\r\n   * - The chart is first loaded\r\n   * - The user changes the visible time range\r\n   * - The interval/period is changed\r\n   * \r\n   * This provides the base dataset that the chart will display initially.\r\n   * Subsequent updates/pagination will build upon this data.\r\n   */\r\n  fetchInitialData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;\r\n\r\n  /**\r\n   * Fetches historical data that's outside the current visible chart range.\r\n   * This data is used to:\r\n   * - Calculate indicators that require lookback periods\r\n   * - Provide seamless scrolling experience when user reaches chart boundaries\r\n   * Automatically triggered when scrolling to the beginning/end of loaded data.\r\n   */\r\n  fetchPaginationData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;\r\n\r\n  /**\r\n   * Fetches updated data periodically based on refreshTime interval.\r\n   * Queries data from the timestamp of last data point to current time.\r\n   * New data points with same timestamps will overwrite existing ones.\r\n   * Used for real-time updates to the chart.\r\n   */\r\n  fetchUpdateData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;\r\n}\r\n\r\nexport function roundTime(time: Time, period: Period) {\r\n  switch (period) {\r\n    case Period.minute:\r\n    case Period.hour:\r\n    case Period.day:\r\n      return time;\r\n    case Period.week:\r\n      return dayjs.tz(timeToDayjs(time), 'UTC').startOf('week').unix() as Time;\r\n    case Period.month:\r\n      return dayjs.tz(timeToDayjs(time), 'UTC').startOf('month').unix() as Time;\r\n    default:\r\n      throw new Error(`Period : ${period} not support`)\r\n  }\r\n}\r\n\r\nexport const aggregate = (items: OHLCVSimple[]) => {\r\n  const high = Math.max(...items.map(item => item.high))\r\n  const low = Math.min(...items.map(item => item.low))\r\n  const volume = items.reduce((acc, item) => acc + item.volume, 0)\r\n  const open = items[0].open\r\n  const close = items[items.length - 1].close\r\n  return {\r\n    time: items[0].time, \r\n    open,\r\n    high,\r\n    low,\r\n    close,\r\n    volume\r\n  } satisfies OHLCVSimple\r\n}\r\n\r\n\r\nexport class DataFeed implements Destroyable {\r\n  _loading: boolean = false;\r\n  _data: OHLCVSimple[] = [];\r\n  _dataChanged = new Delegate();\r\n  interval: Interval = { period: Period.day, times: 1 };\r\n  initialData = false;\r\n  endOfData = false;\r\n  _refeshTimer: NodeJS.Timeout | null = null;\r\n\r\n  _destroyed = false\r\n\r\n  \r\n  constructor(\r\n    public advanceChart: AdvanceChart,\r\n    protected dataFetch: IDataFetch\r\n  ) {\r\n    this.onVisibleLogicalRangeChange =\r\n      this.onVisibleLogicalRangeChange.bind(this);\r\n    this.advanceChart.chartApi\r\n      .timeScale()\r\n      .subscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange);\r\n\r\n    if(this.dataFetch.refeshTime) {\r\n      this._refeshTimer = setInterval(async() => this.updateData(), this.dataFetch.refeshTime)\r\n    }\r\n  }\r\n\r\n  get data () {\r\n    return this._data\r\n  }\r\n\r\n  set data (d: OHLCVSimple[]) {\r\n    this._data = d;\r\n\r\n    this._dataChanged.fire()\r\n  }\r\n\r\n  isNeedPaging(logicalRange: LogicalRange) {\r\n    if (!this.initialData) return false;\r\n    if (this.data.length === 0) return false;\r\n    if (this.endOfData) return false;\r\n    const { from } = logicalRange;\r\n    if (from > 30) return false;\r\n    return true\r\n  }\r\n\r\n\r\n\r\n  groupData() {\r\n    const interval = this.interval\r\n\r\n    if(interval.period === Period.day) return this._data\r\n    if(interval.period === Period.hour) return this._data\r\n    if(interval.period === Period.minute) return this._data\r\n\r\n    const data = groupBy(this._data, item => timeKey(item.time, interval));\r\n\r\n    return Object.entries(data)\r\n      .map(([keyBy, items]) => [parseInt(keyBy), aggregate(items)] as const)\r\n      .sort((a, b) => a[0] - b[0]).map(item => item[1]);\r\n  }\r\n\r\n  processNewData(data: OHLCVSimple[]) {\r\n    const newData = mergeOhlcData(data, this.data);\r\n    if (newData.length === this.data.length) return;\r\n\r\n    this.data = newData\r\n    this.advanceChart.setData(this.groupData(), this.interval);\r\n\r\n    return newData;\r\n  }\r\n\r\n  async updateData() {\r\n    const lastData = this.data[this.data.length - 1];\r\n    const lastTime = lastData.time;\r\n    const data = await this.dataFetch.fetchUpdateData({\r\n      interval: this.interval,\r\n      from: timeToDate(lastTime),\r\n      to: new Date\r\n    }, { forward: this.forward.bind(this) })\r\n\r\n    if(this._destroyed) return\r\n\r\n    this.processNewData(data)\r\n  }\r\n\r\n  async pagingData(logicalRange: LogicalRange) {\r\n    const { from } = logicalRange;\r\n    const toTime = this.data[0].time;\r\n    const toDate = timeToDayjs(toTime);\r\n    const fromDate = this.forward(toTime, from - 200);\r\n    \r\n    const data = await this.dataFetch.fetchPaginationData({\r\n      interval: this.interval,\r\n      from: fromDate.toDate(),\r\n      to: toDate.toDate(),\r\n    }, { forward: this.forward.bind(this) });\r\n\r\n    if(this._destroyed) return\r\n    \r\n    if(!this.processNewData(data)) {\r\n      this.endOfData = true;\r\n    }\r\n  }\r\n\r\n  async onVisibleLogicalRangeChange(logicalRange: LogicalRange | null) {\r\n    if(!logicalRange) return;\r\n    const isSameRange = (range1: LogicalRange, range2: LogicalRange) => range1.from === range2.from && range1.to === range2.to\r\n    if(this.advanceChart.loading) return\r\n    this.advanceChart.loading = true\r\n    try {\r\n      while(this.isNeedPaging(logicalRange)) {\r\n        await this.pagingData(logicalRange)\r\n        const newRange = this.advanceChart.chartApi.timeScale().getVisibleLogicalRange();\r\n        if(!newRange) break;\r\n        if(isSameRange(newRange, logicalRange)) break;\r\n        logicalRange = newRange\r\n      }\r\n    } finally {\r\n      this.advanceChart.loading = false\r\n    }\r\n  }\r\n\r\n  forward(time: Time, step: number) {\r\n    step = Math.round(step);\r\n    const period = this.interval.period\r\n    switch (period) {\r\n      case Period.minute:\r\n        return timeToDayjs(time).add(step, 'minute');\r\n      case Period.hour:\r\n        return timeToDayjs(time).add(step, 'hour');\r\n      case Period.day:\r\n        return timeToDayjs(time).add(step, 'day');\r\n      case Period.week:\r\n        return timeToDayjs(time).add(step, 'week');\r\n      case Period.month:\r\n        return timeToDayjs(time).add(step, 'month');\r\n      default:\r\n        throw new Error(`Period : ${period} not support`)\r\n    }\r\n  }\r\n\r\n  async setRange({ from, to, interval }: IDataFetchQuery) {\r\n    this.resetState();\r\n    \r\n    this.advanceChart.loading = true;\r\n    this.interval = interval;\r\n    const data = await this.dataFetch.fetchInitialData({ from, to, interval }, { forward: this.forward.bind(this) });\r\n    this.data = data;\r\n    this.advanceChart.loading = false;\r\n\r\n    if(this._destroyed) return\r\n\r\n    this.advanceChart.setData(this.groupData(), interval);\r\n    this.initialData = true;\r\n\r\n    const timeScale = this.advanceChart.chartApi.timeScale();\r\n\r\n    const fromIndex = timeScale.timeToIndex(dayjsToTime(dayjs(from)), true)\r\n    const toIndex = timeScale.timeToIndex(dayjsToTime(dayjs(to)), true)\r\n\r\n    if(fromIndex !== undefined && toIndex !== undefined) {\r\n      this.advanceChart.fitRange({ from: fromIndex as unknown as Logical, to: toIndex as unknown as Logical })\r\n    }\r\n    \r\n    await this.onVisibleLogicalRangeChange(\r\n      timeScale.getVisibleLogicalRange()\r\n    );\r\n  }\r\n\r\n  private resetState() {\r\n    this.advanceChart.loading = false;\r\n    this.initialData = false;\r\n    this.endOfData = false;\r\n  }\r\n\r\n  dataChanged (){\r\n    return this._dataChanged as IPublicDelegate<typeof this._dataChanged>\r\n  }\r\n\r\n  trade(trade: { time: Time, price: number, volume: number }) {\r\n    const [lastPoint] = this.advanceChart.lastPoint();\r\n\r\n    const currentKey = timeKey(lastPoint.time, this.interval)\r\n    const newKey = timeKey(trade.time, this.interval)\r\n    if(newKey < currentKey) {\r\n      log.warn(`Trade timestamp ${newKey} is older than current ${currentKey}`)\r\n      return;\r\n    }\r\n    if(currentKey === newKey) {\r\n      this.advanceChart.update({\r\n        open: lastPoint.open,\r\n        high: Math.max(lastPoint.high, trade.price),\r\n        low: Math.min(lastPoint.low, trade.price),\r\n        close: trade.price,\r\n        volume: lastPoint.volume + trade.volume,\r\n        time: trade.time\r\n      }, true)\r\n    } else {\r\n      this.advanceChart.update({\r\n        open: trade.price,\r\n        high: trade.price,\r\n        low: trade.price,\r\n        close: trade.price,\r\n        volume: trade.volume,\r\n        time: trade.time\r\n      })\r\n    }\r\n  }\r\n\r\n  destroy() {\r\n    this._dataChanged.destroy();\r\n    this.advanceChart.chartApi\r\n      .timeScale()\r\n      .unsubscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange);\r\n\r\n    if(this._refeshTimer) {\r\n      clearInterval(this._refeshTimer);\r\n      this._refeshTimer = null;\r\n    }\r\n  }\r\n}\r\n"], "names": ["roundTime", "time", "period", "Period", "dayjs", "timeToD<PERSON><PERSON><PERSON>", "aggregate", "items", "high", "item", "low", "volume", "acc", "open", "close", "DataFeed", "advance<PERSON>hart", "dataFetch", "__publicField", "Delegate", "d", "logicalRange", "from", "interval", "data", "groupBy", "<PERSON><PERSON><PERSON>", "keyBy", "a", "b", "newData", "mergeOhlcData", "lastTime", "timeToDate", "toTime", "toDate", "fromDate", "isSameRange", "range1", "range2", "newRange", "step", "to", "timeScale", "fromIndex", "dayjsToTime", "toIndex", "trade", "lastPoint", "current<PERSON><PERSON>", "new<PERSON>ey", "log"], "mappings": ";;;;;;;;;;;;AAuDgB,SAAAA,EAAUC,GAAYC,GAAgB;AACpD,UAAQA,GAAQ;AAAA,IACd,KAAKC,EAAO;AAAA,IACZ,KAAKA,EAAO;AAAA,IACZ,KAAKA,EAAO;AACH,aAAAF;AAAA,IACT,KAAKE,EAAO;AACH,aAAAC,EAAM,GAAGC,EAAYJ,CAAI,GAAG,KAAK,EAAE,QAAQ,MAAM,EAAE,KAAK;AAAA,IACjE,KAAKE,EAAO;AACH,aAAAC,EAAM,GAAGC,EAAYJ,CAAI,GAAG,KAAK,EAAE,QAAQ,OAAO,EAAE,KAAK;AAAA,IAClE;AACE,YAAM,IAAI,MAAM,YAAYC,CAAM,cAAc;AAAA,EAAA;AAEtD;AAEa,MAAAI,IAAY,CAACC,MAAyB;AAC3C,QAAAC,IAAO,KAAK,IAAI,GAAGD,EAAM,IAAI,CAAAE,MAAQA,EAAK,IAAI,CAAC,GAC/CC,IAAM,KAAK,IAAI,GAAGH,EAAM,IAAI,CAAAE,MAAQA,EAAK,GAAG,CAAC,GAC7CE,IAASJ,EAAM,OAAO,CAACK,GAAKH,MAASG,IAAMH,EAAK,QAAQ,CAAC,GACzDI,IAAON,EAAM,CAAC,EAAE,MAChBO,IAAQP,EAAMA,EAAM,SAAS,CAAC,EAAE;AAC/B,SAAA;AAAA,IACL,MAAMA,EAAM,CAAC,EAAE;AAAA,IACf,MAAAM;AAAA,IACA,MAAAL;AAAA,IACA,KAAAE;AAAA,IACA,OAAAI;AAAA,IACA,QAAAH;AAAA,EACF;AACF;AAGO,MAAMI,EAAgC;AAAA,EAY3C,YACSC,GACGC,GACV;AAdF,IAAAC,EAAA,kBAAoB;AACpB,IAAAA,EAAA,eAAuB,CAAC;AACxB,IAAAA,EAAA,sBAAe,IAAIC,EAAS;AAC5B,IAAAD,EAAA,kBAAqB,EAAE,QAAQf,EAAO,KAAK,OAAO,EAAE;AACpD,IAAAe,EAAA,qBAAc;AACd,IAAAA,EAAA,mBAAY;AACZ,IAAAA,EAAA,sBAAsC;AAEtC,IAAAA,EAAA,oBAAa;AAIJ,SAAA,eAAAF,GACG,KAAA,YAAAC,GAEV,KAAK,8BACH,KAAK,4BAA4B,KAAK,IAAI,GAC5C,KAAK,aAAa,SACf,UACA,EAAA,mCAAmC,KAAK,2BAA2B,GAEnE,KAAK,UAAU,eACX,KAAA,eAAe,YAAY,YAAW,KAAK,cAAc,KAAK,UAAU,UAAU;AAAA,EACzF;AAAA,EAGF,IAAI,OAAQ;AACV,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,IAAI,KAAMG,GAAkB;AAC1B,SAAK,QAAQA,GAEb,KAAK,aAAa,KAAK;AAAA,EAAA;AAAA,EAGzB,aAAaC,GAA4B;AAGnC,QAFA,CAAC,KAAK,eACN,KAAK,KAAK,WAAW,KACrB,KAAK,UAAkB,QAAA;AACrB,UAAA,EAAE,MAAAC,MAASD;AACb,WAAA,EAAAC,IAAO;AAAA,EACJ;AAAA,EAKT,YAAY;AACV,UAAMC,IAAW,KAAK;AAEtB,QAAGA,EAAS,WAAWpB,EAAO,YAAY,KAAK;AAC/C,QAAGoB,EAAS,WAAWpB,EAAO,aAAa,KAAK;AAChD,QAAGoB,EAAS,WAAWpB,EAAO,eAAe,KAAK;AAE5C,UAAAqB,IAAOC,EAAQ,KAAK,OAAO,OAAQC,EAAQjB,EAAK,MAAMc,CAAQ,CAAC;AAErE,WAAO,OAAO,QAAQC,CAAI,EACvB,IAAI,CAAC,CAACG,GAAOpB,CAAK,MAAM,CAAC,SAASoB,CAAK,GAAGrB,EAAUC,CAAK,CAAC,CAAU,EACpE,KAAK,CAACqB,GAAGC,MAAMD,EAAE,CAAC,IAAIC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAQpB,MAAAA,EAAK,CAAC,CAAC;AAAA,EAAA;AAAA,EAGpD,eAAee,GAAqB;AAClC,UAAMM,IAAUC,EAAcP,GAAM,KAAK,IAAI;AAC7C,QAAIM,EAAQ,WAAW,KAAK,KAAK;AAEjC,kBAAK,OAAOA,GACZ,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,KAAK,QAAQ,GAElDA;AAAA,EAAA;AAAA,EAGT,MAAM,aAAa;AAEjB,UAAME,IADW,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,EACrB,MACpBR,IAAO,MAAM,KAAK,UAAU,gBAAgB;AAAA,MAChD,UAAU,KAAK;AAAA,MACf,MAAMS,EAAWD,CAAQ;AAAA,MACzB,IAAQ,oBAAA,KAAA;AAAA,IAAA,GACP,EAAE,SAAS,KAAK,QAAQ,KAAK,IAAI,GAAG;AAEvC,IAAG,KAAK,cAER,KAAK,eAAeR,CAAI;AAAA,EAAA;AAAA,EAG1B,MAAM,WAAWH,GAA4B;AACrC,UAAA,EAAE,MAAAC,MAASD,GACXa,IAAS,KAAK,KAAK,CAAC,EAAE,MACtBC,IAAS9B,EAAY6B,CAAM,GAC3BE,IAAW,KAAK,QAAQF,GAAQZ,IAAO,GAAG,GAE1CE,IAAO,MAAM,KAAK,UAAU,oBAAoB;AAAA,MACpD,UAAU,KAAK;AAAA,MACf,MAAMY,EAAS,OAAO;AAAA,MACtB,IAAID,EAAO,OAAO;AAAA,IAAA,GACjB,EAAE,SAAS,KAAK,QAAQ,KAAK,IAAI,GAAG;AAEvC,IAAG,KAAK,cAEJ,KAAK,eAAeX,CAAI,MAC1B,KAAK,YAAY;AAAA,EACnB;AAAA,EAGF,MAAM,4BAA4BH,GAAmC;AACnE,QAAG,CAACA,EAAc;AACZ,UAAAgB,IAAc,CAACC,GAAsBC,MAAyBD,EAAO,SAASC,EAAO,QAAQD,EAAO,OAAOC,EAAO;AACrH,QAAA,MAAK,aAAa,SACrB;AAAA,WAAK,aAAa,UAAU;AACxB,UAAA;AACI,eAAA,KAAK,aAAalB,CAAY,KAAG;AAC/B,gBAAA,KAAK,WAAWA,CAAY;AAClC,gBAAMmB,IAAW,KAAK,aAAa,SAAS,YAAY,uBAAuB;AAE5E,cADA,CAACA,KACDH,EAAYG,GAAUnB,CAAY,EAAG;AACzB,UAAAA,IAAAmB;AAAA,QAAA;AAAA,MACjB,UACA;AACA,aAAK,aAAa,UAAU;AAAA,MAAA;AAAA;AAAA,EAC9B;AAAA,EAGF,QAAQvC,GAAYwC,GAAc;AACzB,IAAAA,IAAA,KAAK,MAAMA,CAAI;AAChB,UAAAvC,IAAS,KAAK,SAAS;AAC7B,YAAQA,GAAQ;AAAA,MACd,KAAKC,EAAO;AACV,eAAOE,EAAYJ,CAAI,EAAE,IAAIwC,GAAM,QAAQ;AAAA,MAC7C,KAAKtC,EAAO;AACV,eAAOE,EAAYJ,CAAI,EAAE,IAAIwC,GAAM,MAAM;AAAA,MAC3C,KAAKtC,EAAO;AACV,eAAOE,EAAYJ,CAAI,EAAE,IAAIwC,GAAM,KAAK;AAAA,MAC1C,KAAKtC,EAAO;AACV,eAAOE,EAAYJ,CAAI,EAAE,IAAIwC,GAAM,MAAM;AAAA,MAC3C,KAAKtC,EAAO;AACV,eAAOE,EAAYJ,CAAI,EAAE,IAAIwC,GAAM,OAAO;AAAA,MAC5C;AACE,cAAM,IAAI,MAAM,YAAYvC,CAAM,cAAc;AAAA,IAAA;AAAA,EACpD;AAAA,EAGF,MAAM,SAAS,EAAE,MAAAoB,GAAM,IAAAoB,GAAI,UAAAnB,KAA6B;AACtD,SAAK,WAAW,GAEhB,KAAK,aAAa,UAAU,IAC5B,KAAK,WAAWA;AAChB,UAAMC,IAAO,MAAM,KAAK,UAAU,iBAAiB,EAAE,MAAAF,GAAM,IAAAoB,GAAI,UAAAnB,EAAS,GAAG,EAAE,SAAS,KAAK,QAAQ,KAAK,IAAI,GAAG;AAI/G,QAHA,KAAK,OAAOC,GACZ,KAAK,aAAa,UAAU,IAEzB,KAAK,WAAY;AAEpB,SAAK,aAAa,QAAQ,KAAK,UAAA,GAAaD,CAAQ,GACpD,KAAK,cAAc;AAEnB,UAAMoB,IAAY,KAAK,aAAa,SAAS,UAAU,GAEjDC,IAAYD,EAAU,YAAYE,EAAYzC,EAAMkB,CAAI,CAAC,GAAG,EAAI,GAChEwB,IAAUH,EAAU,YAAYE,EAAYzC,EAAMsC,CAAE,CAAC,GAAG,EAAI;AAE/D,IAAAE,MAAc,UAAaE,MAAY,UACxC,KAAK,aAAa,SAAS,EAAE,MAAMF,GAAiC,IAAIE,GAA+B,GAGzG,MAAM,KAAK;AAAA,MACTH,EAAU,uBAAuB;AAAA,IACnC;AAAA,EAAA;AAAA,EAGM,aAAa;AACnB,SAAK,aAAa,UAAU,IAC5B,KAAK,cAAc,IACnB,KAAK,YAAY;AAAA,EAAA;AAAA,EAGnB,cAAc;AACZ,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,MAAMI,GAAsD;AAC1D,UAAM,CAACC,CAAS,IAAI,KAAK,aAAa,UAAU,GAE1CC,IAAavB,EAAQsB,EAAU,MAAM,KAAK,QAAQ,GAClDE,IAASxB,EAAQqB,EAAM,MAAM,KAAK,QAAQ;AAChD,QAAGG,IAASD,GAAY;AACtB,MAAAE,EAAI,KAAK,mBAAmBD,CAAM,0BAA0BD,CAAU,EAAE;AACxE;AAAA,IAAA;AAEF,IAAGA,MAAeC,IAChB,KAAK,aAAa,OAAO;AAAA,MACvB,MAAMF,EAAU;AAAA,MAChB,MAAM,KAAK,IAAIA,EAAU,MAAMD,EAAM,KAAK;AAAA,MAC1C,KAAK,KAAK,IAAIC,EAAU,KAAKD,EAAM,KAAK;AAAA,MACxC,OAAOA,EAAM;AAAA,MACb,QAAQC,EAAU,SAASD,EAAM;AAAA,MACjC,MAAMA,EAAM;AAAA,OACX,EAAI,IAEP,KAAK,aAAa,OAAO;AAAA,MACvB,MAAMA,EAAM;AAAA,MACZ,MAAMA,EAAM;AAAA,MACZ,KAAKA,EAAM;AAAA,MACX,OAAOA,EAAM;AAAA,MACb,QAAQA,EAAM;AAAA,MACd,MAAMA,EAAM;AAAA,IAAA,CACb;AAAA,EACH;AAAA,EAGF,UAAU;AACR,SAAK,aAAa,QAAQ,GAC1B,KAAK,aAAa,SACf,UACA,EAAA,qCAAqC,KAAK,2BAA2B,GAErE,KAAK,iBACN,cAAc,KAAK,YAAY,GAC/B,KAAK,eAAe;AAAA,EACtB;AAEJ;"}