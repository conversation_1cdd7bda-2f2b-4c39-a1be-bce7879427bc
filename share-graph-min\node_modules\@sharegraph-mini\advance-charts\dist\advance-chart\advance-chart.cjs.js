"use strict";var g=Object.defineProperty;var S=(p,e,i)=>e in p?g(p,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):p[e]=i;var o=(p,e,i)=>S(p,typeof e!="symbol"?e+"":e,i);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const h=require("lightweight-charts"),m=require("../indicators/abstract-indicator.cjs.js"),c=require("../helpers/delegate.cjs.js");require("../indicators/index.cjs.js");const l=require("../helpers/utils.cjs.js"),u=require("es-toolkit"),C=require("../helpers/number-formatter.cjs.js"),y=require("./i-advance-chart.cjs.js"),b=require("./DisplayTimezone.cjs.js"),v=require("../indicators/indicator-factory.cjs.js"),f={upColor:m.upColor,downColor:m.downColor,mainColor:"#3594e7",highLowLineVisible:!1,highLineColor:m.upColor,lowLineColor:m.downColor,priceScaleMode:h.PriceScaleMode.Normal,priceLineVisible:!1,locale:"en",gridColor:"#f2f2f2",axesColor:"#333",tzDisplay:Intl.DateTimeFormat().resolvedOptions().timeZone,height:500};class _{constructor(e,i){o(this,"options");o(this,"chartApi");o(this,"chartType",null);o(this,"mainSeries",null);o(this,"dataInterval",{period:y.Period.day,times:1});o(this,"__destroyed",!1);o(this,"data",[]);o(this,"indicators",new Map);o(this,"_volumeType");o(this,"_chartTypeChanged",new c.Delegate);o(this,"_indicatorChanged",new c.Delegate);o(this,"_destroyed",new c.Delegate);o(this,"_updated",new c.Delegate);o(this,"_crosshairMoved",new c.Delegate);o(this,"_chartHovered",new c.Delegate);o(this,"_dataSetChanged",new c.Delegate);o(this,"_loading",new c.Delegate);o(this,"_optionChanged",new c.Delegate);o(this,"_mainSeriesChanged",new c.Delegate);o(this,"_displayTimezone");this.options=Object.freeze(u.merge(u.cloneDeep(f),i??{})),this.chartApi=h.createChart(e,{layout:{attributionLogo:!1,panes:{separatorColor:"#e0e3eb",enableResize:!1},fontSize:this.options.fontSize,fontFamily:this.options.fontFamily,textColor:this.options.axesColor},autoSize:!0,height:this.options.height,localization:{locale:this.options.locale,percentageFormatter:t=>this.numberFormatter.percent(t/100),timeFormatter:t=>this._displayTimezone.format(l.timeToDate(t))},timeScale:{borderVisible:!1,rightOffset:10,maxBarSpacing:40,minBarSpacing:4,secondsVisible:!0,timeVisible:!0,tickMarkFormatter:(t,r)=>this._displayTimezone.tickMarkFormatter(l.timeToDate(t),r)},overlayPriceScales:{scaleMargins:{bottom:.05,top:.05}},leftPriceScale:{borderVisible:!1},handleScale:{axisPressedMouseMove:!1},rightPriceScale:{borderVisible:!1,mode:this.options.priceScaleMode},grid:{horzLines:{visible:!1},vertLines:{color:this.options.gridColor}}}),this.chartApi.subscribeCrosshairMove(t=>{if(t.time===void 0||!this.mainSeries)return;const n=l.binarySearchIndex(this.data,l.timeToUnix(t.time),d=>l.timeToUnix(d.time));if(n===-1)return;const[a,s]=this.getPointFromIndex(n);this._crosshairMoved.fire(a,s)}),this.chartApi.subscribeCrosshairMove(t=>{if(t.logical===void 0)return this._chartHovered.fire(!1);this._chartHovered.fire(!0)}),this.chartApi.timeScale().subscribeVisibleTimeRangeChange(t=>{if(!t)return this._dataSetChanged.fire([]);this._dataSetChanged.fire(this.dataSet)}),this._dataSetChanged.subscribe(()=>{this.tryDrawUpDownLine(),this.updateBaselineChartType()}),this._displayTimezone=new b.DisplayTimezone(this)}get numberFormatter(){return C.NumberFormatterFactory.formatter(this.options.locale??"en")}get dataSet(){const e=this.chartApi.timeScale().getVisibleRange();if(!e)return[];const{from:i,to:t}=e,r=l.binarySearchIndex(this.data,l.timeToUnix(i),a=>l.timeToUnix(a.time)),n=l.binarySearchIndex(this.data,l.timeToUnix(t),a=>l.timeToUnix(a.time));return this.data.slice(r,n+1)}getData(){return this.data}getIndicators(){return Array.from(this.indicators.values())}getPointFromIndex(e){const i=this.data[e],t=this.data[e>0?e-1:e];return[i,t]}lastPoint(){return this.getPointFromIndex(this.data.length-1)}setChartType(e){if(e===this.chartType)return;let i;switch(e){case"line":i=this.chartApi.addSeries(h.LineSeries,{color:this.options.mainColor,priceLineVisible:this.options.priceLineVisible,lastPriceAnimation:h.LastPriceAnimationMode.OnDataUpdate,lineWidth:2},0);break;case"candle":i=this.chartApi.addSeries(h.CandlestickSeries,{upColor:this.options.upColor,downColor:this.options.downColor,priceLineVisible:this.options.priceLineVisible},0);break;case"mountain":i=this.chartApi.addSeries(h.AreaSeries,{topColor:this.options.mainColor,lineColor:this.options.mainColor,bottomColor:"#ffffff00",priceLineVisible:this.options.priceLineVisible,lastPriceAnimation:h.LastPriceAnimationMode.OnDataUpdate,lineWidth:2},0);break;case"bar":i=this.chartApi.addSeries(h.BarSeries,{upColor:this.options.upColor,downColor:this.options.downColor,priceLineVisible:this.options.priceLineVisible},0);break;case"baseline":i=this.chartApi.addSeries(h.BaselineSeries,{topLineColor:this.options.upColor,bottomLineColor:this.options.downColor,bottomFillColor1:"transparent",bottomFillColor2:"transparent",topFillColor1:"transparent",topFillColor2:"transparent",priceLineVisible:this.options.priceLineVisible,lastPriceAnimation:h.LastPriceAnimationMode.OnDataUpdate,lineWidth:2},0);break;case"base-mountain":i=this.chartApi.addSeries(h.BaselineSeries,{topLineColor:this.options.upColor,bottomLineColor:this.options.downColor,priceLineVisible:this.options.priceLineVisible,lastPriceAnimation:h.LastPriceAnimationMode.OnDataUpdate,lineWidth:2},0);break;default:throw new Error("Invalid chart type")}this.mainSeries&&this.chartApi.removeSeries(this.mainSeries),this.chartType=e,this.mainSeries=i,this.updateBaselineChartType(),this.applyMainSeriesData(),this.tryDrawUpDownLine(),Array.from(this.indicators.values()).forEach(t=>{var r;return(r=t.mainSeriesChanged)==null?void 0:r.call(t,i)}),this._updated.fire(),this._chartTypeChanged.fire()}updateBaselineChartType(){var e;if(this.mainSeries&&(this.chartType==="baseline"||this.chartType==="base-mountain")){const i=(e=this.dataSet.at(0))==null?void 0:e.close;i&&this.mainSeries.applyOptions({baseValue:{price:i}})}}setData(e,i){this.dataInterval=i,this.data=e.map(r=>({...r,value:r.close,customValues:r})),this.applyData(),this.chartApi.timeScale().getVisibleRange()&&this._dataSetChanged.fire(this.dataSet),this._updated.fire()}applyMainSeriesData(){this.mainSeries&&this.mainSeries.setData(this.data)}applyData(){this.applyMainSeriesData(),Array.from(this.indicators.values()).map(e=>e.setData(this.data))}update(e,i=!1){var n;const[t]=this.lastPoint(),r={...e,value:e.close,customValues:e,time:i?t.time:e.time};if(i)this.data.splice(this.data.length-1,1,r);else{this.data.push(r);const a=this.chartApi.timeScale().getVisibleLogicalRange();if(a&&Math.floor(a.to)>this.data.length){const s={from:a.from,to:a.to};this.chartApi.timeScale().setVisibleLogicalRange(s)}}(n=this.mainSeries)==null||n.update(r),Array.from(this.indicators.values()).map(a=>a.update()),this.tryDrawUpDownLine(),this._updated.fire()}tryDrawUpDownLine(){var a;if(!this.mainSeries)return;if(!this.options.highLowLineVisible){this.mainSeries.priceLines().forEach(s=>{var d;return(d=this.mainSeries)==null?void 0:d.removePriceLine(s)});return}const e=this.dataSet,i=e.reduce((s,d)=>Math.min(s,d.close),Number.MAX_SAFE_INTEGER),t=e.reduce((s,d)=>Math.max(s,d.close),Number.MIN_SAFE_INTEGER),[r,n]=((a=this.mainSeries)==null?void 0:a.priceLines())??[];r?r.applyOptions({price:i,color:this.options.downColor}):this.mainSeries.createPriceLine({price:i,color:this.options.downColor}),n?n.applyOptions({price:t,color:this.options.upColor}):this.mainSeries.createPriceLine({price:t,color:this.options.upColor})}applyOptions(e){var i;this.options=u.merge(u.cloneDeep(this.options),e),e.highLowLineVisible!==void 0&&this.tryDrawUpDownLine(),e.priceScaleMode!==void 0&&this.chartApi.applyOptions({rightPriceScale:{mode:e.priceScaleMode}}),e.priceLineVisible!==void 0&&((i=this.mainSeries)==null||i.applyOptions({priceLineVisible:e.priceLineVisible})),e.locale&&this.chartApi.applyOptions({localization:{locale:e.locale}})}isShowVolume(){return!!this._volumeType}showVolume(e="volume_overlay",i){if(!this.mainSeries||!e||this.hasIndicator(e))return;const t=this.addIndicator(e);i&&t.applyOptions(i),this._volumeType!==e&&(this._volumeType&&this.removeIndicator(this._volumeType),this._volumeType=e);const r=t.getPaneIndex();if(r===0)return;const n=Array.from(this.indicators.values()).map(a=>{if(a===t)return;const s=a.getPaneIndex();if(s!==0&&!(s>=r))return{paneIndex:s,indicator:a}});t.setPaneIndex(1),n.map(a=>{a&&a.indicator.setPaneIndex(a.paneIndex+1)})}hiddenVolume(){this._volumeType&&this.removeIndicator(this._volumeType)}listIndicators(){return Array.from(this.indicators.keys()).filter(e=>e!==this._volumeType)}addIndicator(e){var t;const i=v.IndicatorFactory.createIndicator(e,this.chartApi,{numberFormatter:()=>this.numberFormatter,upColor:this.options.upColor,downColor:this.options.downColor},this.chartApi.panes().length);return i.setData(this.data),this.indicators.set(e,i),this._updated.fire(),this._indicatorChanged.fire(e,"add"),this.mainSeries&&((t=i.mainSeriesChanged)==null||t.call(i,this.mainSeries)),i}removeIndicator(e){const i=this.indicators.get(e);i&&(i.remove(),this.indicators.delete(e),this._updated.fire(),this._indicatorChanged.fire(e,"remove"))}hasIndicator(e){return this.indicators.has(e)}remove(){Array.from(this.indicators.values()).map(e=>e.remove()),this.indicators.clear(),this.chartApi.remove(),this._destroyed.fire(),this._chartTypeChanged.destroy(),this._indicatorChanged.destroy(),this._destroyed.destroy(),this._updated.destroy(),this._crosshairMoved.destroy(),this._chartHovered.destroy(),this.__destroyed=!0}groupIndicatorByPane(){const e=Array.from(this.indicators.values()),i=Array.from(this.chartApi.panes()).map(t=>({pane:t,indicators:[]}));for(const t of e)i[t.getPaneIndex()].indicators.push(t);return i}fitRange(e){const i=this.chartApi.options().timeScale.rightOffset,t=e.to-e.from,r=this.maxBar-i;this.chartApi.timeScale().setVisibleLogicalRange({from:e.from,to:(t<r?e.from+r:e.to)+i})}fitContent(){const e={from:0,to:Math.max(this.maxBar,this.data.length)};return this.fitRange(e),e}get maxBar(){const e=this.chartApi.options().timeScale.maxBarSpacing,i=this.chartApi.timeScale().width();return Math.round(i/e)}get loading(){var e;return!!((e=this._loading.lastParams())!=null&&e[0])}set loading(e){this._loading.fire(e)}updated(){return this._updated}chartTypeChanged(){return this._chartTypeChanged}indicatorChanged(){return this._indicatorChanged}crosshairMoved(){return this._crosshairMoved}destroyed(){return this._destroyed}chartHovered(){return this._chartHovered}onLoading(){return this._loading}optionChanged(){return this._optionChanged}mainSeriesChanged(){return this._mainSeriesChanged}}exports.AdvanceChart=_;exports.defaultAdvanceChartOptions=f;
//# sourceMappingURL=advance-chart.cjs.js.map
