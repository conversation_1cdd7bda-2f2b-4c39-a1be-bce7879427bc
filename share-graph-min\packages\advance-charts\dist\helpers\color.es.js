import { memoize as a } from "es-toolkit";
function d(r) {
  const t = document.createElement("div");
  t.style.display = "none", document.body.appendChild(t), t.style.color = r;
  const e = window.getComputedStyle(t).color;
  return document.body.removeChild(t), e;
}
const n = a((r) => {
  const e = d(r).match(
    /^rgba?\s*\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)$/
  );
  if (!e)
    throw new Error(`Failed to parse color: ${r}`);
  return [
    parseInt(e[1], 10),
    parseInt(e[2], 10),
    parseInt(e[3], 10),
    e[4] ? parseFloat(e[4]) : 1
  ];
});
class p {
  /**
  * We fallback to RGBA here since supporting alpha transformations
  * on wider color gamuts would currently be a lot of extra code
  * for very little benefit due to actual usage.
  */
  static applyAlpha(t, e) {
    if (t === "transparent" || e === 1) return t;
    const o = n(t), s = o[3];
    return `rgba(${o[0]}, ${o[1]}, ${o[2]}, ${e * s})`;
  }
  static parseColor(t) {
    return n(t);
  }
}
export {
  p as Color,
  n as parseColor
};
//# sourceMappingURL=color.es.js.map
