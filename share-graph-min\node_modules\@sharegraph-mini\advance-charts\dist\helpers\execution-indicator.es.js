var r = Object.defineProperty;
var o = (h, i, t) => i in h ? r(h, i, { enumerable: !0, configurable: !0, writable: !0, value: t }) : h[i] = t;
var s = (h, i, t) => o(h, typeof i != "symbol" ? i + "" : i, t);
class n {
  constructor() {
    s(this, "_varIndex", 0);
    s(this, "_vars", []);
    s(this, "symbol", {
      time: NaN,
      close: NaN,
      high: NaN,
      low: NaN,
      open: NaN,
      volume: NaN,
      isNew: !0
    });
  }
  new_var(i, t) {
    this._varIndex >= this._vars.length && this._vars.push(new _(t));
    const e = this._vars[this._varIndex++];
    return e.set(i), e;
  }
  prepare(i) {
    this._varIndex = 0, this.symbol = i, this._vars.forEach((t) => t.prepare(i));
  }
}
class _ {
  constructor(i) {
    s(this, "_his", null);
    s(this, "_hisPosition", 0);
    s(this, "_minDepth", 0);
    s(this, "origin", NaN);
    s(this, "modified", !1);
    this._his = Array(i).fill(NaN), this._hisPosition = i - 1, this._minDepth = i;
  }
  valueOf() {
    return this.get(0);
  }
  get(i) {
    if (this._his) {
      const t = this._hisPosition - i;
      return this._his[t];
    }
    return this._minDepth = Math.max(this._minDepth, i), NaN;
  }
  getAll() {
    return Array.from(this._his || []).reverse();
  }
  calculable() {
    return this._his ? !this._his.some((i) => isNaN(i)) : !1;
  }
  set(i) {
    this._his && (this._his[this._hisPosition] = i, this.modified = !0);
  }
  prepare(i) {
    i.isNew ? (this.origin = this.get(0), (this.modified || !this._his) && this.addHist()) : this.set(this.origin);
  }
  addHist() {
    if (!this._his) {
      if (!this._minDepth) throw new Error("error");
      this._his = Array(this._minDepth + 1).fill(NaN);
    }
    this._hisPosition = Math.min(this._hisPosition + 1, this._his.length), this._hisPosition === this._his.length && (this._hisPosition = this._his.length - 1, this._his.shift(), this._his.push(NaN)), this._his[this._hisPosition] = this.origin;
  }
}
class c {
  constructor(i) {
    s(this, "data", []);
    s(this, "_context", new n());
    s(this, "_isCalc", !1);
    this.formula = i, this.init();
  }
  init() {
    this._context = new n(), this.formula(this._context), this.data = [];
  }
  calcLasPoint(i) {
    const t = this.data[this.data.length - 1], e = (t == null ? void 0 : t.time) !== i.time;
    this._context.prepare({ ...i, isNew: e });
    const a = { time: i.time, value: this.formula(this._context) };
    e ? this.data.push(a) : this.data[this.data.length - 1] = a;
  }
  recalc(i) {
    this.init();
    for (const t of i)
      this.calcLasPoint(t);
    this._isCalc = !0;
  }
  update(i) {
    this.calcLasPoint(i);
  }
}
export {
  n as Context,
  c as ExecutionContext,
  _ as Var
};
//# sourceMappingURL=execution-indicator.es.js.map
