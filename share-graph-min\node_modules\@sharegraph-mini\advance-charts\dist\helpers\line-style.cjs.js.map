{"version": 3, "file": "line-style.cjs.js", "sources": ["../../src/helpers/line-style.ts"], "sourcesContent": ["import {LineStyle} from \"lightweight-charts\";\r\n\r\nexport function setLineStyle(ctx: CanvasRenderingContext2D, style: LineStyle): void {\r\n\tconst dashPatterns = {\r\n\t\t[LineStyle.Solid]: [],\r\n\t\t[LineStyle.Dotted]: [ctx.lineWidth, ctx.lineWidth],\r\n\t\t[LineStyle.Dashed]: [2 * ctx.lineWidth, 2 * ctx.lineWidth],\r\n\t\t[LineStyle.LargeDashed]: [6 * ctx.lineWidth, 6 * ctx.lineWidth],\r\n\t\t[LineStyle.SparseDotted]: [ctx.lineWidth, 4 * ctx.lineWidth],\r\n\t};\r\n\r\n\tconst dashPattern = dashPatterns[style];\r\n\tctx.setLineDash(dashPattern);\r\n}"], "names": ["setLineStyle", "ctx", "style", "dashPattern", "LineStyle"], "mappings": "sHAEgB,SAAAA,EAAaC,EAA+BC,EAAwB,CAS7E,MAAAC,EARe,CACpB,CAACC,EAAAA,UAAU,KAAK,EAAG,CAAC,EACpB,CAACA,EAAAA,UAAU,MAAM,EAAG,CAACH,EAAI,UAAWA,EAAI,SAAS,EACjD,CAACG,EAAU,UAAA,MAAM,EAAG,CAAC,EAAIH,EAAI,UAAW,EAAIA,EAAI,SAAS,EACzD,CAACG,EAAU,UAAA,WAAW,EAAG,CAAC,EAAIH,EAAI,UAAW,EAAIA,EAAI,SAAS,EAC9D,CAACG,EAAAA,UAAU,YAAY,EAAG,CAACH,EAAI,UAAW,EAAIA,EAAI,SAAS,CAC5D,EAEiCC,CAAK,EACtCD,EAAI,YAAYE,CAAW,CAC5B"}