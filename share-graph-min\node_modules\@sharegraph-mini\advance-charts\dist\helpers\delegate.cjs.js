"use strict";var h=Object.defineProperty;var o=(t,s,e)=>s in t?h(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e;var n=(t,s,e)=>o(t,typeof s!="symbol"?s+"":s,e);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});class a{constructor(){n(this,"_listeners",[]);n(this,"_params",[void 0,void 0,void 0])}fire(s,e,i){this._params=[s,e,i];const r=[...this._listeners];this._listeners=this._listeners.filter(l=>!l.singleshot),r.forEach(l=>l.callback(s,e,i))}lastParams(){return this._params}subscribe(s,e,i){this._listeners.push({callback:s,linkedObject:e,singleshot:!!i})}unsubscribe(s){const e=this._listeners.findIndex(i=>i.callback===s);e>-1&&this._listeners.splice(e,1)}unsubscribeAll(s){this._listeners=this._listeners.filter(e=>e.linkedObject!==s)}hasListener(){return!!this._listeners.length}destroy(){this._listeners=[]}}exports.Delegate=a;
//# sourceMappingURL=delegate.cjs.js.map
