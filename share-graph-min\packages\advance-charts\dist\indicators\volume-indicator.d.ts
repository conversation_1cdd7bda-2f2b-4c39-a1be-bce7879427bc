import { ISeriesApi, SeriesType, IChartApi, Nominal } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface VolumeIndicatorOptions extends ChartIndicatorOptions {
    upColor: string;
    downColor: string;
}
export declare const defaultOptions: VolumeIndicatorOptions;
export type VolumePoint = Nominal<number, 'VolumePoint'>;
export type VolumePositive = Nominal<0 | 1, 'Positive'>;
export type VolumeIndicatorData = [VolumePoint, VolumePositive];
export default class VolumeIndicator extends ChartIndicator<VolumeIndicatorOptions, VolumeIndicatorData> {
    protected chart: IChartApi;
    volumeSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<VolumeIndicatorOptions>, paneIndex?: number);
    applyPriceScaleMargins(): void;
    _applyOptions(options: Partial<VolumeIndicatorOptions>): void;
    applyIndicatorData(): void;
    formula(c: Context): VolumeIndicatorData | undefined;
    getDefaultOptions(): VolumeIndicatorOptions;
    remove(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
