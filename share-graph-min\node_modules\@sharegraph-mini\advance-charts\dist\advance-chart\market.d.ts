import { Nominal } from 'lightweight-charts';
import { Dayjs } from 'dayjs';

type IMarketDate = Nominal<Dayjs, 'marketDate'>;
export interface IMarket {
    isOpen(): boolean;
    isOpen(date: Date): boolean;
    toMarketDate(date: Dayjs): IMarketDate;
    marketZoneNow(): IMarketDate;
    getOpen(date: Date): IMarketDate;
    getClose(date: Date): IMarketDate;
    getNextOpen(date: Date): IMarketDate;
    getNextClose(date: Date): IMarketDate;
    getPrevOpen(date: Date): IMarketDate;
    getPrevClose(date: Date): IMarketDate;
}
interface IMarketOptions {
    name: string;
    timeZone: string;
    open: string;
    close: string;
}
type MarketDateInput = Dayjs | Date | string;
export declare class Market implements IMarket {
    protected options: IMarketOptions;
    private parsedOpenTime;
    private parsedCloseTime;
    constructor(options: IMarketOptions);
    toMarketDate(date: MarketDateInput): IMarketDate;
    private parseTime;
    private getTimeBlocksForDay;
    private isTimeInRange;
    marketZoneNow(): IMarketDate;
    private _isOpen;
    isOpen(dateInput?: MarketDateInput): boolean;
    getOpen(dateInput: MarketDateInput): IMarketDate;
    getClose(dateInput: MarketDateInput): IMarketDate;
    getNextOpen(dateInput: MarketDateInput): IMarketDate;
    getNextClose(dateInput: MarketDateInput): IMarketDate;
    getPrevOpen(dateInput: MarketDateInput): IMarketDate;
    getPrevClose(dateInput: MarketDateInput): IMarketDate;
}
export {};
