"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const p=require("../primitive-base.cjs.js"),u=require("../../helpers/assertions.cjs.js"),r=require("../../helpers/utils.cjs.js"),m={backgroundColor:"#2196f31a"};class x extends p.PrimitivePaneViewBase{dataVisible(){const a=this.getVisibleRange();if(!a)return[];const t=this.data;let e=r.binarySearchIndex(t,r.timeToUnix(a.from),i=>r.timeToUnix(i.time));e===-1&&(e=1);let o=r.binarySearchIndex(t,r.timeToUnix(a.to),i=>r.timeToUnix(i.time));return o===-1&&(o=t.length),t.slice(e-1,o+2)}drawBackground(a){const t=this.dataVisible().map(e=>({x:u.ensureNotNull(this.timeToCoordinate(e.time)),upperCoor:u.ensureNotNull(this.priceToCoordinate(e.upper)),lowerCoor:u.ensureNotNull(this.priceToCoordinate(e.lower))}));t.length<2||a.useBitmapCoordinateSpace(e=>{const o=e.context;o.scale(e.horizontalPixelRatio,e.verticalPixelRatio),o.beginPath();const i=new Path2D,c=0,d=t.length,s=t[c];i.moveTo(s.x,s.upperCoor);for(let n=c+1;n<d;n++){const l=t[n];i.lineTo(l.x,l.upperCoor)}for(let n=d-1;n>=c;n--){const l=t[n];i.lineTo(l.x,l.lowerCoor)}i.lineTo(s.x,s.lowerCoor),i.closePath(),o.fillStyle=this.options.backgroundColor,o.fill(i)})}defaultOptions(){return m}}exports.BandPrimitiveOptionsDefault=m;exports.BandPrimitivePaneView=x;
//# sourceMappingURL=band.cjs.js.map
