"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});function m(e,i){if(e.length===0)return i;if(i.length===0)return e;let r=e.length-1;for(;r>=0&&e[r].time>i[0].time;)r--;const h=e.slice(0,r+1);r>=0&&e[r].time===i[0].time&&h.pop(),h.push(...i);const g=i[i.length-1].time;let t=r+1;for(;t<e.length&&e[t].time<=g;)t++;for(;t<e.length;)h.push(e[t]),t++;return h}exports.mergeOhlcData=m;
//# sourceMappingURL=mergeData.cjs.js.map
