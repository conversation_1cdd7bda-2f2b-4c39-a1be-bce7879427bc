{"version": 3, "file": "stochastic-indicator.cjs.js", "sources": ["../../src/indicators/stochastic-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {SMA} from \"technicalindicators\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\r\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\nimport {max, min} from \"es-toolkit/compat\";\r\nimport {ensureDefined} from \"../helpers/assertions\";\r\n\r\nexport interface StochasticIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  signalColor: string,\r\n  period: number\r\n  priceLineColor: string\r\n  backgroundColor: string\r\n  signalPeriod: number\r\n}\r\n\r\nexport const defaultOptions: StochasticIndicatorOptions = {\r\n  color: \"#2962ff\",\r\n  signalColor: '#ff6d00',\r\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\r\n  backgroundColor: \"#2196f31a\",\r\n  period: 14,\r\n  overlay: false,\r\n  signalPeriod: 3\r\n}\r\n\r\nexport type KStochasticLine = Nominal<number, 'K_Stochastic'>\r\nexport type DStochasticLine = Nominal<number, 'D_Stochastic'>\r\nexport type StochasticData = [KStochasticLine, DStochasticLine]\r\n\r\nexport default class StochasticIndicator extends ChartIndicator<StochasticIndicatorOptions, StochasticData> {\r\n  kSeries: ISeriesApi<SeriesType>\r\n  dSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<StochasticIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options)\r\n    this.kSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'Stochastic',\r\n    }, paneIndex);\r\n\r\n    this.dSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.signalColor,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'Stochastic',\r\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 90, minValue: 10})\r\n    }, paneIndex);\r\n\r\n    this.kSeries.attachPrimitive(\r\n      new RegionPrimitive({\r\n        upPrice: 80,\r\n        lowPrice: 20,\r\n        lineColor: this.options.priceLineColor,\r\n        backgroundColor: this.options.backgroundColor,\r\n      })\r\n    );\r\n  }\r\n\r\n  getDefaultOptions(): StochasticIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n  \r\n  formula(c: Context): StochasticData | undefined {\r\n    const highSeries = c.new_var(c.symbol.high, this.options.period)\r\n    const lowSeries = c.new_var(c.symbol.low, this.options.period)\r\n    const dSmaSeries = c.new_var(NaN, this.options.signalPeriod)\r\n\r\n    if(!highSeries.calculable() || !lowSeries.calculable()) return;\r\n\r\n    const highest = ensureDefined(max(highSeries.getAll()))\r\n    const lowest = ensureDefined(min(lowSeries.getAll()));\r\n\r\n    let k = (c.symbol.close - lowest) / (highest - lowest) * 100;\r\n    k = isNaN(k) ? 0 : k\r\n\r\n    dSmaSeries.set(k);\r\n\r\n    if(!dSmaSeries.calculable()) return;\r\n\r\n    const [d] = new SMA({\r\n      period: this.options.signalPeriod, \r\n      values: dSmaSeries.getAll()\r\n    }).result;\r\n\r\n    return [k as KStochasticLine, d as DStochasticLine]\r\n  }\r\n\r\n  applyIndicatorData() {\r\n    const kData: SingleValueData[] = []\r\n    const dData: SingleValueData[] = []\r\n\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      const time = bar.time as Time;\r\n      if(!value) continue;\r\n\r\n      kData.push({time, value: value[0]})\r\n      dData.push({time, value: value[1]})\r\n    }\r\n    this.kSeries.setData(kData)\r\n    this.dSeries.setData(dData)\r\n  }\r\n\r\n  remove() {\r\n    super.remove()\r\n    this.chart.removeSeries(this.kSeries);\r\n    this.chart.removeSeries(this.dSeries);\r\n  }\r\n\r\n  _applyOptions() {\r\n    this.kSeries.applyOptions({color: this.options.color})\r\n    this.dSeries.applyOptions({color: this.options.signalColor})\r\n\r\n    this.calcIndicatorData()\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n\r\n  setPaneIndex(paneIndex: number) {\r\n    this.kSeries.moveToPane(paneIndex)\r\n    this.dSeries.moveToPane(paneIndex)\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.kSeries.getPane().paneIndex()\r\n  }\r\n}"], "names": ["defaultOptions", "StochasticIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "highSeries", "lowSeries", "dSmaSeries", "highest", "ensureDefined", "max", "lowest", "min", "k", "d", "SMA", "kData", "dData", "bar", "value", "time"], "mappings": "uiBAkBaA,EAA6C,CACxD,MAAO,UACP,YAAa,UACb,eAAgB,4BAChB,gBAAiB,YACjB,OAAQ,GACR,QAAS,GACT,aAAc,CAChB,EAMA,MAAqBC,UAA4BC,EAAAA,cAA2D,CAI1G,YAAYC,EAAkBC,EAA+CC,EAAoB,CAC/F,MAAMF,EAAOC,CAAO,EAJtBE,EAAA,gBACAA,EAAA,gBAIO,KAAA,QAAUH,EAAM,UAAUI,EAAAA,WAAY,CACzC,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,cACbF,CAAS,EAEP,KAAA,QAAUF,EAAM,UAAUI,EAAAA,WAAY,CACzC,MAAO,KAAK,QAAQ,YACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,aACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,GAAI,SAAU,EAAG,CAAA,GAC/EH,CAAS,EAEZ,KAAK,QAAQ,gBACX,IAAII,kBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAAgD,CACvC,OAAAT,CAAA,CAGT,QAAQU,EAAwC,CACxC,MAAAC,EAAaD,EAAE,QAAQA,EAAE,OAAO,KAAM,KAAK,QAAQ,MAAM,EACzDE,EAAYF,EAAE,QAAQA,EAAE,OAAO,IAAK,KAAK,QAAQ,MAAM,EACvDG,EAAaH,EAAE,QAAQ,IAAK,KAAK,QAAQ,YAAY,EAE3D,GAAG,CAACC,EAAW,WAAA,GAAgB,CAACC,EAAU,aAAc,OAExD,MAAME,EAAUC,EAAAA,cAAcC,EAAAA,IAAIL,EAAW,OAAQ,CAAA,CAAC,EAChDM,EAASF,EAAAA,cAAcG,EAAAA,IAAIN,EAAU,OAAQ,CAAA,CAAC,EAEpD,IAAIO,GAAKT,EAAE,OAAO,MAAQO,IAAWH,EAAUG,GAAU,IAKtD,GAJCE,EAAA,MAAMA,CAAC,EAAI,EAAIA,EAEnBN,EAAW,IAAIM,CAAC,EAEb,CAACN,EAAW,aAAc,OAE7B,KAAM,CAACO,CAAC,EAAI,IAAIC,MAAI,CAClB,OAAQ,KAAK,QAAQ,aACrB,OAAQR,EAAW,OAAO,CAC3B,CAAA,EAAE,OAEI,MAAA,CAACM,EAAsBC,CAAoB,CAAA,CAGpD,oBAAqB,CACnB,MAAME,EAA2B,CAAC,EAC5BC,EAA2B,CAAC,EAExB,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MACZE,EAAOF,EAAI,KACbC,IAEJH,EAAM,KAAK,CAAC,KAAAI,EAAM,MAAOD,EAAM,CAAC,EAAE,EAClCF,EAAM,KAAK,CAAC,KAAAG,EAAM,MAAOD,EAAM,CAAC,EAAE,EAAA,CAE/B,KAAA,QAAQ,QAAQH,CAAK,EACrB,KAAA,QAAQ,QAAQC,CAAK,CAAA,CAG5B,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,OAAO,EAC/B,KAAA,MAAM,aAAa,KAAK,OAAO,CAAA,CAGtC,eAAgB,CACd,KAAK,QAAQ,aAAa,CAAC,MAAO,KAAK,QAAQ,MAAM,EACrD,KAAK,QAAQ,aAAa,CAAC,MAAO,KAAK,QAAQ,YAAY,EAE3D,KAAK,kBAAkB,EACvB,KAAK,mBAAmB,CAAA,CAI1B,aAAalB,EAAmB,CACzB,KAAA,QAAQ,WAAWA,CAAS,EAC5B,KAAA,QAAQ,WAAWA,CAAS,CAAA,CAGnC,cAAuB,CACrB,OAAO,KAAK,QAAQ,QAAQ,EAAE,UAAU,CAAA,CAE5C"}