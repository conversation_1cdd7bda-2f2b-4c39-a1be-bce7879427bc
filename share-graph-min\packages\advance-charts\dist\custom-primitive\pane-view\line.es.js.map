{"version": 3, "file": "line.es.js", "sources": ["../../../src/custom-primitive/pane-view/line.ts"], "sourcesContent": ["import {BitmapCoordinatesRenderingScope} from 'fancy-canvas';\r\nimport {ensureNotNull} from '../../helpers/assertions';\r\nimport {PrimitivePaneViewBase} from '../primitive-base';\r\nimport {Coordinate, LineStyle, Time} from 'lightweight-charts';\r\nimport {setLineStyle} from '../../helpers/line-style';\r\n\r\ninterface LinePrimitiveOptions {\r\n  lineColor: string,\r\n  lineWidth: number,\r\n  lineDash: LineStyle\r\n}\r\n\r\nexport const LinePrimitiveOptionsDefault: LinePrimitiveOptions = {\r\n  lineColor: '#eee',\r\n  lineWidth: 1,\r\n  lineDash: LineStyle.Solid\r\n}\r\n\r\nexport type LineData = {\r\n  x: Coordinate,\r\n  price: number,\r\n} | {\r\n  time: Time,\r\n  price: number,\r\n}\r\n\r\nexport class LinePrimitivePaneView extends PrimitivePaneViewBase<LinePrimitiveOptions, LineData> {\r\n  getXCoordinate(data: LineData) {\r\n    return 'time' in data ? this.timeToCoordinate(data.time) : data.x;\r\n  }\r\n  _drawImpl(renderingScope: BitmapCoordinatesRenderingScope): void {\r\n    if(this.data.length === 0) return;\r\n    const ctx = renderingScope.context;\r\n\t\tctx.scale(renderingScope.horizontalPixelRatio, renderingScope.verticalPixelRatio);\r\n    ctx.lineWidth = this.options.lineWidth\r\n    ctx.strokeStyle = this.options.lineColor\r\n    const lines = new Path2D();\r\n    ctx.beginPath();\r\n    const points = this.data.map((item) => ({\r\n      x: this.getXCoordinate(item),\r\n      y: this.priceToCoordinate(item.price),\r\n    }));\r\n    const [firstPoint, ...restPoints] = points;\r\n    lines.moveTo(ensureNotNull(firstPoint.x), ensureNotNull(firstPoint.y));\r\n    for(const point of restPoints) {\r\n      if(!point.x || !point.y) continue;\r\n      lines.lineTo(point.x, point.y);\r\n    }\r\n\r\n    setLineStyle(ctx, this.options.lineDash)\r\n    ctx.stroke(lines)\r\n  }\r\n\r\n  defaultOptions() {\r\n    return LinePrimitiveOptionsDefault\r\n  }\r\n}"], "names": ["LinePrimitiveOptionsDefault", "LineStyle", "LinePrimitivePaneView", "PrimitivePaneViewBase", "data", "renderingScope", "ctx", "lines", "points", "item", "firstPoint", "restPoints", "ensureNotNull", "point", "setLineStyle"], "mappings": ";;;;AAYO,MAAMA,IAAoD;AAAA,EAC/D,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAUC,EAAU;AACtB;AAUO,MAAMC,UAA8BC,EAAsD;AAAA,EAC/F,eAAeC,GAAgB;AAC7B,WAAO,UAAUA,IAAO,KAAK,iBAAiBA,EAAK,IAAI,IAAIA,EAAK;AAAA,EAAA;AAAA,EAElE,UAAUC,GAAuD;AAC5D,QAAA,KAAK,KAAK,WAAW,EAAG;AAC3B,UAAMC,IAAMD,EAAe;AAC7B,IAAAC,EAAI,MAAMD,EAAe,sBAAsBA,EAAe,kBAAkB,GAC1EC,EAAA,YAAY,KAAK,QAAQ,WACzBA,EAAA,cAAc,KAAK,QAAQ;AACzB,UAAAC,IAAQ,IAAI,OAAO;AACzB,IAAAD,EAAI,UAAU;AACd,UAAME,IAAS,KAAK,KAAK,IAAI,CAACC,OAAU;AAAA,MACtC,GAAG,KAAK,eAAeA,CAAI;AAAA,MAC3B,GAAG,KAAK,kBAAkBA,EAAK,KAAK;AAAA,IAAA,EACpC,GACI,CAACC,GAAY,GAAGC,CAAU,IAAIH;AAC9B,IAAAD,EAAA,OAAOK,EAAcF,EAAW,CAAC,GAAGE,EAAcF,EAAW,CAAC,CAAC;AACrE,eAAUG,KAASF;AACjB,MAAG,CAACE,EAAM,KAAK,CAACA,EAAM,KACtBN,EAAM,OAAOM,EAAM,GAAGA,EAAM,CAAC;AAGlB,IAAAC,EAAAR,GAAK,KAAK,QAAQ,QAAQ,GACvCA,EAAI,OAAOC,CAAK;AAAA,EAAA;AAAA,EAGlB,iBAAiB;AACR,WAAAP;AAAA,EAAA;AAEX;"}