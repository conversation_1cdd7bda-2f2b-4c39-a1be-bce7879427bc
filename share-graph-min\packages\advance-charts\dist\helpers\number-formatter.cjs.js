"use strict";var i=Object.defineProperty;var o=(r,t,e)=>t in r?i(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var a=(r,t,e)=>o(r,typeof t!="symbol"?t+"":t,e);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});class m{constructor(t="en-gb"){a(this,"_volumeFormatter");a(this,"_decimalFormatter");a(this,"_percentFormatter");this.locale=t}get volumeFormatter(){return this._volumeFormatter||(this._volumeFormatter=new Intl.NumberFormat(this.locale,{notation:"compact",compactDisplay:"short",maximumFractionDigits:2})),this._volumeFormatter}get decimalFormatter(){return this._decimalFormatter||(this._decimalFormatter=new Intl.NumberFormat(this.locale)),this._decimalFormatter}get percentFormatter(){return this._percentFormatter||(this._percentFormatter=new Intl.NumberFormat(this.locale,{style:"percent",minimumFractionDigits:2,maximumFractionDigits:2})),this._percentFormatter}volume(t){return t==null?"":Number.isNaN(t)?"NaN":this.volumeFormatter.format(t)}decimal(t){return t==null?"":Number.isNaN(t)?"NaN":this.decimalFormatter.format(t)}percent(t){return t==null?"":Number.isNaN(t)?"NaN":this.percentFormatter.format(t)}}const c={_cache:new Map,formatter(r){this._cache.has(r)&&this._cache.get(r);const t=new m(r);return this._cache.set(r,t),t}};exports.NumberFormatter=m;exports.NumberFormatterFactory=c;
//# sourceMappingURL=number-formatter.cjs.js.map
