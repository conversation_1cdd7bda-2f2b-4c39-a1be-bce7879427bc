{"version": 3, "file": "bb-indicator.cjs.js", "sources": ["../../src/indicators/bb-indicator.ts"], "sourcesContent": ["import {\r\n  ISeriesApi,\r\n  Nominal,\r\n  SeriesType,\r\n  Time,\r\n  WhitespaceData,\r\n} from \"lightweight-charts\";\r\nimport { ChartIndicator, ChartIndicatorOptions } from \"./abstract-indicator\";\r\nimport { BollingerBands } from \"technicalindicators\";\r\nimport {SeriesPrimitiveBase} from \"../custom-primitive/primitive-base\";\r\nimport {BandPrimitivePaneView} from \"../custom-primitive/pane-view/band\";\r\nimport {LinePrimitivePaneView} from \"../custom-primitive/pane-view/line\";\r\nimport {Context, IIndicatorBar} from \"../helpers/execution-indicator\";\r\nimport type {BollingerBandsOutput} from \"technicalindicators/declarations/volatility/BollingerBands\";\r\n\r\nexport type UpperBBData = Nominal<number, 'Upper'>\r\nexport type MiddleBBData = Nominal<number, 'Middle'>\r\nexport type LowerBBData = Nominal<number, 'Lower'>\r\nexport type BBData = [UpperBBData, MiddleBBData, LowerBBData]\r\n\r\nexport interface BBIndicatorOptions extends ChartIndicatorOptions {\r\n  backgroundColor: string;\r\n  upperLineColor: string;\r\n  middleLineColor: string;\r\n  lowerLineColor: string;\r\n  period: number;\r\n  stdDev: number;\r\n};\r\n\r\nexport const defaultOptions: BBIndicatorOptions = {\r\n  backgroundColor: \"#2196f312\",\r\n  upperLineColor: \"#2196f3\",\r\n  middleLineColor: \"#ff6d00\",\r\n  lowerLineColor: \"#2196f3\",\r\n  period: 20,\r\n  stdDev: 2,\r\n  overlay: true\r\n};\r\n\r\n\r\nexport type BBIndicatorData = WhitespaceData & {\r\n  middle?: number;\r\n  upper?: number;\r\n  lower?: number;\r\n  pb?: number;\r\n}\r\n\r\nexport class BBPrimitive extends SeriesPrimitiveBase<BBIndicatorData> {\r\n  bandPaneView: BandPrimitivePaneView;\r\n  upperPaneView: LinePrimitivePaneView;\r\n  middlePaneView: LinePrimitivePaneView;\r\n  lowerPaneView: LinePrimitivePaneView;\r\n\r\n  constructor(protected source: BBIndicator) {\r\n    super();\r\n    this.bandPaneView = new BandPrimitivePaneView({\r\n      backgroundColor: this.source.options.backgroundColor,\r\n    });\r\n    this.upperPaneView = new LinePrimitivePaneView({\r\n      lineWidth: 1,\r\n      lineColor: this.source.options.upperLineColor,\r\n    });\r\n    this.middlePaneView = new LinePrimitivePaneView({\r\n      lineWidth: 1,\r\n      lineColor: this.source.options.middleLineColor,\r\n    });\r\n    this.lowerPaneView = new LinePrimitivePaneView({\r\n      lineWidth: 1,\r\n      lineColor: this.source.options.lowerLineColor,\r\n    });\r\n    this._paneViews = [\r\n      this.bandPaneView,\r\n      this.upperPaneView,\r\n      this.middlePaneView,\r\n      this.lowerPaneView\r\n    ];\r\n  }\r\n\r\n  update(data: IIndicatorBar<BBData>[]) {\r\n    const definedData: Array<{time: Time, value: BBData}> = [];\r\n\r\n    for(const bar of data) {\r\n      const value = bar.value;\r\n      if(!value) continue;\r\n      definedData.push({time: bar.time as Time, value})\r\n    }\r\n\r\n    this.bandPaneView.update(\r\n      definedData.map((item) => ({\r\n        time: item.time,\r\n        upper: item.value[0],\r\n        lower: item.value[2],\r\n      }))\r\n    );\r\n    this.upperPaneView.update(\r\n      definedData.map((item) => ({\r\n        time: item.time,\r\n        price: item.value[0],\r\n      }))\r\n    );\r\n    this.middlePaneView.update(\r\n      definedData.map((item) => ({\r\n        time: item.time,\r\n        price: item.value[1],\r\n      }))\r\n    );\r\n    this.lowerPaneView.update(\r\n      definedData.map((item) => ({\r\n        time: item.time,\r\n        price: item.value[2],\r\n      }))\r\n    );\r\n  }\r\n}\r\n\r\nexport default class BBIndicator extends ChartIndicator<BBIndicatorOptions, BBData> {\r\n  bbPrimitive = new BBPrimitive(this)\r\n  _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {\r\n    series.attachPrimitive(this.bbPrimitive)\r\n  }\r\n\r\n  _applyOptions(options: Partial<BBIndicatorOptions>): void {\r\n    if(options.period || options.stdDev) {\r\n      this.applyIndicatorData()\r\n    }\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n      this.bbPrimitive.update(this._executionContext.data)\r\n  }\r\n\r\n  formula(c: Context): BBData | undefined {\r\n    const closeSeries = c.new_var(c.symbol.close, this.options.period);\r\n    if(!closeSeries.calculable()) return;\r\n\r\n    const result = new BollingerBands({\r\n      values: closeSeries.getAll(),\r\n      period: this.options.period,\r\n      stdDev: this.options.stdDev,\r\n    });\r\n\r\n    const item = result.getResult().at(0) as BollingerBandsOutput\r\n    \r\n    return [item.upper as UpperBBData, item.middle as MiddleBBData, item.lower as LowerBBData]\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove()\r\n    this.mainSeries?.detachPrimitive(this.bbPrimitive)\r\n  }\r\n\r\n  getDefaultOptions(): BBIndicatorOptions {\r\n    return defaultOptions;\r\n  }\r\n}\r\n"], "names": ["defaultOptions", "BBPrimitive", "SeriesPrimitiveBase", "source", "__publicField", "BandPrimitivePaneView", "LinePrimitivePaneView", "data", "definedData", "bar", "value", "item", "BBIndicator", "ChartIndicator", "series", "options", "c", "closeSeries", "BollingerBands", "_a"], "mappings": "qgBA6BaA,EAAqC,CAChD,gBAAiB,YACjB,eAAgB,UAChB,gBAAiB,UACjB,eAAgB,UAChB,OAAQ,GACR,OAAQ,EACR,QAAS,EACX,EAUO,MAAMC,UAAoBC,EAAAA,mBAAqC,CAMpE,YAAsBC,EAAqB,CACnC,MAAA,EANRC,EAAA,qBACAA,EAAA,sBACAA,EAAA,uBACAA,EAAA,sBAEsB,KAAA,OAAAD,EAEf,KAAA,aAAe,IAAIE,wBAAsB,CAC5C,gBAAiB,KAAK,OAAO,QAAQ,eAAA,CACtC,EACI,KAAA,cAAgB,IAAIC,wBAAsB,CAC7C,UAAW,EACX,UAAW,KAAK,OAAO,QAAQ,cAAA,CAChC,EACI,KAAA,eAAiB,IAAIA,wBAAsB,CAC9C,UAAW,EACX,UAAW,KAAK,OAAO,QAAQ,eAAA,CAChC,EACI,KAAA,cAAgB,IAAIA,wBAAsB,CAC7C,UAAW,EACX,UAAW,KAAK,OAAO,QAAQ,cAAA,CAChC,EACD,KAAK,WAAa,CAChB,KAAK,aACL,KAAK,cACL,KAAK,eACL,KAAK,aACP,CAAA,CAGF,OAAOC,EAA+B,CACpC,MAAMC,EAAkD,CAAC,EAEzD,UAAUC,KAAOF,EAAM,CACrB,MAAMG,EAAQD,EAAI,MACdC,GACJF,EAAY,KAAK,CAAC,KAAMC,EAAI,KAAc,MAAAC,EAAM,CAAA,CAGlD,KAAK,aAAa,OAChBF,EAAY,IAAKG,IAAU,CACzB,KAAMA,EAAK,KACX,MAAOA,EAAK,MAAM,CAAC,EACnB,MAAOA,EAAK,MAAM,CAAC,CAAA,EACnB,CACJ,EACA,KAAK,cAAc,OACjBH,EAAY,IAAKG,IAAU,CACzB,KAAMA,EAAK,KACX,MAAOA,EAAK,MAAM,CAAC,CAAA,EACnB,CACJ,EACA,KAAK,eAAe,OAClBH,EAAY,IAAKG,IAAU,CACzB,KAAMA,EAAK,KACX,MAAOA,EAAK,MAAM,CAAC,CAAA,EACnB,CACJ,EACA,KAAK,cAAc,OACjBH,EAAY,IAAKG,IAAU,CACzB,KAAMA,EAAK,KACX,MAAOA,EAAK,MAAM,CAAC,CAAA,EACnB,CACJ,CAAA,CAEJ,CAEA,MAAqBC,UAAoBC,EAAAA,cAA2C,CAApF,kCACET,EAAA,mBAAc,IAAIH,EAAY,IAAI,GAClC,mBAAmBa,EAAsC,CAChDA,EAAA,gBAAgB,KAAK,WAAW,CAAA,CAGzC,cAAcC,EAA4C,EACrDA,EAAQ,QAAUA,EAAQ,SAC3B,KAAK,mBAAmB,CAC1B,CAGF,oBAA2B,CACvB,KAAK,YAAY,OAAO,KAAK,kBAAkB,IAAI,CAAA,CAGvD,QAAQC,EAAgC,CAChC,MAAAC,EAAcD,EAAE,QAAQA,EAAE,OAAO,MAAO,KAAK,QAAQ,MAAM,EAC9D,GAAA,CAACC,EAAY,aAAc,OAQ9B,MAAMN,EANS,IAAIO,iBAAe,CAChC,OAAQD,EAAY,OAAO,EAC3B,OAAQ,KAAK,QAAQ,OACrB,OAAQ,KAAK,QAAQ,MAAA,CACtB,EAEmB,UAAU,EAAE,GAAG,CAAC,EAEpC,MAAO,CAACN,EAAK,MAAsBA,EAAK,OAAwBA,EAAK,KAAoB,CAAA,CAG3F,QAAe,OACb,MAAM,OAAO,GACRQ,EAAA,KAAA,aAAA,MAAAA,EAAY,gBAAgB,KAAK,YAAW,CAGnD,mBAAwC,CAC/B,OAAAnB,CAAA,CAEX"}