{"version": 3, "file": "macd-indicator.cjs.js", "sources": ["../../src/indicators/macd-indicator.ts"], "sourcesContent": ["import {HistogramData, HistogramSeries, IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions, downColor, upColor} from \"./abstract-indicator\";\r\nimport {EMA, SMA} from \"technicalindicators\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface MACDIndicatorOptions extends ChartIndicatorOptions {\r\n  fastPeriod: number,\r\n  slowPeriod: number,\r\n  signalPeriod: number,\r\n  SimpleMAOscillator: boolean,\r\n  SimpleMASignal: boolean,\r\n  upColor: string,\r\n  downColor: string,\r\n  macdLineColor: string,\r\n  signalLineColor: string\r\n};\r\n\r\nexport const defaultOptions: MACDIndicatorOptions = {\r\n  fastPeriod: 12,\r\n  slowPeriod: 26,\r\n  signalPeriod: 9,\r\n  SimpleMAOscillator: false,\r\n  SimpleMASignal: false,\r\n  upColor: upColor,\r\n  downColor: downColor,\r\n  macdLineColor: '#2b97f1',\r\n  signalLineColor: '#fd6c1c',\r\n  overlay: false\r\n}\r\n\r\nexport type MacdMACDData = Nominal<number, 'Macd'>\r\nexport type SignalMACDData = Nominal<number, 'Signal'>\r\nexport type HistogramMACDData = Nominal<number, 'Histogram'>\r\nexport type MACDData = [MacdMACDData, SignalMACDData, HistogramMACDData]\r\n\r\nexport default class MACDIndicator extends ChartIndicator<MACDIndicatorOptions, MACDData> {\r\n  histogramSeries: ISeriesApi<'Histogram'>\r\n  macdSeries: ISeriesApi<SeriesType>\r\n  signalSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<MACDIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options);\r\n\r\n    this.histogramSeries = chart.addSeries(HistogramSeries, {\r\n      priceLineVisible: false,\r\n      priceScaleId: 'macd'\r\n    }, paneIndex)\r\n\r\n    this.macdSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.macdLineColor,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'macd'\r\n    }, paneIndex)\r\n\r\n    this.signalSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.signalLineColor,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'macd'\r\n    }, paneIndex)\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n    const histogramData: HistogramData[] = []\r\n    const macdData: SingleValueData[] = []\r\n    const signalData: SingleValueData[] = []\r\n\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      const time = bar.time as Time;\r\n      \r\n      if(!value) continue;\r\n      const [macd, signal, histogram] = value;\r\n      if(!isNaN(macd)) macdData.push({time, value: macd})\r\n      if(!isNaN(signal)) signalData.push({time, value: signal})\r\n      if(!isNaN(histogram)) histogramData.push({time, value: histogram, color: (histogram ?? 0) >= 0 ? this.options.upColor : this.options.downColor})\r\n    }\r\n\r\n    this.histogramSeries.setData(histogramData)\r\n    this.macdSeries.setData(macdData)\r\n    this.signalSeries.setData(signalData)\r\n  }\r\n\r\n  formula(c: Context): MACDData | undefined {\r\n      const fastPeriodSeries = c.new_var(c.symbol.close, this.options.fastPeriod)\r\n      const slowPeriodSeries = c.new_var(c.symbol.close, this.options.slowPeriod)\r\n      const signalPeriodSeries = c.new_var(NaN, this.options.signalPeriod)\r\n\r\n      if(!fastPeriodSeries.calculable() || !slowPeriodSeries.calculable()) return;\r\n\r\n      const oscillatorMAtype = this.options.SimpleMAOscillator ? SMA : EMA;\r\n      const signalMAtype = this.options.SimpleMASignal ? SMA : EMA;\r\n      const [fastMA] = new oscillatorMAtype({ period: this.options.fastPeriod, values: fastPeriodSeries.getAll()}).result;\r\n      const [slowMA] = new oscillatorMAtype({ period: this.options.slowPeriod, values: slowPeriodSeries.getAll()}).result;\r\n      const macd = fastMA - slowMA\r\n      signalPeriodSeries.set(macd);\r\n      if(!signalPeriodSeries.calculable()) return;\r\n      const [signalMA] = new signalMAtype({ period: this.options.signalPeriod, values: signalPeriodSeries.getAll()}).result;\r\n\r\n      const histogram = macd - signalMA;\r\n\r\n      return [macd as MacdMACDData, signalMA as SignalMACDData, histogram as HistogramMACDData]\r\n  }\r\n\r\n  _applyOptions(options: Partial<MACDIndicatorOptions>): void {\r\n    if(\r\n      options.SimpleMAOscillator || \r\n      options.SimpleMASignal ||\r\n      options.fastPeriod ||\r\n      options.signalPeriod ||\r\n      options.slowPeriod\r\n    ) {\r\n      this.calcIndicatorData()\r\n    } \r\n\r\n    if(options.macdLineColor) this.macdSeries.applyOptions({color: options.macdLineColor})\r\n    if(options.signalLineColor) this.signalSeries.applyOptions({color: options.signalLineColor})\r\n    \r\n    if(options.downColor || options.upColor) this.applyIndicatorData()\r\n  }\r\n\r\n  getDefaultOptions() {\r\n    return defaultOptions\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove()\r\n    this.chart.removeSeries(this.histogramSeries)\r\n    this.chart.removeSeries(this.macdSeries)\r\n    this.chart.removeSeries(this.signalSeries)\r\n  }\r\n\r\n  setPaneIndex(paneIndex: number): void {\r\n    this.histogramSeries.moveToPane(paneIndex);\r\n    this.macdSeries.moveToPane(paneIndex);\r\n    this.signalSeries.moveToPane(paneIndex);\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.histogramSeries.getPane().paneIndex()\r\n  }\r\n}\r\n"], "names": ["defaultOptions", "upColor", "downColor", "MACDIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "HistogramSeries", "LineSeries", "histogramData", "macdData", "signalData", "bar", "value", "time", "macd", "signal", "histogram", "c", "fastPeriodSeries", "slowPeriodSeries", "signalPeriodSeries", "oscillatorMAtype", "SMA", "EMA", "signalMAtype", "fastMA", "slowMA", "signalMA"], "mappings": "gYAiBaA,EAAuC,CAClD,WAAY,GACZ,WAAY,GACZ,aAAc,EACd,mBAAoB,GACpB,eAAgB,GAAA,QAChBC,EAAA,QAAA,UACAC,EAAA,UACA,cAAe,UACf,gBAAiB,UACjB,QAAS,EACX,EAOA,MAAqBC,UAAsBC,EAAAA,cAA+C,CAKxF,YAAYC,EAAkBC,EAAyCC,EAAoB,CACzF,MAAMF,EAAOC,CAAO,EALtBE,EAAA,wBACAA,EAAA,mBACAA,EAAA,qBAKO,KAAA,gBAAkBH,EAAM,UAAUI,EAAAA,gBAAiB,CACtD,iBAAkB,GAClB,aAAc,QACbF,CAAS,EAEP,KAAA,WAAaF,EAAM,UAAUK,EAAAA,WAAY,CAC5C,MAAO,KAAK,QAAQ,cACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,QACbH,CAAS,EAEP,KAAA,aAAeF,EAAM,UAAUK,EAAAA,WAAY,CAC9C,MAAO,KAAK,QAAQ,gBACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,QACbH,CAAS,CAAA,CAGd,oBAA2B,CACzB,MAAMI,EAAiC,CAAC,EAClCC,EAA8B,CAAC,EAC/BC,EAAgC,CAAC,EAE7B,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MACZE,EAAOF,EAAI,KAEjB,GAAG,CAACC,EAAO,SACX,KAAM,CAACE,EAAMC,EAAQC,CAAS,EAAIJ,EAC9B,MAAME,CAAI,GAAGL,EAAS,KAAK,CAAC,KAAAI,EAAM,MAAOC,EAAK,EAC9C,MAAMC,CAAM,GAAGL,EAAW,KAAK,CAAC,KAAAG,EAAM,MAAOE,EAAO,EACpD,MAAMC,CAAS,KAAiB,KAAK,CAAC,KAAAH,EAAM,MAAOG,EAAW,OAAQA,GAAa,IAAM,EAAI,KAAK,QAAQ,QAAU,KAAK,QAAQ,UAAU,CAAA,CAG5I,KAAA,gBAAgB,QAAQR,CAAa,EACrC,KAAA,WAAW,QAAQC,CAAQ,EAC3B,KAAA,aAAa,QAAQC,CAAU,CAAA,CAGtC,QAAQO,EAAkC,CAChC,MAAAC,EAAmBD,EAAE,QAAQA,EAAE,OAAO,MAAO,KAAK,QAAQ,UAAU,EACpEE,EAAmBF,EAAE,QAAQA,EAAE,OAAO,MAAO,KAAK,QAAQ,UAAU,EACpEG,EAAqBH,EAAE,QAAQ,IAAK,KAAK,QAAQ,YAAY,EAEnE,GAAG,CAACC,EAAiB,WAAA,GAAgB,CAACC,EAAiB,aAAc,OAErE,MAAME,EAAmB,KAAK,QAAQ,mBAAqBC,EAAM,IAAAC,EAAA,IAC3DC,EAAe,KAAK,QAAQ,eAAiBF,EAAM,IAAAC,EAAA,IACnD,CAACE,CAAM,EAAI,IAAIJ,EAAiB,CAAE,OAAQ,KAAK,QAAQ,WAAY,OAAQH,EAAiB,OAAO,CAAA,CAAE,EAAE,OACvG,CAACQ,CAAM,EAAI,IAAIL,EAAiB,CAAE,OAAQ,KAAK,QAAQ,WAAY,OAAQF,EAAiB,OAAO,CAAA,CAAE,EAAE,OACvGL,EAAOW,EAASC,EAEnB,GADHN,EAAmB,IAAIN,CAAI,EACxB,CAACM,EAAmB,aAAc,OACrC,KAAM,CAACO,CAAQ,EAAI,IAAIH,EAAa,CAAE,OAAQ,KAAK,QAAQ,aAAc,OAAQJ,EAAmB,OAAO,CAAA,CAAE,EAAE,OAEzGJ,EAAYF,EAAOa,EAElB,MAAA,CAACb,EAAsBa,EAA4BX,CAA8B,CAAA,CAG5F,cAAcb,EAA8C,EAExDA,EAAQ,oBACRA,EAAQ,gBACRA,EAAQ,YACRA,EAAQ,cACRA,EAAQ,aAER,KAAK,kBAAkB,EAGtBA,EAAQ,eAAoB,KAAA,WAAW,aAAa,CAAC,MAAOA,EAAQ,cAAc,EAClFA,EAAQ,iBAAsB,KAAA,aAAa,aAAa,CAAC,MAAOA,EAAQ,gBAAgB,GAExFA,EAAQ,WAAaA,EAAQ,eAAc,mBAAmB,CAAA,CAGnE,mBAAoB,CACX,OAAAN,CAAA,CAGT,QAAe,CACb,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,eAAe,EACvC,KAAA,MAAM,aAAa,KAAK,UAAU,EAClC,KAAA,MAAM,aAAa,KAAK,YAAY,CAAA,CAG3C,aAAaO,EAAyB,CAC/B,KAAA,gBAAgB,WAAWA,CAAS,EACpC,KAAA,WAAW,WAAWA,CAAS,EAC/B,KAAA,aAAa,WAAWA,CAAS,CAAA,CAGxC,cAAuB,CACrB,OAAO,KAAK,gBAAgB,QAAQ,EAAE,UAAU,CAAA,CAEpD"}