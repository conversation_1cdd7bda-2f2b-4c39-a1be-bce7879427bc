"use strict";var S=Object.defineProperty;var b=(s,i,e)=>i in s?S(s,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[i]=e;var g=(s,i,e)=>b(s,typeof i!="symbol"?i+"":i,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const f=require("lightweight-charts"),v=require("./abstract-indicator.cjs.js"),C=require("../custom-primitive/primitive/region.cjs.js"),I=require("../helpers/utils.cjs.js"),w={color:"rgba(108, 80, 175, 1)",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#7e57c21a",period:14,overlay:!1};class P extends v.ChartIndicator{constructor(e,o,t){super(e,o);g(this,"williamsSeries");this.williamsSeries=e.addSeries(f.LineSeries,{color:this.options.color,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"williams",autoscaleInfoProvider:I.autoScaleInfoProviderCreator({maxValue:0,minValue:-100})},t),this.williamsSeries.attachPrimitive(new C.RegionPrimitive({upPrice:-20,lowPrice:-80,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return w}formula(e){const o=e.new_var(e.symbol.high,this.options.period),t=e.new_var(e.symbol.low,this.options.period),n=e.new_var(e.symbol.close,this.options.period);if(!o.calculable()||!t.calculable()||!n.calculable())return;const c=o.getAll(),h=t.getAll(),l=n.getAll();if(c.length<this.options.period||h.length<this.options.period||l.length<this.options.period)return;const r=Math.max(...c),p=Math.min(...h),u=l[l.length-1],a=r-p;if(a===0)return[0];const d=(r-u)/a*-100,m=Math.max(-100,Math.min(0,d));return console.log("Williams %R Calculation:",{period:this.options.period,highestHigh:r,lowestLow:p,currentClose:u,range:a,williamsR:d,clampedWilliamsR:m}),[m]}applyIndicatorData(){const e=[];for(const o of this._executionContext.data){const t=o.value;t&&e.push({time:o.time,value:t[0]})}this.williamsSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.williamsSeries)}_applyOptions(){this.williamsSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.williamsSeries.moveToPane(e)}getPaneIndex(){return this.williamsSeries.getPane().paneIndex()}}exports.default=P;exports.defaultOptions=w;
//# sourceMappingURL=williams-indicator.cjs.js.map
