"use strict";var c=Object.defineProperty;var u=(s,i,e)=>i in s?c(s,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[i]=e;var a=(s,i,e)=>u(s,typeof i!="symbol"?i+"":i,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const p=require("lightweight-charts"),h=require("technicalindicators"),d=require("./abstract-indicator.cjs.js"),m=require("../custom-primitive/primitive/region.cjs.js"),g=require("../helpers/utils.cjs.js"),n={color:"rgba(108, 80, 175, 1)",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#7e57c21a",period:14,overlay:!1};class w extends d.ChartIndicator{constructor(e,o,t){super(e,o);a(this,"williamsSeries");this.williamsSeries=e.addSeries(p.LineSeries,{color:this.options.color,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"williams",autoscaleInfoProvider:g.autoScaleInfoProviderCreator({maxValue:0,minValue:-100})},t),this.williamsSeries.attachPrimitive(new m.RegionPrimitive({upPrice:-20,lowPrice:-80,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return n}formula(e){const o=e.new_var(e.symbol.high,this.options.period),t=e.new_var(e.symbol.low,this.options.period),l=e.new_var(e.symbol.close,this.options.period);if(!o.calculable()||!t.calculable()||!l.calculable())return;const r=new h.WilliamsR({period:this.options.period,high:o.getAll(),low:t.getAll(),close:l.getAll()}).getResult();if(r.length!==0)return[r[r.length-1]]}applyIndicatorData(){const e=[];for(const o of this._executionContext.data){const t=o.value;t&&e.push({time:o.time,value:t[0]})}this.williamsSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.williamsSeries)}_applyOptions(){this.williamsSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.williamsSeries.moveToPane(e)}getPaneIndex(){return this.williamsSeries.getPane().paneIndex()}}exports.default=w;exports.defaultOptions=n;
//# sourceMappingURL=williams-indicator.cjs.js.map
