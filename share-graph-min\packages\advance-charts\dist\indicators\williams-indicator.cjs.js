"use strict";var m=Object.defineProperty;var d=(s,t,e)=>t in s?m(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var u=(s,t,e)=>d(s,typeof t!="symbol"?t+"":t,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const g=require("lightweight-charts"),w=require("./abstract-indicator.cjs.js"),f=require("../custom-primitive/primitive/region.cjs.js"),S=require("../helpers/utils.cjs.js"),h={color:"rgba(108, 80, 175, 1)",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#7e57c21a",period:14,overlay:!1};class b extends w.ChartIndicator{constructor(e,i,o){super(e,i);u(this,"williamsSeries");this.williamsSeries=e.addSeries(g.LineSeries,{color:this.options.color,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"williams",autoscaleInfoProvider:S.autoScaleInfoProviderCreator({maxValue:0,minValue:-100})},o),this.williamsSeries.attachPrimitive(new f.RegionPrimitive({upPrice:-20,lowPrice:-80,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return h}formula(e){const i=this.options.period,o=e.new_var(e.symbol.high,i+1),l=e.new_var(e.symbol.low,i+1);if(!o.calculable()||!l.calculable())return;const a=o.getAll().slice(0,-1),n=l.getAll().slice(0,-1),p=e.symbol.close;if(a.length<i||n.length<i)return;const r=Math.max(...a.slice(-i)),c=Math.min(...n.slice(-i));return r===c?[-50]:[(r-p)/(r-c)*-100]}applyIndicatorData(){const e=[];for(const i of this._executionContext.data){const o=i.value;o&&e.push({time:i.time,value:o[0]})}this.williamsSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.williamsSeries)}_applyOptions(){this.williamsSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.williamsSeries.moveToPane(e)}getPaneIndex(){return this.williamsSeries.getPane().paneIndex()}}exports.default=b;exports.defaultOptions=h;
//# sourceMappingURL=williams-indicator.cjs.js.map
