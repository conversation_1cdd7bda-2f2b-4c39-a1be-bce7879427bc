"use strict";var n=Object.defineProperty;var l=(o,i,e)=>i in o?n(o,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[i]=e;var s=(o,i,e)=>l(o,typeof i!="symbol"?i+"":i,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const c=require("lightweight-charts"),u=require("technicalindicators"),p=require("./abstract-indicator.cjs.js"),d=require("../custom-primitive/primitive/region.cjs.js"),h=require("../helpers/utils.cjs.js"),a={color:"rgba(108, 80, 175, 1)",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#7e57c21a",period:14,overlay:!1};class S extends p.ChartIndicator{constructor(e,r,t){super(e,r);s(this,"rsiSeries");this.rsiSeries=e.addSeries(c.LineSeries,{color:this.options.color,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"williams",autoscaleInfoProvider:h.autoScaleInfoProviderCreator({maxValue:80,minValue:20})},t),this.rsiSeries.attachPrimitive(new d.RegionPrimitive({upPrice:70,lowPrice:30,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return a}formula(e){const r=e.new_var(e.symbol.close,this.options.period+1);if(!r.calculable())return;const[t]=new u.RSI({period:this.options.period,values:r.getAll()}).result;return[t]}applyIndicatorData(){const e=[];for(const r of this._executionContext.data){const t=r.value;t&&e.push({time:r.time,value:t[0]})}this.rsiSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.rsiSeries)}_applyOptions(){this.rsiSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.rsiSeries.moveToPane(e)}getPaneIndex(){return this.rsiSeries.getPane().paneIndex()}}exports.default=S;exports.defaultOptions=a;
//# sourceMappingURL=williams-indicator.cjs.js.map
