"use strict";var n=Object.defineProperty;var m=(i,e,r)=>e in i?n(i,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[e]=r;var a=(i,e,r)=>m(i,typeof e!="symbol"?e+"":e,r);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const c=require("../helpers/delegate.cjs.js"),s=require("./i-advance-chart.cjs.js"),o=require("lightweight-charts");require("../helpers/dayjs-setup.cjs.js");const u=require("dayjs");class h{constructor(e){a(this,"_timezoneChanged",new c.Delegate);a(this,"_timezone");a(this,"_browserTimezone",Intl.DateTimeFormat().resolvedOptions().timeZone);this._source=e,this._timezone=this._source.options.tzDisplay}formatDateTime(e){return e=this.convertDateToTimezoneDate(e),new Intl.DateTimeFormat(this._source.options.locale,{dateStyle:"medium",timeStyle:"medium",hourCycle:"h23"}).format(e)}formatDate(e){return e=this.convertDateToTimezoneDate(e),new Intl.DateTimeFormat(this._source.options.locale,{dateStyle:"medium"}).format(e)}convertDateToTimezoneDate(e){return this._timezone===this._browserTimezone?e:u.tz(e,this._timezone).tz(this._browserTimezone,!0).toDate()}format(e){switch(this._source.dataInterval.period){case s.Period.day:case s.Period.month:case s.Period.week:return this.formatDate(e);default:return this.formatDateTime(e)}}tickMarkFormatter(e,r){e=this.convertDateToTimezoneDate(e);const t={};switch(r){case o.TickMarkType.Year:t.year="numeric";break;case o.TickMarkType.Month:t.month="short";break;case o.TickMarkType.DayOfMonth:t.day="numeric";break;case o.TickMarkType.Time:t.hour12=!1,t.hour="2-digit",t.minute="2-digit";break;case o.TickMarkType.TimeWithSeconds:t.hour12=!1,t.hour="2-digit",t.minute="2-digit",t.second="2-digit";break}return e.toLocaleString(this._source.options.locale,t)}}exports.DisplayTimezone=h;
//# sourceMappingURL=DisplayTimezone.cjs.js.map
