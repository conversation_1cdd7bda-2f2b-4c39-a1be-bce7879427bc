{"version": 3, "file": "rsi-indicator.es.js", "sources": ["../../src/indicators/rsi-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {RSI} from \"technicalindicators\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\r\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface RSIIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  period: number\r\n  priceLineColor: string,\r\n  backgroundColor: string\r\n}\r\n\r\nexport const defaultOptions: RSIIndicatorOptions = {\r\n  color: \"rgba(108, 80, 175, 1)\",\r\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\r\n  backgroundColor: '#7e57c21a',\r\n  period: 14,\r\n  overlay: false\r\n}\r\n\r\nexport type RsiLine = Nominal<number, 'RSI'>\r\n\r\nexport type RsiData = [RsiLine]\r\n\r\nexport default class RSIIndicator extends ChartIndicator<RSIIndicatorOptions, RsiData> {\r\n  rsiSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<RSIIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options)\r\n    this.rsiSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'rsi',\r\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 80, minValue: 20})\r\n    }, paneIndex);\r\n    \r\n    this.rsiSeries.attachPrimitive(\r\n      new RegionPrimitive({\r\n        upPrice: 70,\r\n        lowPrice: 30,\r\n        lineColor: this.options.priceLineColor,\r\n        backgroundColor: this.options.backgroundColor\r\n      })\r\n    );\r\n  }\r\n\r\n  getDefaultOptions(): RSIIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n\r\n  formula(c: Context): RsiData | undefined {\r\n    const closeSeries = c.new_var(c.symbol.close, this.options.period + 1);\r\n\r\n    if(!closeSeries.calculable()) return;\r\n\r\n    const [rsi] = new RSI({\r\n      period: this.options.period,\r\n      values: closeSeries.getAll()\r\n    }).result;\r\n\r\n    return [rsi]\r\n  }\r\n\r\n\r\n  applyIndicatorData() {\r\n    const rsi: SingleValueData[] = [];\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      if(!value) continue;\r\n      rsi.push({time: bar.time as Time, value: value[0]})\r\n    }\r\n\r\n    this.rsiSeries.setData(rsi)\r\n  }\r\n\r\n  remove() {\r\n    super.remove()\r\n    this.chart.removeSeries(this.rsiSeries);\r\n  }\r\n\r\n  _applyOptions() {\r\n    this.rsiSeries.applyOptions({color: this.options.color})\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n\r\n  setPaneIndex(paneIndex: number) {\r\n    this.rsiSeries.moveToPane(paneIndex)\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.rsiSeries.getPane().paneIndex()\r\n  }\r\n}"], "names": ["defaultOptions", "RSIIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "closeSeries", "rsi", "RSI", "bar", "value"], "mappings": ";;;;;;;;AAcO,MAAMA,IAAsC;AAAA,EACjD,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,SAAS;AACX;AAMA,MAAqBC,UAAqBC,EAA6C;AAAA,EAGrF,YAAYC,GAAkBC,GAAwCC,GAAoB;AACxF,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAIO,SAAA,YAAYH,EAAM,UAAUI,GAAY;AAAA,MAC3C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,IAAI,UAAU,GAAG,CAAA;AAAA,OAC/EH,CAAS,GAEZ,KAAK,UAAU;AAAA,MACb,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAAyC;AAChC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAiC;AACjC,UAAAC,IAAcD,EAAE,QAAQA,EAAE,OAAO,OAAO,KAAK,QAAQ,SAAS,CAAC;AAElE,QAAA,CAACC,EAAY,aAAc;AAE9B,UAAM,CAACC,CAAG,IAAI,IAAIC,EAAI;AAAA,MACpB,QAAQ,KAAK,QAAQ;AAAA,MACrB,QAAQF,EAAY,OAAO;AAAA,IAC5B,CAAA,EAAE;AAEH,WAAO,CAACC,CAAG;AAAA,EAAA;AAAA,EAIb,qBAAqB;AACnB,UAAMA,IAAyB,CAAC;AACtB,eAAAE,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,MAAIC,KACAH,EAAA,KAAK,EAAC,MAAME,EAAI,MAAc,OAAOC,EAAM,CAAC,GAAE;AAAA,IAAA;AAG/C,SAAA,UAAU,QAAQH,CAAG;AAAA,EAAA;AAAA,EAG5B,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,SAAS;AAAA,EAAA;AAAA,EAGxC,gBAAgB;AACd,SAAK,UAAU,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GACvD,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAI1B,aAAaP,GAAmB;AACzB,SAAA,UAAU,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGrC,eAAuB;AACrB,WAAO,KAAK,UAAU,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE9C;"}