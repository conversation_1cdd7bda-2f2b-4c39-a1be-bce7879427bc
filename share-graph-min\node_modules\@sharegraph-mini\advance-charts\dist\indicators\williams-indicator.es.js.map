{"version": 3, "file": "williams-indicator.es.js", "sources": ["../../src/indicators/williams-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\r\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface WilliamsIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  period: number\r\n  priceLineColor: string,\r\n  backgroundColor: string\r\n}\r\n\r\nexport const defaultOptions: WilliamsIndicatorOptions = {\r\n  color: \"rgba(108, 80, 175, 1)\",\r\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\r\n  backgroundColor: '#7e57c21a',\r\n  period: 14,\r\n  overlay: false\r\n}\r\n\r\nexport type WilliamsLine = Nominal<number, 'Williams'>\r\n\r\nexport type WilliamsData = [WilliamsLine]\r\n\r\nexport default class WilliamsIndicator extends ChartIndicator<WilliamsIndicatorOptions, WilliamsData> {\r\n  williamsSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<WilliamsIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options)\r\n    this.williamsSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'williams',\r\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 0, minValue: -100})\r\n    }, paneIndex);\r\n\r\n    this.williamsSeries.attachPrimitive(\r\n      new RegionPrimitive({\r\n        upPrice: -20,\r\n        lowPrice: -80,\r\n        lineColor: this.options.priceLineColor,\r\n        backgroundColor: this.options.backgroundColor\r\n      })\r\n    );\r\n  }\r\n\r\n  getDefaultOptions(): WilliamsIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n\r\n  formula(c: Context): WilliamsData | undefined {\r\n    const highSeries = c.new_var(c.symbol.high, this.options.period);\r\n    const lowSeries = c.new_var(c.symbol.low, this.options.period);\r\n    const closeSeries = c.new_var(c.symbol.close, this.options.period);\r\n\r\n    if(!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) return;\r\n\r\n    // Get the arrays of values for the period\r\n    const highs = highSeries.getAll();\r\n    const lows = lowSeries.getAll();\r\n    const closes = closeSeries.getAll();\r\n\r\n    // Calculate Williams %R manually\r\n    // Formula: Williams %R = ((Highest High - Close) / (Highest High - Lowest Low)) × -100\r\n\r\n    // Validate we have enough data\r\n    if (highs.length < this.options.period || lows.length < this.options.period || closes.length < this.options.period) {\r\n      return;\r\n    }\r\n\r\n    // Find the highest high and lowest low over the period\r\n    const highestHigh = Math.max(...highs);\r\n    const lowestLow = Math.min(...lows);\r\n    const currentClose = closes[closes.length - 1]; // Most recent close\r\n\r\n    // Avoid division by zero\r\n    const range = highestHigh - lowestLow;\r\n    if (range === 0) {\r\n      return [0 as WilliamsLine]; // If no price movement, return 0\r\n    }\r\n\r\n    // Calculate Williams %R\r\n    const williamsR = ((highestHigh - currentClose) / range) * -100;\r\n\r\n    // Return the result, ensuring it's within the expected range (-100 to 0)\r\n    const clampedWilliamsR = Math.max(-100, Math.min(0, williamsR));\r\n\r\n    // Debug logging (can be removed in production)\r\n    console.log(`Williams %R Calculation:`, {\r\n      period: this.options.period,\r\n      highestHigh,\r\n      lowestLow,\r\n      currentClose,\r\n      range,\r\n      williamsR,\r\n      clampedWilliamsR\r\n    });\r\n\r\n    return [clampedWilliamsR as WilliamsLine];\r\n  }\r\n\r\n\r\n  applyIndicatorData() {\r\n    const williamsData: SingleValueData[] = [];\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      if(!value) continue;\r\n      williamsData.push({time: bar.time as Time, value: value[0]})\r\n    }\r\n\r\n    this.williamsSeries.setData(williamsData)\r\n  }\r\n\r\n  remove() {\r\n    super.remove()\r\n    this.chart.removeSeries(this.williamsSeries);\r\n  }\r\n\r\n  _applyOptions() {\r\n    this.williamsSeries.applyOptions({color: this.options.color})\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n\r\n  setPaneIndex(paneIndex: number) {\r\n    this.williamsSeries.moveToPane(paneIndex)\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.williamsSeries.getPane().paneIndex()\r\n  }\r\n}"], "names": ["defaultOptions", "WilliamsIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "highSeries", "lowSeries", "closeSeries", "highs", "lows", "closes", "highestHigh", "lowestLow", "currentClose", "range", "<PERSON><PERSON>sR", "clampedWilliamsR", "williamsData", "bar", "value"], "mappings": ";;;;;;;AAaO,MAAMA,IAA2C;AAAA,EACtD,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,SAAS;AACX;AAMA,MAAqBC,UAA0BC,EAAuD;AAAA,EAGpG,YAAYC,GAAkBC,GAA6CC,GAAoB;AAC7F,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAIO,SAAA,iBAAiBH,EAAM,UAAUI,GAAY;AAAA,MAChD,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,GAAG,UAAU,KAAK,CAAA;AAAA,OAChFH,CAAS,GAEZ,KAAK,eAAe;AAAA,MAClB,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAA8C;AACrC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAsC;AACtC,UAAAC,IAAaD,EAAE,QAAQA,EAAE,OAAO,MAAM,KAAK,QAAQ,MAAM,GACzDE,IAAYF,EAAE,QAAQA,EAAE,OAAO,KAAK,KAAK,QAAQ,MAAM,GACvDG,IAAcH,EAAE,QAAQA,EAAE,OAAO,OAAO,KAAK,QAAQ,MAAM;AAE9D,QAAA,CAACC,EAAW,WAAA,KAAgB,CAACC,EAAU,gBAAgB,CAACC,EAAY,aAAc;AAG/E,UAAAC,IAAQH,EAAW,OAAO,GAC1BI,IAAOH,EAAU,OAAO,GACxBI,IAASH,EAAY,OAAO;AAMlC,QAAIC,EAAM,SAAS,KAAK,QAAQ,UAAUC,EAAK,SAAS,KAAK,QAAQ,UAAUC,EAAO,SAAS,KAAK,QAAQ;AAC1G;AAIF,UAAMC,IAAc,KAAK,IAAI,GAAGH,CAAK,GAC/BI,IAAY,KAAK,IAAI,GAAGH,CAAI,GAC5BI,IAAeH,EAAOA,EAAO,SAAS,CAAC,GAGvCI,IAAQH,IAAcC;AAC5B,QAAIE,MAAU;AACZ,aAAO,CAAC,CAAiB;AAIrB,UAAAC,KAAcJ,IAAcE,KAAgBC,IAAS,MAGrDE,IAAmB,KAAK,IAAI,MAAM,KAAK,IAAI,GAAGD,CAAS,CAAC;AAG9D,mBAAQ,IAAI,4BAA4B;AAAA,MACtC,QAAQ,KAAK,QAAQ;AAAA,MACrB,aAAAJ;AAAA,MACA,WAAAC;AAAA,MACA,cAAAC;AAAA,MACA,OAAAC;AAAA,MACA,WAAAC;AAAA,MACA,kBAAAC;AAAA,IAAA,CACD,GAEM,CAACA,CAAgC;AAAA,EAAA;AAAA,EAI1C,qBAAqB;AACnB,UAAMC,IAAkC,CAAC;AAC/B,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,MAAIC,KACSF,EAAA,KAAK,EAAC,MAAMC,EAAI,MAAc,OAAOC,EAAM,CAAC,GAAE;AAAA,IAAA;AAGxD,SAAA,eAAe,QAAQF,CAAY;AAAA,EAAA;AAAA,EAG1C,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,cAAc;AAAA,EAAA;AAAA,EAG7C,gBAAgB;AACd,SAAK,eAAe,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GAC5D,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAI1B,aAAalB,GAAmB;AACzB,SAAA,eAAe,WAAWA,CAAS;AAAA,EAAA;AAAA,EAG1C,eAAuB;AACrB,WAAO,KAAK,eAAe,QAAQ,EAAE,UAAU;AAAA,EAAA;AAEnD;"}