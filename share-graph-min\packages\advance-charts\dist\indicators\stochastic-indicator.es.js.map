{"version": 3, "file": "stochastic-indicator.es.js", "sources": ["../../src/indicators/stochastic-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {SMA} from \"technicalindicators\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\r\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\nimport {max, min} from \"es-toolkit/compat\";\r\nimport {ensureDefined} from \"../helpers/assertions\";\r\n\r\nexport interface StochasticIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  signalColor: string,\r\n  period: number\r\n  priceLineColor: string\r\n  backgroundColor: string\r\n  signalPeriod: number\r\n}\r\n\r\nexport const defaultOptions: StochasticIndicatorOptions = {\r\n  color: \"#2962ff\",\r\n  signalColor: '#ff6d00',\r\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\r\n  backgroundColor: \"#2196f31a\",\r\n  period: 14,\r\n  overlay: false,\r\n  signalPeriod: 3\r\n}\r\n\r\nexport type KStochasticLine = Nominal<number, 'K_Stochastic'>\r\nexport type DStochasticLine = Nominal<number, 'D_Stochastic'>\r\nexport type StochasticData = [KStochasticLine, DStochasticLine]\r\n\r\nexport default class StochasticIndicator extends ChartIndicator<StochasticIndicatorOptions, StochasticData> {\r\n  kSeries: ISeriesApi<SeriesType>\r\n  dSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<StochasticIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options)\r\n    this.kSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'Stochastic',\r\n    }, paneIndex);\r\n\r\n    this.dSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.signalColor,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'Stochastic',\r\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 90, minValue: 10})\r\n    }, paneIndex);\r\n\r\n    this.kSeries.attachPrimitive(\r\n      new RegionPrimitive({\r\n        upPrice: 80,\r\n        lowPrice: 20,\r\n        lineColor: this.options.priceLineColor,\r\n        backgroundColor: this.options.backgroundColor,\r\n      })\r\n    );\r\n  }\r\n\r\n  getDefaultOptions(): StochasticIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n  \r\n  formula(c: Context): StochasticData | undefined {\r\n    const highSeries = c.new_var(c.symbol.high, this.options.period)\r\n    const lowSeries = c.new_var(c.symbol.low, this.options.period)\r\n    const dSmaSeries = c.new_var(NaN, this.options.signalPeriod)\r\n\r\n    if(!highSeries.calculable() || !lowSeries.calculable()) return;\r\n\r\n    const highest = ensureDefined(max(highSeries.getAll()))\r\n    const lowest = ensureDefined(min(lowSeries.getAll()));\r\n\r\n    let k = (c.symbol.close - lowest) / (highest - lowest) * 100;\r\n    k = isNaN(k) ? 0 : k\r\n\r\n    dSmaSeries.set(k);\r\n\r\n    if(!dSmaSeries.calculable()) return;\r\n\r\n    const [d] = new SMA({\r\n      period: this.options.signalPeriod, \r\n      values: dSmaSeries.getAll()\r\n    }).result;\r\n\r\n    return [k as KStochasticLine, d as DStochasticLine]\r\n  }\r\n\r\n  applyIndicatorData() {\r\n    const kData: SingleValueData[] = []\r\n    const dData: SingleValueData[] = []\r\n\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      const time = bar.time as Time;\r\n      if(!value) continue;\r\n\r\n      kData.push({time, value: value[0]})\r\n      dData.push({time, value: value[1]})\r\n    }\r\n    this.kSeries.setData(kData)\r\n    this.dSeries.setData(dData)\r\n  }\r\n\r\n  remove() {\r\n    super.remove()\r\n    this.chart.removeSeries(this.kSeries);\r\n    this.chart.removeSeries(this.dSeries);\r\n  }\r\n\r\n  _applyOptions() {\r\n    this.kSeries.applyOptions({color: this.options.color})\r\n    this.dSeries.applyOptions({color: this.options.signalColor})\r\n\r\n    this.calcIndicatorData()\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n\r\n  setPaneIndex(paneIndex: number) {\r\n    this.kSeries.moveToPane(paneIndex)\r\n    this.dSeries.moveToPane(paneIndex)\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.kSeries.getPane().paneIndex()\r\n  }\r\n}"], "names": ["defaultOptions", "StochasticIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "highSeries", "lowSeries", "dSmaSeries", "highest", "ensureDefined", "max", "lowest", "min", "k", "SMA", "kData", "dData", "bar", "value", "time"], "mappings": ";;;;;;;;;;AAkBO,MAAMA,IAA6C;AAAA,EACxD,OAAO;AAAA,EACP,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,cAAc;AAChB;AAMA,MAAqBC,UAA4BC,EAA2D;AAAA,EAI1G,YAAYC,GAAkBC,GAA+CC,GAAoB;AAC/F,UAAMF,GAAOC,CAAO;AAJtB,IAAAE,EAAA;AACA,IAAAA,EAAA;AAIO,SAAA,UAAUH,EAAM,UAAUI,GAAY;AAAA,MACzC,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,OACbF,CAAS,GAEP,KAAA,UAAUF,EAAM,UAAUI,GAAY;AAAA,MACzC,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,IAAI,UAAU,GAAG,CAAA;AAAA,OAC/EH,CAAS,GAEZ,KAAK,QAAQ;AAAA,MACX,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAAgD;AACvC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAwC;AACxC,UAAAC,IAAaD,EAAE,QAAQA,EAAE,OAAO,MAAM,KAAK,QAAQ,MAAM,GACzDE,IAAYF,EAAE,QAAQA,EAAE,OAAO,KAAK,KAAK,QAAQ,MAAM,GACvDG,IAAaH,EAAE,QAAQ,KAAK,KAAK,QAAQ,YAAY;AAE3D,QAAG,CAACC,EAAW,WAAA,KAAgB,CAACC,EAAU,aAAc;AAExD,UAAME,IAAUC,EAAcC,EAAIL,EAAW,OAAQ,CAAA,CAAC,GAChDM,IAASF,EAAcG,EAAIN,EAAU,OAAQ,CAAA,CAAC;AAEpD,QAAIO,KAAKT,EAAE,OAAO,QAAQO,MAAWH,IAAUG,KAAU;AAKtD,QAJCE,IAAA,MAAMA,CAAC,IAAI,IAAIA,GAEnBN,EAAW,IAAIM,CAAC,GAEb,CAACN,EAAW,aAAc;AAE7B,UAAM,CAAC,CAAC,IAAI,IAAIO,EAAI;AAAA,MAClB,QAAQ,KAAK,QAAQ;AAAA,MACrB,QAAQP,EAAW,OAAO;AAAA,IAC3B,CAAA,EAAE;AAEI,WAAA,CAACM,GAAsB,CAAoB;AAAA,EAAA;AAAA,EAGpD,qBAAqB;AACnB,UAAME,IAA2B,CAAC,GAC5BC,IAA2B,CAAC;AAExB,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI,OACZE,IAAOF,EAAI;AACjB,MAAIC,MAEJH,EAAM,KAAK,EAAC,MAAAI,GAAM,OAAOD,EAAM,CAAC,GAAE,GAClCF,EAAM,KAAK,EAAC,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAE;AAAA,IAAA;AAE/B,SAAA,QAAQ,QAAQH,CAAK,GACrB,KAAA,QAAQ,QAAQC,CAAK;AAAA,EAAA;AAAA,EAG5B,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,OAAO,GAC/B,KAAA,MAAM,aAAa,KAAK,OAAO;AAAA,EAAA;AAAA,EAGtC,gBAAgB;AACd,SAAK,QAAQ,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GACrD,KAAK,QAAQ,aAAa,EAAC,OAAO,KAAK,QAAQ,aAAY,GAE3D,KAAK,kBAAkB,GACvB,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAI1B,aAAajB,GAAmB;AACzB,SAAA,QAAQ,WAAWA,CAAS,GAC5B,KAAA,QAAQ,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGnC,eAAuB;AACrB,WAAO,KAAK,QAAQ,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE5C;"}