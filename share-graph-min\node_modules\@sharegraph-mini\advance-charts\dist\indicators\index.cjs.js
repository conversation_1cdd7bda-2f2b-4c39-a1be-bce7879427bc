"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const a=require("./bb-indicator.cjs.js"),o=require("./macd-indicator.cjs.js"),e=require("./rsi-indicator.cjs.js"),r=require("./volume-indicator.cjs.js"),t=require("./indicator-factory.cjs.js"),i=require("./sma-indicator.cjs.js"),c=require("./stochastic-indicator.cjs.js"),d=require("./ema-indicator.cjs.js"),n=require("./wma-indicator.cjs.js"),I=require("./momentum-indicator.cjs.js"),u=require("./williams-indicator.cjs.js");t.IndicatorFactory.registerIndicator("bb",a.default);t.IndicatorFactory.registerIndicator("rsi",e.default);t.IndicatorFactory.registerIndicator("macd",o.default);t.IndicatorFactory.registerIndicator("volume_overlay",r.default,{overlay:!0});t.IndicatorFactory.registerIndicator("volume",r.default);t.IndicatorFactory.registerIndicator("sma",i.default);t.IndicatorFactory.registerIndicator("stochastic",c.default);t.IndicatorFactory.registerIndicator("ema",d.default);t.IndicatorFactory.registerIndicator("wma",n.default);t.IndicatorFactory.registerIndicator("momentum",I.default);t.IndicatorFactory.registerIndicator("williams",u.default);exports.BBIndicator=a.default;exports.MACDIndicator=o.default;exports.RSIIndicator=e.default;exports.VolumeIndicator=r.default;exports.IndicatorFactory=t.IndicatorFactory;exports.SMAIndicator=i.default;exports.StochasticIndicator=c.default;exports.EMAIndicator=d.default;exports.WMAIndicator=n.default;exports.MomentumIndicator=I.default;exports.WilliamsIndicator=u.default;
//# sourceMappingURL=index.cjs.js.map
