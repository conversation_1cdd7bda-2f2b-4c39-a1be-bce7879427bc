"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const s=require("../../helpers/assertions.cjs.js"),h=require("../primitive-base.cjs.js"),c=require("lightweight-charts"),u=require("../../helpers/line-style.cjs.js"),r={lineColor:"#eee",lineWidth:1,lineDash:c.LineStyle.Solid};class P extends h.PrimitivePaneViewBase{getXCoordinate(i){return"time"in i?this.timeToCoordinate(i.time):i.x}_drawImpl(i){if(this.data.length===0)return;const t=i.context;t.scale(i.horizontalPixelRatio,i.verticalPixelRatio),t.lineWidth=this.options.lineWidth,t.strokeStyle=this.options.lineColor;const n=new Path2D;t.beginPath();const l=this.data.map(e=>({x:this.getXCoordinate(e),y:this.priceToCoordinate(e.price)})),[o,...a]=l;n.moveTo(s.ensureNotNull(o.x),s.ensureNotNull(o.y));for(const e of a)!e.x||!e.y||n.lineTo(e.x,e.y);u.setLineStyle(t,this.options.lineDash),t.stroke(n)}defaultOptions(){return r}}exports.LinePrimitiveOptionsDefault=r;exports.LinePrimitivePaneView=P;
//# sourceMappingURL=line.cjs.js.map
