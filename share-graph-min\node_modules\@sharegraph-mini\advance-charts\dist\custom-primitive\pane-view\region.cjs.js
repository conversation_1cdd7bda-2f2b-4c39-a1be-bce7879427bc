"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const m=require("../primitive-base.cjs.js"),p=require("../../helpers/assertions.cjs.js"),h={backgroundColor:"#2196f31a",lineColor:"#787b86",lineWidth:1,lineStyle:[]};class P extends m.PrimitivePaneViewBase{drawBackground(g){const n=this.data,u=this.getVisibleLogicalRange();n.length<2||u&&g.useBitmapCoordinateSpace(l=>{const e=l.context;e.scale(l.horizontalPixelRatio,l.verticalPixelRatio),e.beginPath();const s=new Path2D,a=0,r=n.length,o=n[a];s.moveTo(o.x,p.ensureDefined(o.points.at(0)));for(let i=a+1;i<r;i++){const t=n[i];s.lineTo(t.x,t.points.at(0))}for(let i=r-1;i>=a;i--){const t=n[i];s.lineTo(t.x,t.points.at(-1))}s.lineTo(o.x,p.ensureDefined(o.points.at(0))),s.closePath(),e.fillStyle=this.options.backgroundColor,e.fill(s);const c=o.points.map(()=>new Path2D);o.points.map((i,t)=>c[t].moveTo(o.x,i));for(let i=a+1;i<r;i++){const t=n[i];t.points.map((d,f)=>c[f].lineTo(t.x,d))}e.setLineDash(this.options.lineStyle),e.lineWidth=this.options.lineWidth,e.strokeStyle=this.options.lineColor,c.map(i=>e.stroke(i)),e.setLineDash([])})}defaultOptions(){return h}}exports.RegionPrimitiveOptionsDefault=h;exports.RegionPrimitivePaneView=P;
//# sourceMappingURL=region.cjs.js.map
