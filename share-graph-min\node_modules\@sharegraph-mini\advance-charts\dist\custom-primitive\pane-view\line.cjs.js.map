{"version": 3, "file": "line.cjs.js", "sources": ["../../../src/custom-primitive/pane-view/line.ts"], "sourcesContent": ["import {BitmapCoordinatesRenderingScope} from 'fancy-canvas';\r\nimport {ensureNotNull} from '../../helpers/assertions';\r\nimport {PrimitivePaneViewBase} from '../primitive-base';\r\nimport {Coordinate, LineStyle, Time} from 'lightweight-charts';\r\nimport {setLineStyle} from '../../helpers/line-style';\r\n\r\ninterface LinePrimitiveOptions {\r\n  lineColor: string,\r\n  lineWidth: number,\r\n  lineDash: LineStyle\r\n}\r\n\r\nexport const LinePrimitiveOptionsDefault: LinePrimitiveOptions = {\r\n  lineColor: '#eee',\r\n  lineWidth: 1,\r\n  lineDash: LineStyle.Solid\r\n}\r\n\r\nexport type LineData = {\r\n  x: Coordinate,\r\n  price: number,\r\n} | {\r\n  time: Time,\r\n  price: number,\r\n}\r\n\r\nexport class LinePrimitivePaneView extends PrimitivePaneViewBase<LinePrimitiveOptions, LineData> {\r\n  getXCoordinate(data: LineData) {\r\n    return 'time' in data ? this.timeToCoordinate(data.time) : data.x;\r\n  }\r\n  _drawImpl(renderingScope: BitmapCoordinatesRenderingScope): void {\r\n    if(this.data.length === 0) return;\r\n    const ctx = renderingScope.context;\r\n\t\tctx.scale(renderingScope.horizontalPixelRatio, renderingScope.verticalPixelRatio);\r\n    ctx.lineWidth = this.options.lineWidth\r\n    ctx.strokeStyle = this.options.lineColor\r\n    const lines = new Path2D();\r\n    ctx.beginPath();\r\n    const points = this.data.map((item) => ({\r\n      x: this.getXCoordinate(item),\r\n      y: this.priceToCoordinate(item.price),\r\n    }));\r\n    const [firstPoint, ...restPoints] = points;\r\n    lines.moveTo(ensureNotNull(firstPoint.x), ensureNotNull(firstPoint.y));\r\n    for(const point of restPoints) {\r\n      if(!point.x || !point.y) continue;\r\n      lines.lineTo(point.x, point.y);\r\n    }\r\n\r\n    setLineStyle(ctx, this.options.lineDash)\r\n    ctx.stroke(lines)\r\n  }\r\n\r\n  defaultOptions() {\r\n    return LinePrimitiveOptionsDefault\r\n  }\r\n}"], "names": ["LinePrimitiveOptionsDefault", "LineStyle", "LinePrimitivePaneView", "PrimitivePaneViewBase", "data", "renderingScope", "ctx", "lines", "points", "item", "firstPoint", "restPoints", "ensureNotNull", "point", "setLineStyle"], "mappings": "sPAYaA,EAAoD,CAC/D,UAAW,OACX,UAAW,EACX,SAAUC,EAAAA,UAAU,KACtB,EAUO,MAAMC,UAA8BC,EAAAA,qBAAsD,CAC/F,eAAeC,EAAgB,CAC7B,MAAO,SAAUA,EAAO,KAAK,iBAAiBA,EAAK,IAAI,EAAIA,EAAK,CAAA,CAElE,UAAUC,EAAuD,CAC5D,GAAA,KAAK,KAAK,SAAW,EAAG,OAC3B,MAAMC,EAAMD,EAAe,QAC7BC,EAAI,MAAMD,EAAe,qBAAsBA,EAAe,kBAAkB,EAC1EC,EAAA,UAAY,KAAK,QAAQ,UACzBA,EAAA,YAAc,KAAK,QAAQ,UACzB,MAAAC,EAAQ,IAAI,OAClBD,EAAI,UAAU,EACd,MAAME,EAAS,KAAK,KAAK,IAAKC,IAAU,CACtC,EAAG,KAAK,eAAeA,CAAI,EAC3B,EAAG,KAAK,kBAAkBA,EAAK,KAAK,CAAA,EACpC,EACI,CAACC,EAAY,GAAGC,CAAU,EAAIH,EAC9BD,EAAA,OAAOK,EAAAA,cAAcF,EAAW,CAAC,EAAGE,EAAc,cAAAF,EAAW,CAAC,CAAC,EACrE,UAAUG,KAASF,EACd,CAACE,EAAM,GAAK,CAACA,EAAM,GACtBN,EAAM,OAAOM,EAAM,EAAGA,EAAM,CAAC,EAGlBC,EAAAA,aAAAR,EAAK,KAAK,QAAQ,QAAQ,EACvCA,EAAI,OAAOC,CAAK,CAAA,CAGlB,gBAAiB,CACR,OAAAP,CAAA,CAEX"}