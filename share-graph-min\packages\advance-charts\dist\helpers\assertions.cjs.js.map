{"version": 3, "file": "assertions.cjs.js", "sources": ["../../src/helpers/assertions.ts"], "sourcesContent": ["/**\r\n * Ensures that value is defined.\r\n * Throws if the value is undefined, returns the original value otherwise.\r\n *\r\n * @param value - The value, or undefined.\r\n * @returns The passed value, if it is not undefined\r\n */\r\nexport function ensureDefined(value: undefined): never;\r\nexport function ensureDefined<T>(value: T | undefined): T;\r\nexport function ensureDefined<T>(value: T | undefined): T {\r\n\tif (value === undefined) {\r\n\t\tthrow new Error('Value is undefined');\r\n\t}\r\n\r\n\treturn value;\r\n}\r\n\r\n/**\r\n * Ensures that value is not null.\r\n * Throws if the value is null, returns the original value otherwise.\r\n *\r\n * @param value - The value, or null.\r\n * @returns The passed value, if it is not null\r\n */\r\nexport function ensureNotNull(value: null): never;\r\nexport function ensureNotNull<T>(value: T | null): T;\r\nexport function ensureNotNull<T>(value: T | null): T {\r\n\tif (value === null) {\r\n\t\tthrow new Error('Value is null');\r\n\t}\r\n\r\n\treturn value;\r\n}\r\n"], "names": ["ensureDefined", "value", "ensureNotNull"], "mappings": "gFASO,SAASA,EAAiBC,EAAyB,CACzD,GAAIA,IAAU,OACP,MAAA,IAAI,MAAM,oBAAoB,EAG9B,OAAAA,CACR,CAWO,SAASC,EAAiBD,EAAoB,CACpD,GAAIA,IAAU,KACP,MAAA,IAAI,MAAM,eAAe,EAGzB,OAAAA,CACR"}