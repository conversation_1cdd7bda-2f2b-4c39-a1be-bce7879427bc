{"version": 3, "file": "i-advance-chart.es.js", "sources": ["../../src/advance-chart/i-advance-chart.ts"], "sourcesContent": ["import {IChartApi, IPaneApi, ISeriesApi, PriceScaleMode, SeriesType, Time} from \"lightweight-charts\";\r\nimport { ISubscription} from \"../helpers/delegate\";\r\nimport {ChartIndicator} from \"../indicators/abstract-indicator\";\r\nimport {NumberFormatter} from \"../helpers/number-formatter\";\r\nimport {OHLCVExtraData} from \"../interface\";\r\n\r\nexport enum Period {\r\n  minute = 'minute',\r\n  hour = 'hour',\r\n  day = 'day',\r\n  week = 'week',\r\n  month = 'month'\r\n}\r\n\r\nexport interface Interval {\r\n  period: Period;\r\n  times: number;\r\n}\r\n\r\nexport type IGroupIndicatorByPane = {\r\n  pane: IPaneApi<Time>;\r\n  indicators: ChartIndicator[];\r\n};\r\n\r\nexport type IAdvanceChartType =\r\n  | 'line'\r\n  | 'candle'\r\n  | 'mountain'\r\n  | 'bar'\r\n  | 'baseline'\r\n  | 'base-mountain';\r\n\r\nexport interface IAdvanceChartOptions {\r\n    upColor: string;\r\n    downColor: string;\r\n    mainColor: string;\r\n    highLowLineVisible: boolean;\r\n    highLineColor: string;\r\n    lowLineColor: string;\r\n    priceScaleMode: PriceScaleMode,\r\n    priceLineVisible: boolean,\r\n    locale: string,\r\n    gridColor: string;\r\n    axesColor: string;\r\n    fontSize?: number;\r\n    fontFamily?: string;\r\n    tzDisplay: string\r\n    height: number;\r\n}\r\nexport interface IAdvanceChart {\r\n  chartApi: IChartApi;\r\n  chartType: IAdvanceChartType | null\r\n  options: IAdvanceChartOptions;\r\n  numberFormatter: NumberFormatter\r\n  loading: boolean\r\n  mainSeries: ISeriesApi<SeriesType> | null\r\n  dataInterval: Interval\r\n  updated(): ISubscription\r\n  chartTypeChanged(): ISubscription\r\n  indicatorChanged(): ISubscription<string, 'add' | 'remove'>\r\n  crosshairMoved(): ISubscription<OHLCVExtraData, OHLCVExtraData>\r\n  destroyed(): ISubscription\r\n  chartHovered(): ISubscription<boolean>\r\n  onLoading(): ISubscription<boolean>\r\n  optionChanged(): ISubscription\r\n  mainSeriesChanged(): ISubscription\r\n}\r\n"], "names": ["Period"], "mappings": "AAMY,IAAAA,sBAAAA,OACVA,EAAA,SAAS,UACTA,EAAA,OAAO,QACPA,EAAA,MAAM,OACNA,EAAA,OAAO,QACPA,EAAA,QAAQ,SALEA,IAAAA,KAAA,CAAA,CAAA;"}