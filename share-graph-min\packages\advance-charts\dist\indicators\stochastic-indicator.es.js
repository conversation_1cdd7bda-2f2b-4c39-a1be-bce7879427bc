var u = Object.defineProperty;
var m = (r, i, e) => i in r ? u(r, i, { enumerable: !0, configurable: !0, writable: !0, value: e }) : r[i] = e;
var n = (r, i, e) => m(r, typeof i != "symbol" ? i + "" : i, e);
import { LineSeries as p } from "lightweight-charts";
import { SMA as S } from "technicalindicators";
import { ChartIndicator as f } from "./abstract-indicator.es.js";
import { RegionPrimitive as g } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as v } from "../helpers/utils.es.js";
import { max as b, min as k } from "es-toolkit/compat";
import { ensureDefined as h } from "../helpers/assertions.es.js";
const P = {
  color: "#2962ff",
  signalColor: "#ff6d00",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#2196f31a",
  period: 14,
  overlay: !1,
  signalPeriod: 3
};
class O extends f {
  constructor(e, s, o) {
    super(e, s);
    n(this, "kSeries");
    n(this, "dSeries");
    this.kSeries = e.addSeries(p, {
      color: this.options.color,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "Stochastic"
    }, o), this.dSeries = e.addSeries(p, {
      color: this.options.signalColor,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "Stochastic",
      autoscaleInfoProvider: v({ maxValue: 90, minValue: 10 })
    }, o), this.kSeries.attachPrimitive(
      new g({
        upPrice: 80,
        lowPrice: 20,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return P;
  }
  formula(e) {
    const s = e.new_var(e.symbol.high, this.options.period), o = e.new_var(e.symbol.low, this.options.period), t = e.new_var(NaN, this.options.signalPeriod);
    if (!s.calculable() || !o.calculable()) return;
    const l = h(b(s.getAll())), c = h(k(o.getAll()));
    let a = (e.symbol.close - c) / (l - c) * 100;
    if (a = isNaN(a) ? 0 : a, t.set(a), !t.calculable()) return;
    const [d] = new S({
      period: this.options.signalPeriod,
      values: t.getAll()
    }).result;
    return [a, d];
  }
  applyIndicatorData() {
    const e = [], s = [];
    for (const o of this._executionContext.data) {
      const t = o.value, l = o.time;
      t && (e.push({ time: l, value: t[0] }), s.push({ time: l, value: t[1] }));
    }
    this.kSeries.setData(e), this.dSeries.setData(s);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.kSeries), this.chart.removeSeries(this.dSeries);
  }
  _applyOptions() {
    this.kSeries.applyOptions({ color: this.options.color }), this.dSeries.applyOptions({ color: this.options.signalColor }), this.calcIndicatorData(), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.kSeries.moveToPane(e), this.dSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.kSeries.getPane().paneIndex();
  }
}
export {
  O as default,
  P as defaultOptions
};
//# sourceMappingURL=stochastic-indicator.es.js.map
