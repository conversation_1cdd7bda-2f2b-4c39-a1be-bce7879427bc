export type INumberParam = number | undefined | null;
export declare class NumberFormatter {
    locale: string;
    constructor(locale?: string);
    private _volumeFormatter;
    get volumeFormatter(): Intl.NumberFormat;
    private _decimalFormatter;
    get decimalFormatter(): Intl.NumberFormat;
    private _percentFormatter;
    get percentFormatter(): Intl.NumberFormat;
    volume(num: INumberParam): string;
    decimal(num: INumberParam): string;
    percent(num: INumberParam): string;
}
export declare const NumberFormatterFactory: {
    _cache: Map<string, NumberFormatter>;
    formatter(locale: string): NumberFormatter;
};
