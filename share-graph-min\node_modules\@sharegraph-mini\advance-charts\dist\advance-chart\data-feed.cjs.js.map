{"version": 3, "file": "data-feed.cjs.js", "sources": ["../../src/advance-chart/data-feed.ts"], "sourcesContent": ["import { Logical, LogicalRange, Time } from 'lightweight-charts';\r\nimport { Destroyable, OHLCVSimple } from '../interface';\r\nimport { dayjsToTime, timeToDate, timeToDayjs } from '../helpers/utils';\r\nimport { AdvanceChart } from './advance-chart';\r\nimport {mergeOhlcData} from '../helpers/mergeData';\r\nimport {Delegate, IPublicDelegate} from '../helpers/delegate';\r\nimport {Interval, Period} from './i-advance-chart';\r\nimport {groupBy} from 'es-toolkit';\r\nimport dayjs from '../helpers/dayjs-setup';\r\nimport {timeKey} from './time-key';\r\nimport {log} from '../helpers/log';\r\nimport {Dayjs} from 'dayjs';\r\n\r\nexport interface IDataFetchQuery {\r\n  from: Date;\r\n  to: Date;\r\n  interval: Interval;\r\n}\r\n\r\nexport type IDataFetchUtils = {\r\n  forward: (time: Time, step: number) => Dayjs\r\n}\r\n\r\nexport interface IDataFetch {\r\n  refeshTime?: number;\r\n\r\n  /**\r\n   * Fetches the initial data when:\r\n   * - The chart is first loaded\r\n   * - The user changes the visible time range\r\n   * - The interval/period is changed\r\n   * \r\n   * This provides the base dataset that the chart will display initially.\r\n   * Subsequent updates/pagination will build upon this data.\r\n   */\r\n  fetchInitialData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;\r\n\r\n  /**\r\n   * Fetches historical data that's outside the current visible chart range.\r\n   * This data is used to:\r\n   * - Calculate indicators that require lookback periods\r\n   * - Provide seamless scrolling experience when user reaches chart boundaries\r\n   * Automatically triggered when scrolling to the beginning/end of loaded data.\r\n   */\r\n  fetchPaginationData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;\r\n\r\n  /**\r\n   * Fetches updated data periodically based on refreshTime interval.\r\n   * Queries data from the timestamp of last data point to current time.\r\n   * New data points with same timestamps will overwrite existing ones.\r\n   * Used for real-time updates to the chart.\r\n   */\r\n  fetchUpdateData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;\r\n}\r\n\r\nexport function roundTime(time: Time, period: Period) {\r\n  switch (period) {\r\n    case Period.minute:\r\n    case Period.hour:\r\n    case Period.day:\r\n      return time;\r\n    case Period.week:\r\n      return dayjs.tz(timeToDayjs(time), 'UTC').startOf('week').unix() as Time;\r\n    case Period.month:\r\n      return dayjs.tz(timeToDayjs(time), 'UTC').startOf('month').unix() as Time;\r\n    default:\r\n      throw new Error(`Period : ${period} not support`)\r\n  }\r\n}\r\n\r\nexport const aggregate = (items: OHLCVSimple[]) => {\r\n  const high = Math.max(...items.map(item => item.high))\r\n  const low = Math.min(...items.map(item => item.low))\r\n  const volume = items.reduce((acc, item) => acc + item.volume, 0)\r\n  const open = items[0].open\r\n  const close = items[items.length - 1].close\r\n  return {\r\n    time: items[0].time, \r\n    open,\r\n    high,\r\n    low,\r\n    close,\r\n    volume\r\n  } satisfies OHLCVSimple\r\n}\r\n\r\n\r\nexport class DataFeed implements Destroyable {\r\n  _loading: boolean = false;\r\n  _data: OHLCVSimple[] = [];\r\n  _dataChanged = new Delegate();\r\n  interval: Interval = { period: Period.day, times: 1 };\r\n  initialData = false;\r\n  endOfData = false;\r\n  _refeshTimer: NodeJS.Timeout | null = null;\r\n\r\n  _destroyed = false\r\n\r\n  \r\n  constructor(\r\n    public advanceChart: AdvanceChart,\r\n    protected dataFetch: IDataFetch\r\n  ) {\r\n    this.onVisibleLogicalRangeChange =\r\n      this.onVisibleLogicalRangeChange.bind(this);\r\n    this.advanceChart.chartApi\r\n      .timeScale()\r\n      .subscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange);\r\n\r\n    if(this.dataFetch.refeshTime) {\r\n      this._refeshTimer = setInterval(async() => this.updateData(), this.dataFetch.refeshTime)\r\n    }\r\n  }\r\n\r\n  get data () {\r\n    return this._data\r\n  }\r\n\r\n  set data (d: OHLCVSimple[]) {\r\n    this._data = d;\r\n\r\n    this._dataChanged.fire()\r\n  }\r\n\r\n  isNeedPaging(logicalRange: LogicalRange) {\r\n    if (!this.initialData) return false;\r\n    if (this.data.length === 0) return false;\r\n    if (this.endOfData) return false;\r\n    const { from } = logicalRange;\r\n    if (from > 30) return false;\r\n    return true\r\n  }\r\n\r\n\r\n\r\n  groupData() {\r\n    const interval = this.interval\r\n\r\n    if(interval.period === Period.day) return this._data\r\n    if(interval.period === Period.hour) return this._data\r\n    if(interval.period === Period.minute) return this._data\r\n\r\n    const data = groupBy(this._data, item => timeKey(item.time, interval));\r\n\r\n    return Object.entries(data)\r\n      .map(([keyBy, items]) => [parseInt(keyBy), aggregate(items)] as const)\r\n      .sort((a, b) => a[0] - b[0]).map(item => item[1]);\r\n  }\r\n\r\n  processNewData(data: OHLCVSimple[]) {\r\n    const newData = mergeOhlcData(data, this.data);\r\n    if (newData.length === this.data.length) return;\r\n\r\n    this.data = newData\r\n    this.advanceChart.setData(this.groupData(), this.interval);\r\n\r\n    return newData;\r\n  }\r\n\r\n  async updateData() {\r\n    const lastData = this.data[this.data.length - 1];\r\n    const lastTime = lastData.time;\r\n    const data = await this.dataFetch.fetchUpdateData({\r\n      interval: this.interval,\r\n      from: timeToDate(lastTime),\r\n      to: new Date\r\n    }, { forward: this.forward.bind(this) })\r\n\r\n    if(this._destroyed) return\r\n\r\n    this.processNewData(data)\r\n  }\r\n\r\n  async pagingData(logicalRange: LogicalRange) {\r\n    const { from } = logicalRange;\r\n    const toTime = this.data[0].time;\r\n    const toDate = timeToDayjs(toTime);\r\n    const fromDate = this.forward(toTime, from - 200);\r\n    \r\n    const data = await this.dataFetch.fetchPaginationData({\r\n      interval: this.interval,\r\n      from: fromDate.toDate(),\r\n      to: toDate.toDate(),\r\n    }, { forward: this.forward.bind(this) });\r\n\r\n    if(this._destroyed) return\r\n    \r\n    if(!this.processNewData(data)) {\r\n      this.endOfData = true;\r\n    }\r\n  }\r\n\r\n  async onVisibleLogicalRangeChange(logicalRange: LogicalRange | null) {\r\n    if(!logicalRange) return;\r\n    const isSameRange = (range1: LogicalRange, range2: LogicalRange) => range1.from === range2.from && range1.to === range2.to\r\n    if(this.advanceChart.loading) return\r\n    this.advanceChart.loading = true\r\n    try {\r\n      while(this.isNeedPaging(logicalRange)) {\r\n        await this.pagingData(logicalRange)\r\n        const newRange = this.advanceChart.chartApi.timeScale().getVisibleLogicalRange();\r\n        if(!newRange) break;\r\n        if(isSameRange(newRange, logicalRange)) break;\r\n        logicalRange = newRange\r\n      }\r\n    } finally {\r\n      this.advanceChart.loading = false\r\n    }\r\n  }\r\n\r\n  forward(time: Time, step: number) {\r\n    step = Math.round(step);\r\n    const period = this.interval.period\r\n    switch (period) {\r\n      case Period.minute:\r\n        return timeToDayjs(time).add(step, 'minute');\r\n      case Period.hour:\r\n        return timeToDayjs(time).add(step, 'hour');\r\n      case Period.day:\r\n        return timeToDayjs(time).add(step, 'day');\r\n      case Period.week:\r\n        return timeToDayjs(time).add(step, 'week');\r\n      case Period.month:\r\n        return timeToDayjs(time).add(step, 'month');\r\n      default:\r\n        throw new Error(`Period : ${period} not support`)\r\n    }\r\n  }\r\n\r\n  async setRange({ from, to, interval }: IDataFetchQuery) {\r\n    this.resetState();\r\n    \r\n    this.advanceChart.loading = true;\r\n    this.interval = interval;\r\n    const data = await this.dataFetch.fetchInitialData({ from, to, interval }, { forward: this.forward.bind(this) });\r\n    this.data = data;\r\n    this.advanceChart.loading = false;\r\n\r\n    if(this._destroyed) return\r\n\r\n    this.advanceChart.setData(this.groupData(), interval);\r\n    this.initialData = true;\r\n\r\n    const timeScale = this.advanceChart.chartApi.timeScale();\r\n\r\n    const fromIndex = timeScale.timeToIndex(dayjsToTime(dayjs(from)), true)\r\n    const toIndex = timeScale.timeToIndex(dayjsToTime(dayjs(to)), true)\r\n\r\n    if(fromIndex !== undefined && toIndex !== undefined) {\r\n      this.advanceChart.fitRange({ from: fromIndex as unknown as Logical, to: toIndex as unknown as Logical })\r\n    }\r\n    \r\n    await this.onVisibleLogicalRangeChange(\r\n      timeScale.getVisibleLogicalRange()\r\n    );\r\n  }\r\n\r\n  private resetState() {\r\n    this.advanceChart.loading = false;\r\n    this.initialData = false;\r\n    this.endOfData = false;\r\n  }\r\n\r\n  dataChanged (){\r\n    return this._dataChanged as IPublicDelegate<typeof this._dataChanged>\r\n  }\r\n\r\n  trade(trade: { time: Time, price: number, volume: number }) {\r\n    const [lastPoint] = this.advanceChart.lastPoint();\r\n\r\n    const currentKey = timeKey(lastPoint.time, this.interval)\r\n    const newKey = timeKey(trade.time, this.interval)\r\n    if(newKey < currentKey) {\r\n      log.warn(`Trade timestamp ${newKey} is older than current ${currentKey}`)\r\n      return;\r\n    }\r\n    if(currentKey === newKey) {\r\n      this.advanceChart.update({\r\n        open: lastPoint.open,\r\n        high: Math.max(lastPoint.high, trade.price),\r\n        low: Math.min(lastPoint.low, trade.price),\r\n        close: trade.price,\r\n        volume: lastPoint.volume + trade.volume,\r\n        time: trade.time\r\n      }, true)\r\n    } else {\r\n      this.advanceChart.update({\r\n        open: trade.price,\r\n        high: trade.price,\r\n        low: trade.price,\r\n        close: trade.price,\r\n        volume: trade.volume,\r\n        time: trade.time\r\n      })\r\n    }\r\n  }\r\n\r\n  destroy() {\r\n    this._dataChanged.destroy();\r\n    this.advanceChart.chartApi\r\n      .timeScale()\r\n      .unsubscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange);\r\n\r\n    if(this._refeshTimer) {\r\n      clearInterval(this._refeshTimer);\r\n      this._refeshTimer = null;\r\n    }\r\n  }\r\n}\r\n"], "names": ["roundTime", "time", "period", "Period", "dayjs", "timeToD<PERSON><PERSON><PERSON>", "aggregate", "items", "high", "item", "low", "volume", "acc", "open", "close", "DataFeed", "advance<PERSON>hart", "dataFetch", "__publicField", "Delegate", "d", "logicalRange", "from", "interval", "data", "groupBy", "<PERSON><PERSON><PERSON>", "keyBy", "b", "newData", "mergeOhlcData", "lastTime", "timeToDate", "toTime", "toDate", "fromDate", "isSameRange", "range1", "range2", "newRange", "step", "to", "timeScale", "fromIndex", "dayjsToTime", "toIndex", "trade", "lastPoint", "current<PERSON><PERSON>", "new<PERSON>ey", "log"], "mappings": "kjBAuDgB,SAAAA,EAAUC,EAAYC,EAAgB,CACpD,OAAQA,EAAQ,CACd,KAAKC,EAAO,OAAA,OACZ,KAAKA,EAAO,OAAA,KACZ,KAAKA,EAAO,OAAA,IACH,OAAAF,EACT,KAAKE,EAAO,OAAA,KACH,OAAAC,EAAM,GAAGC,EAAAA,YAAYJ,CAAI,EAAG,KAAK,EAAE,QAAQ,MAAM,EAAE,KAAK,EACjE,KAAKE,EAAO,OAAA,MACH,OAAAC,EAAM,GAAGC,EAAAA,YAAYJ,CAAI,EAAG,KAAK,EAAE,QAAQ,OAAO,EAAE,KAAK,EAClE,QACE,MAAM,IAAI,MAAM,YAAYC,CAAM,cAAc,CAAA,CAEtD,CAEa,MAAAI,EAAaC,GAAyB,CAC3C,MAAAC,EAAO,KAAK,IAAI,GAAGD,EAAM,IAAIE,GAAQA,EAAK,IAAI,CAAC,EAC/CC,EAAM,KAAK,IAAI,GAAGH,EAAM,IAAIE,GAAQA,EAAK,GAAG,CAAC,EAC7CE,EAASJ,EAAM,OAAO,CAACK,EAAKH,IAASG,EAAMH,EAAK,OAAQ,CAAC,EACzDI,EAAON,EAAM,CAAC,EAAE,KAChBO,EAAQP,EAAMA,EAAM,OAAS,CAAC,EAAE,MAC/B,MAAA,CACL,KAAMA,EAAM,CAAC,EAAE,KACf,KAAAM,EACA,KAAAL,EACA,IAAAE,EACA,MAAAI,EACA,OAAAH,CACF,CACF,EAGO,MAAMI,CAAgC,CAY3C,YACSC,EACGC,EACV,CAdFC,EAAA,gBAAoB,IACpBA,EAAA,aAAuB,CAAC,GACxBA,EAAA,oBAAe,IAAIC,EAAAA,UACnBD,EAAA,gBAAqB,CAAE,OAAQf,EAAAA,OAAO,IAAK,MAAO,CAAE,GACpDe,EAAA,mBAAc,IACdA,EAAA,iBAAY,IACZA,EAAA,oBAAsC,MAEtCA,EAAA,kBAAa,IAIJ,KAAA,aAAAF,EACG,KAAA,UAAAC,EAEV,KAAK,4BACH,KAAK,4BAA4B,KAAK,IAAI,EAC5C,KAAK,aAAa,SACf,UACA,EAAA,mCAAmC,KAAK,2BAA2B,EAEnE,KAAK,UAAU,aACX,KAAA,aAAe,YAAY,SAAW,KAAK,aAAc,KAAK,UAAU,UAAU,EACzF,CAGF,IAAI,MAAQ,CACV,OAAO,KAAK,KAAA,CAGd,IAAI,KAAMG,EAAkB,CAC1B,KAAK,MAAQA,EAEb,KAAK,aAAa,KAAK,CAAA,CAGzB,aAAaC,EAA4B,CAGnC,GAFA,CAAC,KAAK,aACN,KAAK,KAAK,SAAW,GACrB,KAAK,UAAkB,MAAA,GACrB,KAAA,CAAE,KAAAC,GAASD,EACb,MAAA,EAAAC,EAAO,GACJ,CAKT,WAAY,CACV,MAAMC,EAAW,KAAK,SAEtB,GAAGA,EAAS,SAAWpB,EAAAA,OAAO,WAAY,KAAK,MAC/C,GAAGoB,EAAS,SAAWpB,EAAAA,OAAO,YAAa,KAAK,MAChD,GAAGoB,EAAS,SAAWpB,EAAAA,OAAO,cAAe,KAAK,MAE5C,MAAAqB,EAAOC,EAAAA,QAAQ,KAAK,SAAeC,EAAAA,QAAQjB,EAAK,KAAMc,CAAQ,CAAC,EAErE,OAAO,OAAO,QAAQC,CAAI,EACvB,IAAI,CAAC,CAACG,EAAOpB,CAAK,IAAM,CAAC,SAASoB,CAAK,EAAGrB,EAAUC,CAAK,CAAC,CAAU,EACpE,KAAK,CAAC,EAAGqB,IAAM,EAAE,CAAC,EAAIA,EAAE,CAAC,CAAC,EAAE,IAAYnB,GAAAA,EAAK,CAAC,CAAC,CAAA,CAGpD,eAAee,EAAqB,CAClC,MAAMK,EAAUC,EAAA,cAAcN,EAAM,KAAK,IAAI,EAC7C,GAAIK,EAAQ,SAAW,KAAK,KAAK,OAEjC,YAAK,KAAOA,EACZ,KAAK,aAAa,QAAQ,KAAK,UAAU,EAAG,KAAK,QAAQ,EAElDA,CAAA,CAGT,MAAM,YAAa,CAEjB,MAAME,EADW,KAAK,KAAK,KAAK,KAAK,OAAS,CAAC,EACrB,KACpBP,EAAO,MAAM,KAAK,UAAU,gBAAgB,CAChD,SAAU,KAAK,SACf,KAAMQ,aAAWD,CAAQ,EACzB,GAAQ,IAAA,IAAA,EACP,CAAE,QAAS,KAAK,QAAQ,KAAK,IAAI,EAAG,EAEpC,KAAK,YAER,KAAK,eAAeP,CAAI,CAAA,CAG1B,MAAM,WAAWH,EAA4B,CACrC,KAAA,CAAE,KAAAC,GAASD,EACXY,EAAS,KAAK,KAAK,CAAC,EAAE,KACtBC,EAAS7B,cAAY4B,CAAM,EAC3BE,EAAW,KAAK,QAAQF,EAAQX,EAAO,GAAG,EAE1CE,EAAO,MAAM,KAAK,UAAU,oBAAoB,CACpD,SAAU,KAAK,SACf,KAAMW,EAAS,OAAO,EACtB,GAAID,EAAO,OAAO,CAAA,EACjB,CAAE,QAAS,KAAK,QAAQ,KAAK,IAAI,EAAG,EAEpC,KAAK,YAEJ,KAAK,eAAeV,CAAI,IAC1B,KAAK,UAAY,GACnB,CAGF,MAAM,4BAA4BH,EAAmC,CACnE,GAAG,CAACA,EAAc,OACZ,MAAAe,EAAc,CAACC,EAAsBC,IAAyBD,EAAO,OAASC,EAAO,MAAQD,EAAO,KAAOC,EAAO,GACrH,GAAA,MAAK,aAAa,QACrB,MAAK,aAAa,QAAU,GACxB,GAAA,CACI,KAAA,KAAK,aAAajB,CAAY,GAAG,CAC/B,MAAA,KAAK,WAAWA,CAAY,EAClC,MAAMkB,EAAW,KAAK,aAAa,SAAS,YAAY,uBAAuB,EAE5E,GADA,CAACA,GACDH,EAAYG,EAAUlB,CAAY,EAAG,MACzBA,EAAAkB,CAAA,CACjB,QACA,CACA,KAAK,aAAa,QAAU,EAAA,EAC9B,CAGF,QAAQtC,EAAYuC,EAAc,CACzBA,EAAA,KAAK,MAAMA,CAAI,EAChB,MAAAtC,EAAS,KAAK,SAAS,OAC7B,OAAQA,EAAQ,CACd,KAAKC,EAAO,OAAA,OACV,OAAOE,EAAY,YAAAJ,CAAI,EAAE,IAAIuC,EAAM,QAAQ,EAC7C,KAAKrC,EAAO,OAAA,KACV,OAAOE,EAAY,YAAAJ,CAAI,EAAE,IAAIuC,EAAM,MAAM,EAC3C,KAAKrC,EAAO,OAAA,IACV,OAAOE,EAAY,YAAAJ,CAAI,EAAE,IAAIuC,EAAM,KAAK,EAC1C,KAAKrC,EAAO,OAAA,KACV,OAAOE,EAAY,YAAAJ,CAAI,EAAE,IAAIuC,EAAM,MAAM,EAC3C,KAAKrC,EAAO,OAAA,MACV,OAAOE,EAAY,YAAAJ,CAAI,EAAE,IAAIuC,EAAM,OAAO,EAC5C,QACE,MAAM,IAAI,MAAM,YAAYtC,CAAM,cAAc,CAAA,CACpD,CAGF,MAAM,SAAS,CAAE,KAAAoB,EAAM,GAAAmB,EAAI,SAAAlB,GAA6B,CACtD,KAAK,WAAW,EAEhB,KAAK,aAAa,QAAU,GAC5B,KAAK,SAAWA,EAChB,MAAMC,EAAO,MAAM,KAAK,UAAU,iBAAiB,CAAE,KAAAF,EAAM,GAAAmB,EAAI,SAAAlB,CAAS,EAAG,CAAE,QAAS,KAAK,QAAQ,KAAK,IAAI,EAAG,EAI/G,GAHA,KAAK,KAAOC,EACZ,KAAK,aAAa,QAAU,GAEzB,KAAK,WAAY,OAEpB,KAAK,aAAa,QAAQ,KAAK,UAAA,EAAaD,CAAQ,EACpD,KAAK,YAAc,GAEnB,MAAMmB,EAAY,KAAK,aAAa,SAAS,UAAU,EAEjDC,EAAYD,EAAU,YAAYE,EAAAA,YAAYxC,EAAMkB,CAAI,CAAC,EAAG,EAAI,EAChEuB,EAAUH,EAAU,YAAYE,EAAAA,YAAYxC,EAAMqC,CAAE,CAAC,EAAG,EAAI,EAE/DE,IAAc,QAAaE,IAAY,QACxC,KAAK,aAAa,SAAS,CAAE,KAAMF,EAAiC,GAAIE,EAA+B,EAGzG,MAAM,KAAK,4BACTH,EAAU,uBAAuB,CACnC,CAAA,CAGM,YAAa,CACnB,KAAK,aAAa,QAAU,GAC5B,KAAK,YAAc,GACnB,KAAK,UAAY,EAAA,CAGnB,aAAc,CACZ,OAAO,KAAK,YAAA,CAGd,MAAMI,EAAsD,CAC1D,KAAM,CAACC,CAAS,EAAI,KAAK,aAAa,UAAU,EAE1CC,EAAatB,EAAAA,QAAQqB,EAAU,KAAM,KAAK,QAAQ,EAClDE,EAASvB,EAAAA,QAAQoB,EAAM,KAAM,KAAK,QAAQ,EAChD,GAAGG,EAASD,EAAY,CACtBE,EAAA,IAAI,KAAK,mBAAmBD,CAAM,0BAA0BD,CAAU,EAAE,EACxE,MAAA,CAECA,IAAeC,EAChB,KAAK,aAAa,OAAO,CACvB,KAAMF,EAAU,KAChB,KAAM,KAAK,IAAIA,EAAU,KAAMD,EAAM,KAAK,EAC1C,IAAK,KAAK,IAAIC,EAAU,IAAKD,EAAM,KAAK,EACxC,MAAOA,EAAM,MACb,OAAQC,EAAU,OAASD,EAAM,OACjC,KAAMA,EAAM,MACX,EAAI,EAEP,KAAK,aAAa,OAAO,CACvB,KAAMA,EAAM,MACZ,KAAMA,EAAM,MACZ,IAAKA,EAAM,MACX,MAAOA,EAAM,MACb,OAAQA,EAAM,OACd,KAAMA,EAAM,IAAA,CACb,CACH,CAGF,SAAU,CACR,KAAK,aAAa,QAAQ,EAC1B,KAAK,aAAa,SACf,UACA,EAAA,qCAAqC,KAAK,2BAA2B,EAErE,KAAK,eACN,cAAc,KAAK,YAAY,EAC/B,KAAK,aAAe,KACtB,CAEJ"}