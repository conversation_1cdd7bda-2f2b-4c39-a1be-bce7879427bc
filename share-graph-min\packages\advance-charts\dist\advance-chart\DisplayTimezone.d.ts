import { Delegate } from '../helpers/delegate';
import { IAdvanceChart } from './i-advance-chart';
import { TickMarkType } from 'lightweight-charts';

export declare class DisplayTimezone {
    private _source;
    _timezoneChanged: Delegate<string, void, void>;
    _timezone: string;
    _browserTimezone: string;
    constructor(_source: IAdvanceChart);
    formatDateTime(date: Date): string;
    formatDate(date: Date): string;
    convertDateToTimezoneDate(date: Date): Date;
    format(date: Date): string;
    tickMarkFormatter(date: Date, tickMarkType: TickMarkType): string;
}
