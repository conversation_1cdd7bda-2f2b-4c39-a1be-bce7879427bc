import { CanvasRenderingTarget2D } from 'fancy-canvas';
import { PrimitivePaneViewBase } from '../primitive-base';
import { Coordinate } from 'lightweight-charts';

export interface RegionPrimitivePaneViewOptions {
    backgroundColor: string;
    lineWidth: number;
    lineColor: string;
    lineStyle: number[];
}
export declare const RegionPrimitiveOptionsDefault: RegionPrimitivePaneViewOptions;
interface RegionPrimitiveData {
    x: Coordinate;
    points: Coordinate[];
}
export declare class RegionPrimitivePaneView extends PrimitivePaneViewBase<RegionPrimitivePaneViewOptions, RegionPrimitiveData> {
    drawBackground(target: CanvasRenderingTarget2D): void;
    defaultOptions(): RegionPrimitivePaneViewOptions;
}
export {};
