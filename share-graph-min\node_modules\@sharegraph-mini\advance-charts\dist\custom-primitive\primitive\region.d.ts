import { Coordinate, DeepPartial, IPriceLine, ISeriesPrimitive, Time } from 'lightweight-charts';
import { PrimitivePaneViewBase, SeriesPrimitiveBase } from '../primitive-base';
import { LinePrimitivePaneView } from '../pane-view/line';
import { BitmapCoordinatesRenderingScope } from 'fancy-canvas';

export interface PrimitivePaneViewBaseOptions {
    backgroundColor: string;
    upPrice: number;
    lowPrice: number;
}
export interface RegionPrimitiveOptions extends PrimitivePaneViewBaseOptions {
    lineWidth: number;
    lineColor: string;
}
export declare const RegionPrimitiveOptionsDefault: RegionPrimitiveOptions;
export interface RegionPaneViewData {
    top: Coordinate;
    bottom: Coordinate;
}
export declare class RegionPaneView extends PrimitivePaneViewBase<PrimitivePaneViewBaseOptions> {
    _drawBackgroundImpl(renderingScope: BitmapCoordinatesRenderingScope): void;
    defaultOptions(): PrimitivePaneViewBaseOptions;
}
export declare class RegionPrimitive extends SeriesPrimitiveBase implements ISeriesPrimitive<Time> {
    bandPaneView: RegionPaneView;
    upLinePaneView: LinePrimitivePaneView;
    lowLinePaneView: LinePrimitivePaneView;
    _options: RegionPrimitiveOptions;
    upPriceLine: IPriceLine | null;
    lowPriceLine: IPriceLine | null;
    constructor(options: DeepPartial<RegionPrimitiveOptions>);
    _updateAllViews(): void;
}
