{"name": "@sharegraph-mini/advance-charts", "version": "0.0.0", "type": "module", "exports": {".": {"require": "./dist/advance-charts.cjs.js", "import": "./dist/advance-charts.es.js"}}, "module": "./dist/advance-charts.es.js", "types": "./dist/advance-charts.d.ts", "scripts": {"dev": "vite --port 4000", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest", "lint": "eslint ."}, "dependencies": {"lightweight-charts": "^5.0.5", "es-toolkit": "^1.30.1", "dayjs": "^1.11.13", "fancy-canvas": "^2.1.0", "technicalindicators": "^3.1.0"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^3.8.1", "vitest": "^2.1.8", "eslint": "^9.15.0", "globals": "^15.12.0", "@eslint/js": "^9.15.0", "typescript-eslint": "^8.15.0"}}