{"watch": ["app/src/**/*", "packages/*/src/**/*", "app/index.html", "app/vite.config.ts", "packages/*/vite.config.ts", "packages/*/tsconfig.json", "app/tsconfig.json", "turbo.json"], "ext": "ts,tsx,js,jsx,json,html,css,scss,sass", "ignore": ["node_modules/**/*", "*/node_modules/**/*", "dist/**/*", "*/dist/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", ".turbo/**/*"], "exec": "turbo run dev", "env": {"NODE_ENV": "development"}, "delay": 1500, "verbose": true, "restartable": "rs", "colours": true}