"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const a=require("es-toolkit");function i(r){const e=document.createElement("div");e.style.display="none",document.body.appendChild(e),e.style.color=r;const t=window.getComputedStyle(e).color;return document.body.removeChild(e),t}const n=a.memoize(r=>{const t=i(r).match(/^rgba?\s*\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)$/);if(!t)throw new Error(`Failed to parse color: ${r}`);return[parseInt(t[1],10),parseInt(t[2],10),parseInt(t[3],10),t[4]?parseFloat(t[4]):1]});class d{static applyAlpha(e,t){if(e==="transparent"||t===1)return e;const o=n(e),s=o[3];return`rgba(${o[0]}, ${o[1]}, ${o[2]}, ${t*s})`}static parseColor(e){return n(e)}}exports.Color=d;exports.parseColor=n;
//# sourceMappingURL=color.cjs.js.map
