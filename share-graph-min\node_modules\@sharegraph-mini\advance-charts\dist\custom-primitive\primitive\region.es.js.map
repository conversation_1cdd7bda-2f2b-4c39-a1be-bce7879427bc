{"version": 3, "file": "region.es.js", "sources": ["../../../src/custom-primitive/primitive/region.ts"], "sourcesContent": ["import {Coordinate, DeepPartial, IPriceLine, ISeriesPrimitive, LineStyle, Time} from \"lightweight-charts\";\r\nimport {PrimitivePaneViewBase, SeriesPrimitiveBase} from \"../primitive-base\";\r\nimport {LinePrimitivePaneView, LinePrimitiveOptionsDefault} from \"../pane-view/line\";\r\nimport {cloneDeep, merge} from \"es-toolkit\";\r\nimport {BitmapCoordinatesRenderingScope} from \"fancy-canvas\";\r\nimport {ensureNotNull} from \"../../helpers/assertions\";\r\n\r\nexport interface PrimitivePaneViewBaseOptions {\r\n  backgroundColor: string,\r\n  upPrice: number,\r\n  lowPrice: number,\r\n}\r\n\r\nexport interface RegionPrimitiveOptions extends PrimitivePaneViewBaseOptions {\r\n  lineWidth: number, \r\n  lineColor: string;\r\n}\r\n\r\nexport const RegionPrimitiveOptionsDefault: RegionPrimitiveOptions = {\r\n  backgroundColor: '#2196f31a',\r\n  ...LinePrimitiveOptionsDefault,\r\n  upPrice: 0,\r\n  lowPrice: 0,\r\n}\r\n\r\nexport interface RegionPaneViewData {\r\n  top: Coordinate, \r\n  bottom: Coordinate\r\n}\r\nexport class RegionPaneView extends PrimitivePaneViewBase<PrimitivePaneViewBaseOptions> {\r\n  _drawBackgroundImpl(renderingScope: BitmapCoordinatesRenderingScope): void {\r\n    const ctx = renderingScope.context;\r\n    ctx.scale(renderingScope.horizontalPixelRatio, renderingScope.verticalPixelRatio)\r\n    const width = ctx.canvas.width\r\n    const region = new Path2D;\r\n    const upCoor = ensureNotNull(this.priceToCoordinate(this.options.upPrice))\r\n    const lowCoor = ensureNotNull(this.priceToCoordinate(this.options.lowPrice))\r\n    region.moveTo(0, upCoor)\r\n    region.lineTo(width, upCoor)\r\n    region.lineTo(width, lowCoor)\r\n    region.lineTo(0, lowCoor)\r\n    region.lineTo(0, upCoor)\r\n    region.closePath()\r\n    ctx.beginPath();\r\n    ctx.fillStyle = this.options.backgroundColor\r\n    ctx.fill(region)\r\n  }\r\n\r\n  defaultOptions(): PrimitivePaneViewBaseOptions {\r\n    return { \r\n      backgroundColor: '#2196f31a',\r\n      upPrice: 0,\r\n      lowPrice: 0\r\n    }\r\n  }\r\n}\r\n\r\nexport class RegionPrimitive extends SeriesPrimitiveBase implements ISeriesPrimitive<Time> {\r\n  bandPaneView: RegionPaneView\r\n  upLinePaneView: LinePrimitivePaneView\r\n  lowLinePaneView: LinePrimitivePaneView\r\n  _options: RegionPrimitiveOptions\r\n\r\n  upPriceLine: IPriceLine | null = null\r\n  lowPriceLine: IPriceLine | null = null\r\n\r\n  constructor(options: DeepPartial<RegionPrimitiveOptions>) {\r\n    super();\r\n    this._options = merge(cloneDeep(RegionPrimitiveOptionsDefault), options)\r\n\r\n    this.bandPaneView = new RegionPaneView(this._options)\r\n    this.upLinePaneView = new LinePrimitivePaneView({...this._options, lineDash: LineStyle.LargeDashed})\r\n    this.lowLinePaneView = new LinePrimitivePaneView({...this._options, lineDash: LineStyle.LargeDashed})\r\n    this._paneViews = [this.upLinePaneView, this.bandPaneView, this.lowLinePaneView]\r\n  }\r\n\r\n  _updateAllViews(): void {\r\n    const width = this.chart.timeScale().width();\r\n    const { upPrice, lowPrice } = this._options;\r\n\r\n    this.upLinePaneView.update([\r\n      {\r\n        x: 0 as Coordinate,\r\n        price: upPrice,\r\n      },\r\n      {\r\n        x: width as Coordinate,\r\n        price: upPrice,\r\n      }\r\n    ])\r\n\r\n    this.lowLinePaneView.update([\r\n      {\r\n        x: 0 as Coordinate,\r\n        price: lowPrice,\r\n      },\r\n      {\r\n        x: width as Coordinate,\r\n        price: lowPrice,\r\n      }\r\n    ])\r\n  }\r\n}"], "names": ["RegionPrimitiveOptionsDefault", "LinePrimitiveOptionsDefault", "RegionPaneView", "PrimitivePaneViewBase", "renderingScope", "ctx", "width", "region", "upCoor", "ensureNotNull", "lowCoor", "RegionPrimitive", "SeriesPrimitiveBase", "options", "__publicField", "merge", "cloneDeep", "LinePrimitivePaneView", "LineStyle", "upPrice", "lowPrice"], "mappings": ";;;;;;;;AAkBO,MAAMA,IAAwD;AAAA,EACnE,iBAAiB;AAAA,EACjB,GAAGC;AAAA,EACH,SAAS;AAAA,EACT,UAAU;AACZ;AAMO,MAAMC,UAAuBC,EAAoD;AAAA,EACtF,oBAAoBC,GAAuD;AACzE,UAAMC,IAAMD,EAAe;AAC3B,IAAAC,EAAI,MAAMD,EAAe,sBAAsBA,EAAe,kBAAkB;AAC1E,UAAAE,IAAQD,EAAI,OAAO,OACnBE,IAAS,IAAI,OAAA,GACbC,IAASC,EAAc,KAAK,kBAAkB,KAAK,QAAQ,OAAO,CAAC,GACnEC,IAAUD,EAAc,KAAK,kBAAkB,KAAK,QAAQ,QAAQ,CAAC;AACpE,IAAAF,EAAA,OAAO,GAAGC,CAAM,GAChBD,EAAA,OAAOD,GAAOE,CAAM,GACpBD,EAAA,OAAOD,GAAOI,CAAO,GACrBH,EAAA,OAAO,GAAGG,CAAO,GACjBH,EAAA,OAAO,GAAGC,CAAM,GACvBD,EAAO,UAAU,GACjBF,EAAI,UAAU,GACVA,EAAA,YAAY,KAAK,QAAQ,iBAC7BA,EAAI,KAAKE,CAAM;AAAA,EAAA;AAAA,EAGjB,iBAA+C;AACtC,WAAA;AAAA,MACL,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,EAAA;AAEJ;AAEO,MAAMI,UAAwBC,EAAsD;AAAA,EASzF,YAAYC,GAA8C;AAClD,UAAA;AATR,IAAAC,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA;AAEA,IAAAA,EAAA,qBAAiC;AACjC,IAAAA,EAAA,sBAAkC;AAIhC,SAAK,WAAWC,EAAMC,EAAUhB,CAA6B,GAAGa,CAAO,GAEvE,KAAK,eAAe,IAAIX,EAAe,KAAK,QAAQ,GAC/C,KAAA,iBAAiB,IAAIe,EAAsB,EAAC,GAAG,KAAK,UAAU,UAAUC,EAAU,aAAY,GAC9F,KAAA,kBAAkB,IAAID,EAAsB,EAAC,GAAG,KAAK,UAAU,UAAUC,EAAU,aAAY,GACpG,KAAK,aAAa,CAAC,KAAK,gBAAgB,KAAK,cAAc,KAAK,eAAe;AAAA,EAAA;AAAA,EAGjF,kBAAwB;AACtB,UAAMZ,IAAQ,KAAK,MAAM,UAAA,EAAY,MAAM,GACrC,EAAE,SAAAa,GAAS,UAAAC,EAAS,IAAI,KAAK;AAEnC,SAAK,eAAe,OAAO;AAAA,MACzB;AAAA,QACE,GAAG;AAAA,QACH,OAAOD;AAAA,MACT;AAAA,MACA;AAAA,QACE,GAAGb;AAAA,QACH,OAAOa;AAAA,MAAA;AAAA,IACT,CACD,GAED,KAAK,gBAAgB,OAAO;AAAA,MAC1B;AAAA,QACE,GAAG;AAAA,QACH,OAAOC;AAAA,MACT;AAAA,MACA;AAAA,QACE,GAAGd;AAAA,QACH,OAAOc;AAAA,MAAA;AAAA,IACT,CACD;AAAA,EAAA;AAEL;"}