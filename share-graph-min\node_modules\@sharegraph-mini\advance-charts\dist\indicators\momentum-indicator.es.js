var c = Object.defineProperty;
var l = (r, t, e) => t in r ? c(r, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : r[t] = e;
var m = (r, t, e) => l(r, typeof t != "symbol" ? t + "" : t, e);
import { LineSeries as u } from "lightweight-charts";
import { ChartIndicator as p } from "./abstract-indicator.es.js";
const d = {
  period: 14,
  usePercentage: !1,
  color: "#2b97f1",
  zeroLineColor: "#808080",
  overlay: !1
};
class v extends p {
  constructor(e, s, o) {
    super(e, s);
    m(this, "momentumSeries");
    this.momentumSeries = e.addSeries(u, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "momentum"
    }, o);
  }
  applyIndicatorData() {
    const e = [];
    for (const s of this._executionContext.data) {
      const o = s.value, n = s.time;
      if (!o) continue;
      const [i] = o;
      isNaN(i) || e.push({ time: n, value: i });
    }
    this.momentumSeries.setData(e);
  }
  formula(e) {
    const s = this.options.period, o = e.symbol.close, n = e.new_var(o, s + 1);
    if (!n.calculable()) return;
    const i = n.get(s);
    let a;
    return this.options.usePercentage ? a = (o - i) / i * 100 : a = o - i, [a];
  }
  _applyOptions(e) {
    (e.period || e.usePercentage) && this.calcIndicatorData(), e.color && this.momentumSeries.applyOptions({ color: e.color }), this.applyIndicatorData();
  }
  getDefaultOptions() {
    return d;
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.momentumSeries);
  }
  setPaneIndex(e) {
    this.momentumSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.momentumSeries.getPane().paneIndex();
  }
}
export {
  v as default,
  d as defaultOptions
};
//# sourceMappingURL=momentum-indicator.es.js.map
