{"version": 3, "file": "primitive-base.es.js", "sources": ["../../src/custom-primitive/primitive-base.ts"], "sourcesContent": ["import {\r\n  DataChangedScope,\r\n  DeepPartial,\r\n  IChartApi,\r\n  IPrimitivePaneRenderer,\r\n  IPrimitivePaneView,\r\n  ISeriesApi,\r\n  ISeriesPrimitive,\r\n  SeriesAttachedParameter,\r\n  SeriesType,\r\n  SingleValueData,\r\n  Time,\r\n  LogicalRange,\r\n  Logical,\r\n  WhitespaceData,\r\n  ITimeScaleApi,\r\n} from \"lightweight-charts\";\r\nimport { ensureDefined, ensureNotNull } from \"../helpers/assertions\";\r\nimport {\r\n  BitmapCoordinatesRenderingScope,\r\n  CanvasRenderingTarget2D,\r\n} from \"fancy-canvas\";\r\nimport { cloneDeep, merge } from \"es-toolkit\";\r\nimport { binarySearch, timeToUnix } from \"../helpers/utils\";\r\nimport { OHLCVData } from \"../interface\";\r\n\r\nexport abstract class SeriesPrimitiveBase<\r\n  TData extends WhitespaceData = WhitespaceData,\r\n> implements ISeriesPrimitive<Time>\r\n{\r\n  private _chart: IChartApi | undefined = undefined;\r\n  private _series: ISeriesApi<SeriesType> | undefined = undefined;\r\n  protected _paneViews: IPrimitivePaneViewApi[] = [];\r\n  indicatorData: TData[] = [];\r\n  protected _isDetached = false;\r\n\r\n  updateAllViews(): void {\r\n    if (this._isDetached) return;\r\n    this._updateAllViews?.();\r\n  }\r\n\r\n  _updateAllViews?() {}\r\n\r\n  paneViews(): readonly IPrimitivePaneView[] {\r\n    return this._paneViews;\r\n  }\r\n\r\n  protected dataUpdated?(scope: DataChangedScope): void;\r\n  protected requestUpdate(): void {\r\n    if (this._requestUpdate) this._requestUpdate();\r\n  }\r\n  private _requestUpdate?: () => void;\r\n\r\n  public attached({\r\n    chart,\r\n    series,\r\n    requestUpdate,\r\n  }: SeriesAttachedParameter<Time>) {\r\n    this._chart = chart;\r\n    this._series = series;\r\n    this._series.subscribeDataChanged(this._fireDataUpdated);\r\n    this._requestUpdate = requestUpdate;\r\n    this.requestUpdate();\r\n    this._paneViews.map((item) => item.attached(series, chart));\r\n\r\n    this._attached?.();\r\n  }\r\n\r\n  _attached?(): void;\r\n\r\n  get data() {\r\n    const data = this.series.data();\r\n    if (data.length > 0) {\r\n      ensureDefined(data[0].customValues);\r\n    } else {\r\n      return [];\r\n    }\r\n    return data.map((item) => item.customValues as unknown as OHLCVData);\r\n  }\r\n\r\n  public detached() {\r\n    this._series?.unsubscribeDataChanged(this._fireDataUpdated);\r\n    this._chart = undefined;\r\n    this._series = undefined;\r\n    this._paneViews = [];\r\n    this._requestUpdate = undefined;\r\n    this._isDetached = true;\r\n  }\r\n\r\n  public get chart(): IChartApi {\r\n    return ensureDefined(this._chart);\r\n  }\r\n\r\n  public get series(): ISeriesApi<SeriesType> {\r\n    return ensureDefined(this._series);\r\n  }\r\n\r\n  // This method is a class property to maintain the\r\n  // lexical 'this' scope (due to the use of the arrow function)\r\n  // and to ensure its reference stays the same, so we can unsubscribe later.\r\n  private _fireDataUpdated = (scope: DataChangedScope) => {\r\n    if (this.dataUpdated) {\r\n      this.dataUpdated(scope);\r\n    }\r\n  };\r\n\r\n  dataByTime(time: Time): TData | undefined {\r\n    return binarySearch(this.indicatorData, timeToUnix(time), (item) =>\r\n      timeToUnix(item.time)\r\n    );\r\n  }\r\n\r\n  lastPoint(): TData | undefined {\r\n    return this.indicatorData.at(-1);\r\n  }\r\n}\r\n\r\nexport interface IPrimitivePaneViewApi\r\n  extends IPrimitivePaneView,\r\n    IPrimitivePaneRenderer {\r\n  attached(series: ISeriesApi<SeriesType>, chartApi: IChartApi): void;\r\n}\r\n\r\nexport abstract class PrimitivePaneViewBase<\r\n  TOptions extends object = object,\r\n  TData = SingleValueData,\r\n> implements IPrimitivePaneViewApi\r\n{\r\n  options: TOptions;\r\n\r\n  _series: ISeriesApi<SeriesType> | null = null;\r\n  _chartApi: IChartApi | null = null;\r\n  _data: TData[] | null = null;\r\n\r\n  constructor(options?: DeepPartial<TOptions>) {\r\n    this.options = merge(cloneDeep(this.defaultOptions()), options ?? {});\r\n  }\r\n\r\n  renderer(): IPrimitivePaneRenderer | null {\r\n    return this;\r\n  }\r\n\r\n  draw(target: CanvasRenderingTarget2D) {\r\n    if (!this._chartApi) return;\r\n    if (!this._series) return;\r\n\r\n    target.useBitmapCoordinateSpace((scope) => this._drawImpl?.(scope));\r\n  }\r\n\r\n  drawBackground(target: CanvasRenderingTarget2D) {\r\n    if (!this._chartApi) return;\r\n    if (!this._series) return;\r\n\r\n    target.useBitmapCoordinateSpace((scope) =>\r\n      this._drawBackgroundImpl?.(scope)\r\n    );\r\n  }\r\n\r\n  get data() {\r\n    return ensureNotNull(this._data);\r\n  }\r\n\r\n  get series() {\r\n    return ensureNotNull(this._series);\r\n  }\r\n\r\n  get chartApi() {\r\n    return ensureNotNull(this._chartApi);\r\n  }\r\n\r\n  _timeScale: ITimeScaleApi<Time> | undefined;\r\n  get timeScale() {\r\n    if (!this._timeScale) this._timeScale = this.chartApi.timeScale();\r\n    return this._timeScale;\r\n  }\r\n\r\n  getVisibleLogicalRange() {\r\n    const range = this.timeScale.getVisibleLogicalRange();\r\n    if (!range) return;\r\n    return {\r\n      from: Math.floor(range.from) as Logical,\r\n      to: Math.ceil(range.to) as Logical,\r\n    } satisfies LogicalRange;\r\n  }\r\n\r\n  getVisibleRange() {\r\n    return this.timeScale.getVisibleRange();\r\n  }\r\n\r\n  coordinateToPrice(coordinate: number) {\r\n    return this.series.coordinateToPrice(coordinate);\r\n  }\r\n  priceToCoordinate(price: number) {\r\n    return this.series.priceToCoordinate(price);\r\n  }\r\n\r\n  timeToCoordinate(time: Time) {\r\n    return this.timeScale.timeToCoordinate(time);\r\n  }\r\n\r\n  attached(series: ISeriesApi<SeriesType>, chartApi: IChartApi) {\r\n    this._series = series;\r\n    this._chartApi = chartApi;\r\n  }\r\n\r\n  update(data: TData[]) {\r\n    this._data = data;\r\n    this._update?.();\r\n  }\r\n\r\n  _update?(): void;\r\n  _drawImpl?(renderingScope: BitmapCoordinatesRenderingScope): void;\r\n  _drawBackgroundImpl?(renderingScope: BitmapCoordinatesRenderingScope): void;\r\n\r\n  abstract defaultOptions(): TOptions;\r\n}\r\n"], "names": ["SeriesPrimitiveBase", "__publicField", "scope", "_a", "chart", "series", "requestUpdate", "item", "data", "ensureDefined", "time", "binarySearch", "timeToUnix", "PrimitivePaneViewBase", "options", "merge", "cloneDeep", "target", "ensureNotNull", "range", "coordinate", "price", "chartApi"], "mappings": ";;;;;;AA0BO,MAAeA,EAGtB;AAAA,EAHO;AAIG,IAAAC,EAAA;AACA,IAAAA,EAAA;AACE,IAAAA,EAAA,oBAAsC,CAAC;AACjD,IAAAA,EAAA,uBAAyB,CAAC;AAChB,IAAAA,EAAA,qBAAc;AAiBhB,IAAAA,EAAA;AAiDA;AAAA;AAAA;AAAA,IAAAA,EAAA,0BAAmB,CAACC,MAA4B;AACtD,MAAI,KAAK,eACP,KAAK,YAAYA,CAAK;AAAA,IAE1B;AAAA;AAAA,EApEA,iBAAuB;;AACrB,IAAI,KAAK,gBACTC,IAAA,KAAK,oBAAL,QAAAA,EAAA;AAAA,EAAuB;AAAA,EAGzB,kBAAmB;AAAA,EAAA;AAAA,EAEnB,YAA2C;AACzC,WAAO,KAAK;AAAA,EAAA;AAAA,EAIJ,gBAAsB;AAC1B,IAAA,KAAK,kBAAgB,KAAK,eAAe;AAAA,EAAA;AAAA,EAIxC,SAAS;AAAA,IACd,OAAAC;AAAA,IACA,QAAAC;AAAA,IACA,eAAAC;AAAA,EAAA,GACgC;;AAChC,SAAK,SAASF,GACd,KAAK,UAAUC,GACV,KAAA,QAAQ,qBAAqB,KAAK,gBAAgB,GACvD,KAAK,iBAAiBC,GACtB,KAAK,cAAc,GACd,KAAA,WAAW,IAAI,CAACC,MAASA,EAAK,SAASF,GAAQD,CAAK,CAAC,IAE1DD,IAAA,KAAK,cAAL,QAAAA,EAAA;AAAA,EAAiB;AAAA,EAKnB,IAAI,OAAO;AACH,UAAAK,IAAO,KAAK,OAAO,KAAK;AAC1B,QAAAA,EAAK,SAAS;AACF,MAAAC,EAAAD,EAAK,CAAC,EAAE,YAAY;AAAA;AAElC,aAAO,CAAC;AAEV,WAAOA,EAAK,IAAI,CAACD,MAASA,EAAK,YAAoC;AAAA,EAAA;AAAA,EAG9D,WAAW;;AACX,KAAAJ,IAAA,KAAA,YAAA,QAAAA,EAAS,uBAAuB,KAAK,mBAC1C,KAAK,SAAS,QACd,KAAK,UAAU,QACf,KAAK,aAAa,CAAC,GACnB,KAAK,iBAAiB,QACtB,KAAK,cAAc;AAAA,EAAA;AAAA,EAGrB,IAAW,QAAmB;AACrB,WAAAM,EAAc,KAAK,MAAM;AAAA,EAAA;AAAA,EAGlC,IAAW,SAAiC;AACnC,WAAAA,EAAc,KAAK,OAAO;AAAA,EAAA;AAAA,EAYnC,WAAWC,GAA+B;AACjC,WAAAC;AAAA,MAAa,KAAK;AAAA,MAAeC,EAAWF,CAAI;AAAA,MAAG,CAACH,MACzDK,EAAWL,EAAK,IAAI;AAAA,IACtB;AAAA,EAAA;AAAA,EAGF,YAA+B;AACtB,WAAA,KAAK,cAAc,GAAG,EAAE;AAAA,EAAA;AAEnC;AAQO,MAAeM,EAItB;AAAA,EAOE,YAAYC,GAAiC;AAN7C,IAAAb,EAAA;AAEA,IAAAA,EAAA,iBAAyC;AACzC,IAAAA,EAAA,mBAA8B;AAC9B,IAAAA,EAAA,eAAwB;AAsCxB,IAAAA,EAAA;AAnCO,SAAA,UAAUc,EAAMC,EAAU,KAAK,eAAgB,CAAA,GAAGF,KAAW,EAAE;AAAA,EAAA;AAAA,EAGtE,WAA0C;AACjC,WAAA;AAAA,EAAA;AAAA,EAGT,KAAKG,GAAiC;AAChC,IAAC,KAAK,aACL,KAAK,WAEVA,EAAO,yBAAyB,CAACf,MAAU;;AAAA,cAAAC,IAAA,KAAK,cAAL,gBAAAA,EAAA,WAAiBD;AAAA,KAAM;AAAA,EAAA;AAAA,EAGpE,eAAee,GAAiC;AAC1C,IAAC,KAAK,aACL,KAAK,WAEHA,EAAA;AAAA,MAAyB,CAACf,MAAA;;AAC/B,gBAAAC,IAAA,KAAK,wBAAL,gBAAAA,EAAA,WAA2BD;AAAA;AAAA,IAC7B;AAAA,EAAA;AAAA,EAGF,IAAI,OAAO;AACF,WAAAgB,EAAc,KAAK,KAAK;AAAA,EAAA;AAAA,EAGjC,IAAI,SAAS;AACJ,WAAAA,EAAc,KAAK,OAAO;AAAA,EAAA;AAAA,EAGnC,IAAI,WAAW;AACN,WAAAA,EAAc,KAAK,SAAS;AAAA,EAAA;AAAA,EAIrC,IAAI,YAAY;AACd,WAAK,KAAK,oBAAiB,aAAa,KAAK,SAAS,UAAU,IACzD,KAAK;AAAA,EAAA;AAAA,EAGd,yBAAyB;AACjB,UAAAC,IAAQ,KAAK,UAAU,uBAAuB;AACpD,QAAKA;AACE,aAAA;AAAA,QACL,MAAM,KAAK,MAAMA,EAAM,IAAI;AAAA,QAC3B,IAAI,KAAK,KAAKA,EAAM,EAAE;AAAA,MACxB;AAAA,EAAA;AAAA,EAGF,kBAAkB;AACT,WAAA,KAAK,UAAU,gBAAgB;AAAA,EAAA;AAAA,EAGxC,kBAAkBC,GAAoB;AAC7B,WAAA,KAAK,OAAO,kBAAkBA,CAAU;AAAA,EAAA;AAAA,EAEjD,kBAAkBC,GAAe;AACxB,WAAA,KAAK,OAAO,kBAAkBA,CAAK;AAAA,EAAA;AAAA,EAG5C,iBAAiBX,GAAY;AACpB,WAAA,KAAK,UAAU,iBAAiBA,CAAI;AAAA,EAAA;AAAA,EAG7C,SAASL,GAAgCiB,GAAqB;AAC5D,SAAK,UAAUjB,GACf,KAAK,YAAYiB;AAAA,EAAA;AAAA,EAGnB,OAAOd,GAAe;;AACpB,SAAK,QAAQA,IACbL,IAAA,KAAK,YAAL,QAAAA,EAAA;AAAA,EAAe;AAQnB;"}