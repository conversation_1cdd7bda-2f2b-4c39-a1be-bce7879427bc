{"version": 3, "sources": ["../../../../../node_modules/technicalindicators/lib/Utils/LinkedList.js", "../../../../../node_modules/technicalindicators/lib/Utils/FixedSizeLinkedList.js", "../../../../../node_modules/technicalindicators/lib/StockData.js", "../../../../../node_modules/technicalindicators/lib/config.js", "../../../../../node_modules/technicalindicators/lib/Utils/NumberFormatter.js", "../../../../../node_modules/technicalindicators/lib/indicator/indicator.js", "../../../../../node_modules/technicalindicators/lib/moving_averages/SMA.js", "../../../../../node_modules/technicalindicators/lib/moving_averages/EMA.js", "../../../../../node_modules/technicalindicators/lib/moving_averages/WMA.js", "../../../../../node_modules/technicalindicators/lib/moving_averages/WEMA.js", "../../../../../node_modules/technicalindicators/lib/moving_averages/MACD.js", "../../../../../node_modules/technicalindicators/lib/Utils/AverageGain.js", "../../../../../node_modules/technicalindicators/lib/Utils/AverageLoss.js", "../../../../../node_modules/technicalindicators/lib/oscillators/RSI.js", "../../../../../node_modules/technicalindicators/lib/Utils/SD.js", "../../../../../node_modules/technicalindicators/lib/volatility/BollingerBands.js", "../../../../../node_modules/technicalindicators/lib/moving_averages/WilderSmoothing.js", "../../../../../node_modules/technicalindicators/lib/directionalmovement/MinusDM.js", "../../../../../node_modules/technicalindicators/lib/directionalmovement/PlusDM.js", "../../../../../node_modules/technicalindicators/lib/directionalmovement/TrueRange.js", "../../../../../node_modules/technicalindicators/lib/directionalmovement/ADX.js", "../../../../../node_modules/technicalindicators/lib/directionalmovement/ATR.js", "../../../../../node_modules/technicalindicators/lib/momentum/ROC.js", "../../../../../node_modules/technicalindicators/lib/momentum/KST.js", "../../../../../node_modules/technicalindicators/lib/momentum/PSAR.js", "../../../../../node_modules/technicalindicators/lib/momentum/Stochastic.js", "../../../../../node_modules/technicalindicators/lib/momentum/WilliamsR.js", "../../../../../node_modules/technicalindicators/lib/volume/ADL.js", "../../../../../node_modules/technicalindicators/lib/volume/OBV.js", "../../../../../node_modules/technicalindicators/lib/momentum/TRIX.js", "../../../../../node_modules/technicalindicators/lib/volume/ForceIndex.js", "../../../../../node_modules/technicalindicators/lib/oscillators/CCI.js", "../../../../../node_modules/technicalindicators/lib/oscillators/AwesomeOscillator.js", "../../../../../node_modules/technicalindicators/lib/volume/VWAP.js", "../../../../../node_modules/technicalindicators/lib/volume/VolumeProfile.js", "../../../../../node_modules/technicalindicators/lib/chart_types/TypicalPrice.js", "../../../../../node_modules/technicalindicators/lib/volume/MFI.js", "../../../../../node_modules/technicalindicators/lib/momentum/StochasticRSI.js", "../../../../../node_modules/technicalindicators/lib/Utils/Highest.js", "../../../../../node_modules/technicalindicators/lib/Utils/Lowest.js", "../../../../../node_modules/technicalindicators/lib/Utils/Sum.js", "../../../../../node_modules/technicalindicators/lib/chart_types/Renko.js", "../../../../../node_modules/technicalindicators/lib/chart_types/HeikinAshi.js", "../../../../../node_modules/technicalindicators/lib/candlestick/CandlestickFinder.js", "../../../../../node_modules/technicalindicators/lib/candlestick/MorningStar.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BullishEngulfingPattern.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BullishHarami.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BullishHaramiCross.js", "../../../../../node_modules/technicalindicators/lib/candlestick/Doji.js", "../../../../../node_modules/technicalindicators/lib/candlestick/MorningDojiStar.js", "../../../../../node_modules/technicalindicators/lib/candlestick/DownsideTasukiGap.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BullishMarubozu.js", "../../../../../node_modules/technicalindicators/lib/candlestick/PiercingLine.js", "../../../../../node_modules/technicalindicators/lib/candlestick/ThreeWhiteSoldiers.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BullishHammerStick.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BullishInvertedHammerStick.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BearishHammerStick.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BearishInvertedHammerStick.js", "../../../../../node_modules/technicalindicators/lib/candlestick/HammerPattern.js", "../../../../../node_modules/technicalindicators/lib/candlestick/HammerPatternUnconfirmed.js", "../../../../../node_modules/technicalindicators/lib/candlestick/TweezerBottom.js", "../../../../../node_modules/technicalindicators/lib/candlestick/Bullish.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BearishEngulfingPattern.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BearishHarami.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BearishHaramiCross.js", "../../../../../node_modules/technicalindicators/lib/candlestick/EveningDojiStar.js", "../../../../../node_modules/technicalindicators/lib/candlestick/EveningStar.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BearishMarubozu.js", "../../../../../node_modules/technicalindicators/lib/candlestick/ThreeBlackCrows.js", "../../../../../node_modules/technicalindicators/lib/candlestick/HangingMan.js", "../../../../../node_modules/technicalindicators/lib/candlestick/HangingManUnconfirmed.js", "../../../../../node_modules/technicalindicators/lib/candlestick/ShootingStar.js", "../../../../../node_modules/technicalindicators/lib/candlestick/ShootingStarUnconfirmed.js", "../../../../../node_modules/technicalindicators/lib/candlestick/TweezerTop.js", "../../../../../node_modules/technicalindicators/lib/candlestick/Bearish.js", "../../../../../node_modules/technicalindicators/lib/candlestick/AbandonedBaby.js", "../../../../../node_modules/technicalindicators/lib/candlestick/DarkCloudCover.js", "../../../../../node_modules/technicalindicators/lib/candlestick/DragonFlyDoji.js", "../../../../../node_modules/technicalindicators/lib/candlestick/GraveStoneDoji.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BullishSpinningTop.js", "../../../../../node_modules/technicalindicators/lib/candlestick/BearishSpinningTop.js", "../../../../../node_modules/technicalindicators/lib/drawingtools/fibonacci.js", "../../../../../node_modules/technicalindicators/lib/ichimoku/IchimokuCloud.js", "../../../../../node_modules/technicalindicators/lib/volatility/KeltnerChannels.js", "../../../../../node_modules/technicalindicators/lib/volatility/ChandelierExit.js", "../../../../../node_modules/technicalindicators/lib/Utils/CrossUp.js", "../../../../../node_modules/technicalindicators/lib/Utils/CrossDown.js"], "sourcesContent": ["class Item {\n    constructor(data, prev, next) {\n        this.next = next;\n        if (next)\n            next.prev = this;\n        this.prev = prev;\n        if (prev)\n            prev.next = this;\n        this.data = data;\n    }\n}\nexport class LinkedList {\n    constructor() {\n        this._length = 0;\n    }\n    get head() {\n        return this._head && this._head.data;\n    }\n    get tail() {\n        return this._tail && this._tail.data;\n    }\n    get current() {\n        return this._current && this._current.data;\n    }\n    get length() {\n        return this._length;\n    }\n    push(data) {\n        this._tail = new Item(data, this._tail);\n        if (this._length === 0) {\n            this._head = this._tail;\n            this._current = this._head;\n            this._next = this._head;\n        }\n        this._length++;\n    }\n    pop() {\n        var tail = this._tail;\n        if (this._length === 0) {\n            return;\n        }\n        this._length--;\n        if (this._length === 0) {\n            this._head = this._tail = this._current = this._next = undefined;\n            return tail.data;\n        }\n        this._tail = tail.prev;\n        this._tail.next = undefined;\n        if (this._current === tail) {\n            this._current = this._tail;\n            this._next = undefined;\n        }\n        return tail.data;\n    }\n    shift() {\n        var head = this._head;\n        if (this._length === 0) {\n            return;\n        }\n        this._length--;\n        if (this._length === 0) {\n            this._head = this._tail = this._current = this._next = undefined;\n            return head.data;\n        }\n        this._head = this._head.next;\n        if (this._current === head) {\n            this._current = this._head;\n            this._next = this._current.next;\n        }\n        return head.data;\n    }\n    unshift(data) {\n        this._head = new Item(data, undefined, this._head);\n        if (this._length === 0) {\n            this._tail = this._head;\n            this._next = this._head;\n        }\n        this._length++;\n    }\n    unshiftCurrent() {\n        var current = this._current;\n        if (current === this._head || this._length < 2) {\n            return current && current.data;\n        }\n        // remove\n        if (current === this._tail) {\n            this._tail = current.prev;\n            this._tail.next = undefined;\n            this._current = this._tail;\n        }\n        else {\n            current.next.prev = current.prev;\n            current.prev.next = current.next;\n            this._current = current.prev;\n        }\n        this._next = this._current.next;\n        // unshift\n        current.next = this._head;\n        current.prev = undefined;\n        this._head.prev = current;\n        this._head = current;\n        return current.data;\n    }\n    removeCurrent() {\n        var current = this._current;\n        if (this._length === 0) {\n            return;\n        }\n        this._length--;\n        if (this._length === 0) {\n            this._head = this._tail = this._current = this._next = undefined;\n            return current.data;\n        }\n        if (current === this._tail) {\n            this._tail = current.prev;\n            this._tail.next = undefined;\n            this._current = this._tail;\n        }\n        else if (current === this._head) {\n            this._head = current.next;\n            this._head.prev = undefined;\n            this._current = this._head;\n        }\n        else {\n            current.next.prev = current.prev;\n            current.prev.next = current.next;\n            this._current = current.prev;\n        }\n        this._next = this._current.next;\n        return current.data;\n    }\n    resetCursor() {\n        this._current = this._next = this._head;\n        return this;\n    }\n    next() {\n        var next = this._next;\n        if (next !== undefined) {\n            this._next = next.next;\n            this._current = next;\n            return next.data;\n        }\n    }\n}\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/7/16.\n */\nimport { LinkedList } from './LinkedList';\nexport default class FixedSizeLinkedList extends LinkedList {\n    constructor(size, maintainHigh, maintainLow, maintainSum) {\n        super();\n        this.size = size;\n        this.maintainHigh = maintainHigh;\n        this.maintainLow = maintainLow;\n        this.maintainSum = maintainSum;\n        this.totalPushed = 0;\n        this.periodHigh = 0;\n        this.periodLow = Infinity;\n        this.periodSum = 0;\n        if (!size || typeof size !== 'number') {\n            throw ('Size required and should be a number.');\n        }\n        this._push = this.push;\n        this.push = function (data) {\n            this.add(data);\n            this.totalPushed++;\n        };\n    }\n    add(data) {\n        if (this.length === this.size) {\n            this.lastShift = this.shift();\n            this._push(data);\n            //TODO: FInd a better way\n            if (this.maintainHigh)\n                if (this.lastShift == this.periodHigh)\n                    this.calculatePeriodHigh();\n            if (this.maintainLow)\n                if (this.lastShift == this.periodLow)\n                    this.calculatePeriodLow();\n            if (this.maintainSum) {\n                this.periodSum = this.periodSum - this.lastShift;\n            }\n        }\n        else {\n            this._push(data);\n        }\n        //TODO: FInd a better way\n        if (this.maintainHigh)\n            if (this.periodHigh <= data)\n                (this.periodHigh = data);\n        if (this.maintainLow)\n            if (this.periodLow >= data)\n                (this.periodLow = data);\n        if (this.maintainSum) {\n            this.periodSum = this.periodSum + data;\n        }\n    }\n    *iterator() {\n        this.resetCursor();\n        while (this.next()) {\n            yield this.current;\n        }\n    }\n    calculatePeriodHigh() {\n        this.resetCursor();\n        if (this.next())\n            this.periodHigh = this.current;\n        while (this.next()) {\n            if (this.periodHigh <= this.current) {\n                this.periodHigh = this.current;\n            }\n            ;\n        }\n        ;\n    }\n    calculatePeriodLow() {\n        this.resetCursor();\n        if (this.next())\n            this.periodLow = this.current;\n        while (this.next()) {\n            if (this.periodLow >= this.current) {\n                this.periodLow = this.current;\n            }\n            ;\n        }\n        ;\n    }\n}\n", "export default class StockData {\n    constructor(open, high, low, close, reversedInput) {\n        this.open = open;\n        this.high = high;\n        this.low = low;\n        this.close = close;\n        this.reversedInput = reversedInput;\n    }\n}\nexport class CandleData {\n}\nexport class CandleList {\n    constructor() {\n        this.open = [];\n        this.high = [];\n        this.low = [];\n        this.close = [];\n        this.volume = [];\n        this.timestamp = [];\n    }\n}\n", "let config = {};\nexport function setConfig(key, value) {\n    config[key] = value;\n}\nexport function getConfig(key) {\n    return config[key];\n}\n", "import { getConfig } from '../config';\nexport function format(v) {\n    let precision = getConfig('precision');\n    if (precision) {\n        return parseFloat(v.toPrecision(precision));\n    }\n    return v;\n}\n", "import { format as nf } from '../Utils/NumberFormatter';\nexport class IndicatorInput {\n}\nexport class AllInputs {\n}\nexport class Indicator {\n    constructor(input) {\n        this.format = input.format || nf;\n    }\n    static reverseInputs(input) {\n        if (input.reversedInput) {\n            input.values ? input.values.reverse() : undefined;\n            input.open ? input.open.reverse() : undefined;\n            input.high ? input.high.reverse() : undefined;\n            input.low ? input.low.reverse() : undefined;\n            input.close ? input.close.reverse() : undefined;\n            input.volume ? input.volume.reverse() : undefined;\n            input.timestamp ? input.timestamp.reverse() : undefined;\n        }\n    }\n    getResult() {\n        return this.result;\n    }\n}\n", "//STEP 1. Import Necessary indicator or rather last step\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { LinkedList } from '../Utils/LinkedList';\n//STEP 2. Create the input for the indicator, mandatory should be in the constructor\nexport class MAInput extends IndicatorInput {\n    constructor(period, values) {\n        super();\n        this.period = period;\n        this.values = values;\n    }\n}\n//STEP3. Add class based syntax with export\nexport class SMA extends Indicator {\n    constructor(input) {\n        super(input);\n        this.period = input.period;\n        this.price = input.values;\n        var genFn = (function* (period) {\n            var list = new LinkedList();\n            var sum = 0;\n            var counter = 1;\n            var current = yield;\n            var result;\n            list.push(0);\n            while (true) {\n                if (counter < period) {\n                    counter++;\n                    list.push(current);\n                    sum = sum + current;\n                }\n                else {\n                    sum = sum - list.shift() + current;\n                    result = ((sum) / period);\n                    list.push(current);\n                }\n                current = yield result;\n            }\n        });\n        this.generator = genFn(this.period);\n        this.generator.next();\n        this.result = [];\n        this.price.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nSMA.calculate = sma;\nexport function sma(input) {\n    Indicator.reverseInputs(input);\n    var result = new SMA(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n//STEP 6. Run the tests\n", "import { Indicator } from '../indicator/indicator';\nimport { SMA } from './SMA';\nexport class EMA extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        var exponent = (2 / (period + 1));\n        var sma;\n        this.result = [];\n        sma = new SMA({ period: period, values: [] });\n        var genFn = (function* () {\n            var tick = yield;\n            var prevEma;\n            while (true) {\n                if (prevEma !== undefined && tick !== undefined) {\n                    prevEma = ((tick - prevEma) * exponent) + prevEma;\n                    tick = yield prevEma;\n                }\n                else {\n                    tick = yield;\n                    prevEma = sma.nextValue(tick);\n                    if (prevEma)\n                        tick = yield prevEma;\n                }\n            }\n        });\n        this.generator = genFn();\n        this.generator.next();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nEMA.calculate = ema;\nexport function ema(input) {\n    Indicator.reverseInputs(input);\n    var result = new EMA(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "\"use strict\";\nimport { Indicator } from '../indicator/indicator';\nimport { LinkedList } from '../Utils/LinkedList';\nexport class WMA extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        this.result = [];\n        this.generator = (function* () {\n            let data = new LinkedList();\n            let denominator = period * (period + 1) / 2;\n            while (true) {\n                if ((data.length) < period) {\n                    data.push(yield);\n                }\n                else {\n                    data.resetCursor();\n                    let result = 0;\n                    for (let i = 1; i <= period; i++) {\n                        result = result + (data.next() * i / (denominator));\n                    }\n                    var next = yield result;\n                    data.shift();\n                    data.push(next);\n                }\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick, index) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    //STEP 5. REMOVE GET RESULT FUNCTION\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nWMA.calculate = wma;\n;\nexport function wma(input) {\n    Indicator.reverseInputs(input);\n    var result = new WMA(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "import { Indicator } from '../indicator/indicator';\nimport { SMA } from './SMA';\nexport class WEMA extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        var exponent = 1 / period;\n        var sma;\n        this.result = [];\n        sma = new SMA({ period: period, values: [] });\n        var genFn = (function* () {\n            var tick = yield;\n            var prevEma;\n            while (true) {\n                if (prevEma !== undefined && tick !== undefined) {\n                    prevEma = ((tick - prevEma) * exponent) + prevEma;\n                    tick = yield prevEma;\n                }\n                else {\n                    tick = yield;\n                    prevEma = sma.nextValue(tick);\n                    if (prevEma !== undefined)\n                        tick = yield prevEma;\n                }\n            }\n        });\n        this.generator = genFn();\n        this.generator.next();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nWEMA.calculate = wema;\nexport function wema(input) {\n    Indicator.reverseInputs(input);\n    var result = new WEMA(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/4/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from './SMA';\nimport { EMA } from './EMA';\nexport class MACDInput extends IndicatorInput {\n    constructor(values) {\n        super();\n        this.values = values;\n        this.SimpleMAOscillator = true;\n        this.SimpleMASignal = true;\n    }\n}\nexport class MACDOutput {\n}\nexport class MACD extends Indicator {\n    constructor(input) {\n        super(input);\n        var oscillatorMAtype = input.SimpleMAOscillator ? SMA : EMA;\n        var signalMAtype = input.SimpleMASignal ? SMA : EMA;\n        var fastMAProducer = new oscillatorMAtype({ period: input.fastPeriod, values: [], format: (v) => { return v; } });\n        var slowMAProducer = new oscillatorMAtype({ period: input.slowPeriod, values: [], format: (v) => { return v; } });\n        var signalMAProducer = new signalMAtype({ period: input.signalPeriod, values: [], format: (v) => { return v; } });\n        var format = this.format;\n        this.result = [];\n        this.generator = (function* () {\n            var index = 0;\n            var tick;\n            var MACD, signal, histogram, fast, slow;\n            while (true) {\n                if (index < input.slowPeriod) {\n                    tick = yield;\n                    fast = fastMAProducer.nextValue(tick);\n                    slow = slowMAProducer.nextValue(tick);\n                    index++;\n                    continue;\n                }\n                if (fast && slow) { //Just for typescript to be happy\n                    MACD = fast - slow;\n                    signal = signalMAProducer.nextValue(MACD);\n                }\n                histogram = MACD - signal;\n                tick = yield ({\n                    //fast : fast,\n                    //slow : slow,\n                    MACD: format(MACD),\n                    signal: signal ? format(signal) : undefined,\n                    histogram: isNaN(histogram) ? undefined : format(histogram)\n                });\n                fast = fastMAProducer.nextValue(tick);\n                slow = slowMAProducer.nextValue(tick);\n            }\n        })();\n        this.generator.next();\n        input.values.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        return result;\n    }\n    ;\n}\nMACD.calculate = macd;\nexport function macd(input) {\n    Indicator.reverseInputs(input);\n    var result = new MACD(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class AvgGainInput extends IndicatorInput {\n}\nexport class AverageGain extends Indicator {\n    constructor(input) {\n        super(input);\n        let values = input.values;\n        let period = input.period;\n        let format = this.format;\n        this.generator = (function* (period) {\n            var currentValue = yield;\n            var counter = 1;\n            var gainSum = 0;\n            var avgGain;\n            var gain;\n            var lastValue = currentValue;\n            currentValue = yield;\n            while (true) {\n                gain = currentValue - lastValue;\n                gain = gain > 0 ? gain : 0;\n                if (gain > 0) {\n                    gainSum = gainSum + gain;\n                }\n                if (counter < period) {\n                    counter++;\n                }\n                else if (avgGain === undefined) {\n                    avgGain = gainSum / period;\n                }\n                else {\n                    avgGain = ((avgGain * (period - 1)) + gain) / period;\n                }\n                lastValue = currentValue;\n                avgGain = (avgGain !== undefined) ? format(avgGain) : undefined;\n                currentValue = yield avgGain;\n            }\n        })(period);\n        this.generator.next();\n        this.result = [];\n        values.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nAverageGain.calculate = averagegain;\nexport function averagegain(input) {\n    Indicator.reverseInputs(input);\n    var result = new AverageGain(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class AvgLossInput extends IndicatorInput {\n}\nexport class AverageLoss extends Indicator {\n    constructor(input) {\n        super(input);\n        let values = input.values;\n        let period = input.period;\n        let format = this.format;\n        this.generator = (function* (period) {\n            var currentValue = yield;\n            var counter = 1;\n            var lossSum = 0;\n            var avgLoss;\n            var loss;\n            var lastValue = currentValue;\n            currentValue = yield;\n            while (true) {\n                loss = lastValue - currentValue;\n                loss = loss > 0 ? loss : 0;\n                if (loss > 0) {\n                    lossSum = lossSum + loss;\n                }\n                if (counter < period) {\n                    counter++;\n                }\n                else if (avgLoss === undefined) {\n                    avgLoss = lossSum / period;\n                }\n                else {\n                    avgLoss = ((avgLoss * (period - 1)) + loss) / period;\n                }\n                lastValue = currentValue;\n                avgLoss = (avgLoss !== undefined) ? format(avgLoss) : undefined;\n                currentValue = yield avgLoss;\n            }\n        })(period);\n        this.generator.next();\n        this.result = [];\n        values.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nAverageLoss.calculate = averageloss;\nexport function averageloss(input) {\n    Indicator.reverseInputs(input);\n    var result = new AverageLoss(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/5/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { AverageGain } from '../Utils/AverageGain';\nimport { AverageLoss } from '../Utils/AverageLoss';\nexport class RSIInput extends IndicatorInput {\n}\nexport class RSI extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var values = input.values;\n        var GainProvider = new AverageGain({ period: period, values: [] });\n        var LossProvider = new AverageLoss({ period: period, values: [] });\n        let count = 1;\n        this.generator = (function* (period) {\n            var current = yield;\n            var lastAvgGain, lastAvgLoss, RS, currentRSI;\n            while (true) {\n                lastAvgGain = GainProvider.nextValue(current);\n                lastAvgLoss = LossProvider.nextValue(current);\n                if ((lastAvgGain !== undefined) && (lastAvgLoss !== undefined)) {\n                    if (lastAvgLoss === 0) {\n                        currentRSI = 100;\n                    }\n                    else if (lastAvgGain === 0) {\n                        currentRSI = 0;\n                    }\n                    else {\n                        RS = lastAvgGain / lastAvgLoss;\n                        RS = isNaN(RS) ? 0 : RS;\n                        currentRSI = parseFloat((100 - (100 / (1 + RS))).toFixed(2));\n                    }\n                }\n                count++;\n                current = yield currentRSI;\n            }\n        })(period);\n        this.generator.next();\n        this.result = [];\n        values.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nRSI.calculate = rsi;\nexport function rsi(input) {\n    Indicator.reverseInputs(input);\n    var result = new RSI(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\n/**\n * Created by <PERSON>ra<PERSON>dan on 5/7/16.\n */\n\"use strict\";\nexport class SDInput extends IndicatorInput {\n}\n;\nexport class SD extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        var sma = new SMA({ period: period, values: [], format: (v) => { return v; } });\n        this.result = [];\n        this.generator = (function* () {\n            var tick;\n            var mean;\n            var currentSet = new LinkedList(period);\n            ;\n            tick = yield;\n            var sd;\n            while (true) {\n                currentSet.push(tick);\n                mean = sma.nextValue(tick);\n                if (mean) {\n                    let sum = 0;\n                    for (let x of currentSet.iterator()) {\n                        sum = sum + (Math.pow((x - mean), 2));\n                    }\n                    sd = Math.sqrt(sum / (period));\n                }\n                tick = yield sd;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var nextResult = this.generator.next(price);\n        if (nextResult.value != undefined)\n            return this.format(nextResult.value);\n    }\n    ;\n}\nSD.calculate = sd;\nexport function sd(input) {\n    Indicator.reverseInputs(input);\n    var result = new SD(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "\"use strict\";\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport { SD } from '../Utils/SD';\nexport class BollingerBandsInput extends IndicatorInput {\n}\n;\nexport class BollingerBandsOutput extends IndicatorInput {\n}\n;\nexport class <PERSON>llingerBands extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        var stdDev = input.stdDev;\n        var format = this.format;\n        var sma, sd;\n        this.result = [];\n        sma = new SMA({ period: period, values: [], format: (v) => { return v; } });\n        sd = new SD({ period: period, values: [], format: (v) => { return v; } });\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var calcSMA;\n            var calcsd;\n            tick = yield;\n            while (true) {\n                calcSMA = sma.nextValue(tick);\n                calcsd = sd.nextValue(tick);\n                if (calcSMA) {\n                    let middle = format(calcSMA);\n                    let upper = format(calcSMA + (calcsd * stdDev));\n                    let lower = format(calcSMA - (calcsd * stdDev));\n                    let pb = format((tick - lower) / (upper - lower));\n                    result = {\n                        middle: middle,\n                        upper: upper,\n                        lower: lower,\n                        pb: pb\n                    };\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nBollingerBands.calculate = bollingerbands;\nexport function bollingerbands(input) {\n    Indicator.reverseInputs(input);\n    var result = new BollingerBands(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator } from '../indicator/indicator';\nimport { LinkedList } from '../Utils/LinkedList';\n//STEP3. Add class based syntax with export\nexport class WilderSmoothing extends Indicator {\n    constructor(input) {\n        super(input);\n        this.period = input.period;\n        this.price = input.values;\n        var genFn = (function* (period) {\n            var list = new LinkedList();\n            var sum = 0;\n            var counter = 1;\n            var current = yield;\n            var result = 0;\n            while (true) {\n                if (counter < period) {\n                    counter++;\n                    sum = sum + current;\n                    result = undefined;\n                }\n                else if (counter == period) {\n                    counter++;\n                    sum = sum + current;\n                    result = sum;\n                }\n                else {\n                    result = result - (result / period) + current;\n                }\n                current = yield result;\n            }\n        });\n        this.generator = genFn(this.period);\n        this.generator.next();\n        this.result = [];\n        this.price.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nWilderSmoothing.calculate = wildersmoothing;\nexport function wildersmoothing(input) {\n    Indicator.reverseInputs(input);\n    var result = new WilderSmoothing(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n//STEP 6. Run the tests\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\n\"use strict\";\nexport class MDMInput extends IndicatorInput {\n}\n;\nexport class MDM extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var format = this.format;\n        if (lows.length != highs.length) {\n            throw ('Inputs(low,high) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var minusDm;\n            var current = yield;\n            var last;\n            while (true) {\n                if (last) {\n                    let upMove = (current.high - last.high);\n                    let downMove = (last.low - current.low);\n                    minusDm = format((downMove > upMove && downMove > 0) ? downMove : 0);\n                }\n                last = current;\n                current = yield minusDm;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index]\n            });\n            if (result.value !== undefined)\n                this.result.push(result.value);\n        });\n    }\n    ;\n    static calculate(input) {\n        Indicator.reverseInputs(input);\n        var result = new MDM(input).result;\n        if (input.reversedInput) {\n            result.reverse();\n        }\n        Indicator.reverseInputs(input);\n        return result;\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\nexport class PDMInput extends IndicatorInput {\n}\n;\nexport class PDM extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var format = this.format;\n        if (lows.length != highs.length) {\n            throw ('Inputs(low,high) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var plusDm;\n            var current = yield;\n            var last;\n            while (true) {\n                if (last) {\n                    let upMove = (current.high - last.high);\n                    let downMove = (last.low - current.low);\n                    plusDm = format((upMove > downMove && upMove > 0) ? upMove : 0);\n                }\n                last = current;\n                current = yield plusDm;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index]\n            });\n            if (result.value !== undefined)\n                this.result.push(result.value);\n        });\n    }\n    ;\n    static calculate(input) {\n        Indicator.reverseInputs(input);\n        var result = new PDM(input).result;\n        if (input.reversedInput) {\n            result.reverse();\n        }\n        Indicator.reverseInputs(input);\n        return result;\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\n\"use strict\";\nexport class TrueRangeInput extends IndicatorInput {\n}\n;\nexport class TrueRange extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var format = this.format;\n        if (lows.length != highs.length) {\n            throw ('Inputs(low,high) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var current = yield;\n            var previousClose, result;\n            while (true) {\n                if (previousClose === undefined) {\n                    previousClose = current.close;\n                    current = yield result;\n                }\n                result = Math.max(current.high - current.low, isNaN(Math.abs(current.high - previousClose)) ? 0 : Math.abs(current.high - previousClose), isNaN(Math.abs(current.low - previousClose)) ? 0 : Math.abs(current.low - previousClose));\n                previousClose = current.close;\n                if (result != undefined) {\n                    result = format(result);\n                }\n                current = yield result;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nTrueRange.calculate = truerange;\nexport function truerange(input) {\n    Indicator.reverseInputs(input);\n    var result = new TrueRange(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { WilderSmoothing } from '../moving_averages/WilderSmoothing';\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { MDM } from './MinusDM';\nimport { PDM } from './PlusDM';\nimport { TrueRange } from './TrueRange';\nimport { WEMA } from '../moving_averages/WEMA';\nexport class ADXInput extends IndicatorInput {\n}\n;\nexport class ADXOutput extends IndicatorInput {\n}\n;\nexport class ADX extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var period = input.period;\n        var format = this.format;\n        var plusDM = new PDM({\n            high: [],\n            low: []\n        });\n        var minusDM = new MDM({\n            high: [],\n            low: []\n        });\n        var emaPDM = new WilderSmoothing({ period: period, values: [], format: (v) => { return v; } });\n        var emaMDM = new WilderSmoothing({ period: period, values: [], format: (v) => { return v; } });\n        var emaTR = new WilderSmoothing({ period: period, values: [], format: (v) => { return v; } });\n        var emaDX = new WEMA({ period: period, values: [], format: (v) => { return v; } });\n        var tr = new TrueRange({\n            low: [],\n            high: [],\n            close: [],\n        });\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        ADXOutput;\n        this.generator = (function* () {\n            var tick = yield;\n            var index = 0;\n            var lastATR, lastAPDM, lastAMDM, lastPDI, lastMDI, lastDX, smoothedDX;\n            lastATR = 0;\n            lastAPDM = 0;\n            lastAMDM = 0;\n            while (true) {\n                let calcTr = tr.nextValue(tick);\n                let calcPDM = plusDM.nextValue(tick);\n                let calcMDM = minusDM.nextValue(tick);\n                if (calcTr === undefined) {\n                    tick = yield;\n                    continue;\n                }\n                let lastATR = emaTR.nextValue(calcTr);\n                let lastAPDM = emaPDM.nextValue(calcPDM);\n                let lastAMDM = emaMDM.nextValue(calcMDM);\n                if ((lastATR != undefined) && (lastAPDM != undefined) && (lastAMDM != undefined)) {\n                    lastPDI = (lastAPDM) * 100 / lastATR;\n                    lastMDI = (lastAMDM) * 100 / lastATR;\n                    let diDiff = Math.abs(lastPDI - lastMDI);\n                    let diSum = (lastPDI + lastMDI);\n                    lastDX = (diDiff / diSum) * 100;\n                    smoothedDX = emaDX.nextValue(lastDX);\n                    // console.log(tick.high.toFixed(2), tick.low.toFixed(2), tick.close.toFixed(2) , calcTr.toFixed(2), calcPDM.toFixed(2), calcMDM.toFixed(2), lastATR.toFixed(2), lastAPDM.toFixed(2), lastAMDM.toFixed(2), lastPDI.toFixed(2), lastMDI.toFixed(2), diDiff.toFixed(2), diSum.toFixed(2), lastDX.toFixed(2));\n                }\n                tick = yield { adx: smoothedDX, pdi: lastPDI, mdi: lastMDI };\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value != undefined && result.value.adx != undefined) {\n                this.result.push({ adx: format(result.value.adx), pdi: format(result.value.pdi), mdi: format(result.value.mdi) });\n            }\n        });\n    }\n    ;\n    ;\n    nextValue(price) {\n        let result = this.generator.next(price).value;\n        if (result != undefined && result.adx != undefined) {\n            return { adx: this.format(result.adx), pdi: this.format(result.pdi), mdi: this.format(result.mdi) };\n        }\n    }\n    ;\n}\nADX.calculate = adx;\nexport function adx(input) {\n    Indicator.reverseInputs(input);\n    var result = new ADX(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\n\"use strict\";\nimport { WEMA } from '../moving_averages/WEMA';\nimport { TrueRange } from './TrueRange';\nexport class ATRInput extends IndicatorInput {\n}\n;\nexport class ATR extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var period = input.period;\n        var format = this.format;\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        var trueRange = new TrueRange({\n            low: [],\n            high: [],\n            close: []\n        });\n        var wema = new WEMA({ period: period, values: [], format: (v) => { return v; } });\n        this.result = [];\n        this.generator = (function* () {\n            var tick = yield;\n            var avgTrueRange, trange;\n            ;\n            while (true) {\n                trange = trueRange.nextValue({\n                    low: tick.low,\n                    high: tick.high,\n                    close: tick.close\n                });\n                if (trange === undefined) {\n                    avgTrueRange = undefined;\n                }\n                else {\n                    avgTrueRange = wema.nextValue(trange);\n                }\n                tick = yield avgTrueRange;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(format(result.value));\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nATR.calculate = atr;\nexport function atr(input) {\n    Indicator.reverseInputs(input);\n    var result = new ATR(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class ROCInput extends IndicatorInput {\n}\nexport class ROC extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        this.result = [];\n        this.generator = (function* () {\n            let index = 1;\n            var pastPeriods = new LinkedList(period);\n            ;\n            var tick = yield;\n            var roc;\n            while (true) {\n                pastPeriods.push(tick);\n                if (index < period) {\n                    index++;\n                }\n                else {\n                    roc = ((tick - pastPeriods.lastShift) / (pastPeriods.lastShift)) * 100;\n                }\n                tick = yield roc;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined && (!isNaN(result.value))) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var nextResult = this.generator.next(price);\n        if (nextResult.value != undefined && (!isNaN(nextResult.value))) {\n            return this.format(nextResult.value);\n        }\n    }\n    ;\n}\nROC.calculate = roc;\n;\nexport function roc(input) {\n    Indicator.reverseInputs(input);\n    var result = new ROC(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport { ROC } from './ROC';\nexport class KSTInput extends IndicatorInput {\n}\nexport class KSTOutput {\n}\nexport class KST extends Indicator {\n    constructor(input) {\n        super(input);\n        let priceArray = input.values;\n        let rocPer1 = input.ROCPer1;\n        let rocPer2 = input.ROCPer2;\n        let rocPer3 = input.ROCPer3;\n        let rocPer4 = input.ROCPer4;\n        let smaPer1 = input.SMAROCPer1;\n        let smaPer2 = input.SMAROCPer2;\n        let smaPer3 = input.SMAROCPer3;\n        let smaPer4 = input.SMAROCPer4;\n        let signalPeriod = input.signalPeriod;\n        let roc1 = new ROC({ period: rocPer1, values: [] });\n        let roc2 = new ROC({ period: rocPer2, values: [] });\n        let roc3 = new ROC({ period: rocPer3, values: [] });\n        let roc4 = new ROC({ period: rocPer4, values: [] });\n        let sma1 = new SMA({ period: smaPer1, values: [], format: (v) => { return v; } });\n        let sma2 = new SMA({ period: smaPer2, values: [], format: (v) => { return v; } });\n        let sma3 = new SMA({ period: smaPer3, values: [], format: (v) => { return v; } });\n        let sma4 = new SMA({ period: smaPer4, values: [], format: (v) => { return v; } });\n        let signalSMA = new SMA({ period: signalPeriod, values: [], format: (v) => { return v; } });\n        var format = this.format;\n        this.result = [];\n        let firstResult = Math.max(rocPer1 + smaPer1, rocPer2 + smaPer2, rocPer3 + smaPer3, rocPer4 + smaPer4);\n        this.generator = (function* () {\n            let index = 1;\n            let tick = yield;\n            let kst;\n            let RCMA1, RCMA2, RCMA3, RCMA4, signal, result;\n            while (true) {\n                let roc1Result = roc1.nextValue(tick);\n                let roc2Result = roc2.nextValue(tick);\n                let roc3Result = roc3.nextValue(tick);\n                let roc4Result = roc4.nextValue(tick);\n                RCMA1 = (roc1Result !== undefined) ? sma1.nextValue(roc1Result) : undefined;\n                RCMA2 = (roc2Result !== undefined) ? sma2.nextValue(roc2Result) : undefined;\n                RCMA3 = (roc3Result !== undefined) ? sma3.nextValue(roc3Result) : undefined;\n                RCMA4 = (roc4Result !== undefined) ? sma4.nextValue(roc4Result) : undefined;\n                if (index < firstResult) {\n                    index++;\n                }\n                else {\n                    kst = (RCMA1 * 1) + (RCMA2 * 2) + (RCMA3 * 3) + (RCMA4 * 4);\n                }\n                signal = (kst !== undefined) ? signalSMA.nextValue(kst) : undefined;\n                result = kst !== undefined ? {\n                    kst: format(kst),\n                    signal: signal ? format(signal) : undefined\n                } : undefined;\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            let result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        let nextResult = this.generator.next(price);\n        if (nextResult.value != undefined)\n            return nextResult.value;\n    }\n    ;\n}\nKST.calculate = kst;\nexport function kst(input) {\n    Indicator.reverseInputs(input);\n    var result = new KST(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\n\"use strict\";\n/*\n  There seems to be a few interpretations of the rules for this regarding which prices.\n  I mean the english from which periods are included. The wording does seem to\n  introduce some discrepancy so maybe that is why. I want to put the author's\n  own description here to reassess this later.\n  ----------------------------------------------------------------------------------------\n  For the first day of entry the SAR is the previous Significant Point\n\n  If long the SP is the lowest price reached while in the previous short trade\n  If short the SP is the highest price reached while in the previous long trade\n\n  If long:\n  Find the difference between the highest price made while in the trade and the SAR for today.\n  Multiple the difference by the AF and ADD the result to today's SAR to obtain the SAR for tomorrow.\n  Use 0.02 for the first AF and increase it by 0.02 on every day that a new high for the trade is made.\n  If a new high is not made continue to use the AF as last increased. Do not increase the AF above .20\n\n  Never move the SAR for tomorrow ABOVE the previous day's LOW or today's LOW.\n  If the SAR is calculated to be ABOVE the previous day's LOW or today's LOW then use the lower low between today and the previous day as the new SAR.\n  Make the next day's calculations based on this SAR.\n\n  If short:\n  Find the difference between the lowest price made while in the trade and the SAR for today.\n  Multiple the difference by the AF and SUBTRACT the result to today's SAR to obtain the SAR for tomorrow.\n  Use 0.02 for the first AF and increase it by 0.02 on every day that a new high for the trade is made.\n  If a new high is not made continue to use the AF as last increased. Do not increase the AF above .20\n\n  Never move the SAR for tomorrow BELOW the previous day's HIGH or today's HIGH.\n  If the SAR is calculated to be BELOW the previous day's HIGH or today's HIGH then use the higher high between today and the previous day as the new SAR. Make the next day's calculations based on this SAR.\n  ----------------------------------------------------------------------------------------\n*/\nexport class PSARInput extends IndicatorInput {\n}\n;\nexport class PSAR extends Indicator {\n    constructor(input) {\n        super(input);\n        let highs = input.high || [];\n        let lows = input.low || [];\n        var genFn = function* (step, max) {\n            let curr, extreme, sar, furthest;\n            let up = true;\n            let accel = step;\n            let prev = yield;\n            while (true) {\n                if (curr) {\n                    sar = sar + accel * (extreme - sar);\n                    if (up) {\n                        sar = Math.min(sar, furthest.low, prev.low);\n                        if (curr.high > extreme) {\n                            extreme = curr.high;\n                            accel = Math.min(accel + step, max);\n                        }\n                        ;\n                    }\n                    else {\n                        sar = Math.max(sar, furthest.high, prev.high);\n                        if (curr.low < extreme) {\n                            extreme = curr.low;\n                            accel = Math.min(accel + step, max);\n                        }\n                    }\n                    if ((up && curr.low < sar) || (!up && curr.high > sar)) {\n                        accel = step;\n                        sar = extreme;\n                        up = !up;\n                        extreme = !up ? curr.low : curr.high;\n                    }\n                }\n                else {\n                    // Randomly setup start values? What is the trend on first tick??\n                    sar = prev.low;\n                    extreme = prev.high;\n                }\n                furthest = prev;\n                if (curr)\n                    prev = curr;\n                curr = yield sar;\n            }\n        };\n        this.result = [];\n        this.generator = genFn(input.step, input.max);\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(input) {\n        let nextResult = this.generator.next(input);\n        if (nextResult.value !== undefined)\n            return nextResult.value;\n    }\n    ;\n}\nPSAR.calculate = psar;\nexport function psar(input) {\n    Indicator.reverseInputs(input);\n    var result = new PSAR(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/10/16.\n */\n\"use strict\";\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nimport { SMA } from '../moving_averages/SMA';\nexport class StochasticInput extends IndicatorInput {\n}\n;\nexport class StochasticOutput {\n}\n;\nexport class Stochastic extends Indicator {\n    constructor(input) {\n        super(input);\n        let lows = input.low;\n        let highs = input.high;\n        let closes = input.close;\n        let period = input.period;\n        let signalPeriod = input.signalPeriod;\n        let format = this.format;\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        //%K = (Current Close - Lowest Low)/(Highest High - Lowest Low) * 100\n        //%D = 3-day SMA of %K\n        //\n        //Lowest Low = lowest low for the look-back period\n        //Highest High = highest high for the look-back period\n        //%K is multiplied by 100 to move the decimal point two places\n        this.generator = (function* () {\n            let index = 1;\n            let pastHighPeriods = new LinkedList(period, true, false);\n            let pastLowPeriods = new LinkedList(period, false, true);\n            let dSma = new SMA({\n                period: signalPeriod,\n                values: [],\n                format: (v) => { return v; }\n            });\n            let k, d;\n            var tick = yield;\n            while (true) {\n                pastHighPeriods.push(tick.high);\n                pastLowPeriods.push(tick.low);\n                if (index < period) {\n                    index++;\n                    tick = yield;\n                    continue;\n                }\n                let periodLow = pastLowPeriods.periodLow;\n                k = (tick.close - periodLow) / (pastHighPeriods.periodHigh - periodLow) * 100;\n                k = isNaN(k) ? 0 : k; //This happens when the close, high and low are same for the entire period; Bug fix for \n                d = dSma.nextValue(k);\n                tick = yield {\n                    k: format(k),\n                    d: (d !== undefined) ? format(d) : undefined\n                };\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(input) {\n        let nextResult = this.generator.next(input);\n        if (nextResult.value !== undefined)\n            return nextResult.value;\n    }\n    ;\n}\nStochastic.calculate = stochastic;\nexport function stochastic(input) {\n    Indicator.reverseInputs(input);\n    var result = new Stochastic(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class WilliamsRInput extends IndicatorInput {\n}\n;\nexport class WilliamsR extends Indicator {\n    constructor(input) {\n        super(input);\n        let lows = input.low;\n        let highs = input.high;\n        let closes = input.close;\n        let period = input.period;\n        let format = this.format;\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        //%R = (Highest High - Close)/(Highest High - Lowest Low) * -100\n        //Lowest Low = lowest low for the look-back period\n        //Highest High = highest high for the look-back period\n        //%R is multiplied by -100 correct the inversion and move the decimal.\n        this.generator = (function* () {\n            let index = 1;\n            let pastHighPeriods = new LinkedList(period, true, false);\n            let pastLowPeriods = new LinkedList(period, false, true);\n            let periodLow;\n            let periodHigh;\n            var tick = yield;\n            let williamsR;\n            while (true) {\n                pastHighPeriods.push(tick.high);\n                pastLowPeriods.push(tick.low);\n                if (index < period) {\n                    index++;\n                    tick = yield;\n                    continue;\n                }\n                periodLow = pastLowPeriods.periodLow;\n                periodHigh = pastHighPeriods.periodHigh;\n                williamsR = format((periodHigh - tick.close) / (periodHigh - periodLow) * -100);\n                tick = yield williamsR;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((low, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var nextResult = this.generator.next(price);\n        if (nextResult.value != undefined)\n            return this.format(nextResult.value);\n    }\n    ;\n}\nWilliamsR.calculate = williamsr;\nexport function williamsr(input) {\n    Indicator.reverseInputs(input);\n    var result = new WilliamsR(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/17/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class ADLInput extends IndicatorInput {\n}\nexport class ADL extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var closes = input.close;\n        var volumes = input.volume;\n        if (!((lows.length === highs.length) && (highs.length === closes.length) && (highs.length === volumes.length))) {\n            throw ('Inputs(low,high, close, volumes) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var result = 0;\n            var tick;\n            tick = yield;\n            while (true) {\n                let moneyFlowMultiplier = ((tick.close - tick.low) - (tick.high - tick.close)) / (tick.high - tick.low);\n                moneyFlowMultiplier = isNaN(moneyFlowMultiplier) ? 1 : moneyFlowMultiplier;\n                let moneyFlowVolume = moneyFlowMultiplier * tick.volume;\n                result = result + moneyFlowVolume;\n                tick = yield Math.round(result);\n            }\n        })();\n        this.generator.next();\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: lows[index],\n                close: closes[index],\n                volume: volumes[index]\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nADL.calculate = adl;\nexport function adl(input) {\n    Indicator.reverseInputs(input);\n    var result = new ADL(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/17/16.\n */\n\"use strict\";\nexport class OBVInput extends IndicatorInput {\n}\nexport class OBV extends Indicator {\n    constructor(input) {\n        super(input);\n        var closes = input.close;\n        var volumes = input.volume;\n        this.result = [];\n        this.generator = (function* () {\n            var result = 0;\n            var tick;\n            var lastClose;\n            tick = yield;\n            if (tick.close && (typeof tick.close === 'number')) {\n                lastClose = tick.close;\n                tick = yield;\n            }\n            while (true) {\n                if (lastClose < tick.close) {\n                    result = result + tick.volume;\n                }\n                else if (tick.close < lastClose) {\n                    result = result - tick.volume;\n                }\n                lastClose = tick.close;\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        closes.forEach((close, index) => {\n            let tickInput = {\n                close: closes[index],\n                volume: volumes[index]\n            };\n            let result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nOBV.calculate = obv;\nexport function obv(input) {\n    Indicator.reverseInputs(input);\n    var result = new OBV(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/9/16.\n */\n\"use strict\";\nimport { ROC } from './ROC.js';\nimport { EMA } from '../moving_averages/EMA.js';\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class TRIXInput extends IndicatorInput {\n}\n;\nexport class TRIX extends Indicator {\n    constructor(input) {\n        super(input);\n        let priceArray = input.values;\n        let period = input.period;\n        let format = this.format;\n        let ema = new EMA({ period: period, values: [], format: (v) => { return v; } });\n        let emaOfema = new EMA({ period: period, values: [], format: (v) => { return v; } });\n        let emaOfemaOfema = new EMA({ period: period, values: [], format: (v) => { return v; } });\n        let trixROC = new ROC({ period: 1, values: [], format: (v) => { return v; } });\n        this.result = [];\n        this.generator = (function* () {\n            let tick = yield;\n            while (true) {\n                let initialema = ema.nextValue(tick);\n                let smoothedResult = initialema ? emaOfema.nextValue(initialema) : undefined;\n                let doubleSmoothedResult = smoothedResult ? emaOfemaOfema.nextValue(smoothedResult) : undefined;\n                let result = doubleSmoothedResult ? trixROC.nextValue(doubleSmoothedResult) : undefined;\n                tick = yield result ? format(result) : undefined;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            let result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        let nextResult = this.generator.next(price);\n        if (nextResult.value !== undefined)\n            return nextResult.value;\n    }\n    ;\n}\nTRIX.calculate = trix;\nexport function trix(input) {\n    Indicator.reverseInputs(input);\n    var result = new TRIX(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { EMA } from '../moving_averages/EMA';\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class ForceIndexInput extends IndicatorInput {\n    constructor() {\n        super(...arguments);\n        this.period = 1;\n    }\n}\n;\nexport class ForceIndex extends Indicator {\n    constructor(input) {\n        super(input);\n        var closes = input.close;\n        var volumes = input.volume;\n        var period = input.period || 1;\n        if (!((volumes.length === closes.length))) {\n            throw ('Inputs(volume, close) not of equal size');\n        }\n        let emaForceIndex = new EMA({ values: [], period: period });\n        this.result = [];\n        this.generator = (function* () {\n            var previousTick = yield;\n            var tick = yield;\n            let forceIndex;\n            while (true) {\n                forceIndex = (tick.close - previousTick.close) * tick.volume;\n                previousTick = tick;\n                tick = yield emaForceIndex.nextValue(forceIndex);\n            }\n        })();\n        this.generator.next();\n        volumes.forEach((tick, index) => {\n            var result = this.generator.next({\n                close: closes[index],\n                volume: volumes[index]\n            });\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    ;\n    nextValue(price) {\n        let result = this.generator.next(price).value;\n        if (result != undefined) {\n            return result;\n        }\n    }\n    ;\n}\nForceIndex.calculate = forceindex;\nexport function forceindex(input) {\n    Indicator.reverseInputs(input);\n    var result = new ForceIndex(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class CCIInput extends IndicatorInput {\n}\n;\nexport class CCI extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var period = input.period;\n        var format = this.format;\n        let constant = .015;\n        var currentTpSet = new LinkedList(period);\n        ;\n        var tpSMACalculator = new SMA({ period: period, values: [], format: (v) => { return v; } });\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var tick = yield;\n            while (true) {\n                let tp = (tick.high + tick.low + tick.close) / 3;\n                currentTpSet.push(tp);\n                let smaTp = tpSMACalculator.nextValue(tp);\n                let meanDeviation = null;\n                let cci;\n                let sum = 0;\n                if (smaTp != undefined) {\n                    //First, subtract the most recent 20-period average of the typical price from each period's typical price. \n                    //Second, take the absolute values of these numbers.\n                    //Third,sum the absolute values. \n                    for (let x of currentTpSet.iterator()) {\n                        sum = sum + (Math.abs(x - smaTp));\n                    }\n                    //Fourth, divide by the total number of periods (20). \n                    meanDeviation = sum / period;\n                    cci = (tp - smaTp) / (constant * meanDeviation);\n                }\n                tick = yield cci;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    ;\n    nextValue(price) {\n        let result = this.generator.next(price).value;\n        if (result != undefined) {\n            return result;\n        }\n    }\n    ;\n}\nCCI.calculate = cci;\nexport function cci(input) {\n    Indicator.reverseInputs(input);\n    var result = new CCI(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nexport class AwesomeOscillatorInput extends IndicatorInput {\n}\nexport class AwesomeOscillator extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var fastPeriod = input.fastPeriod;\n        var slowPeriod = input.slowPeriod;\n        var slowSMA = new SMA({ values: [], period: slowPeriod });\n        var fastSMA = new SMA({ values: [], period: fastPeriod });\n        this.result = [];\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var medianPrice;\n            var slowSmaValue;\n            var fastSmaValue;\n            tick = yield;\n            while (true) {\n                medianPrice = (tick.high + tick.low) / 2;\n                slowSmaValue = slowSMA.nextValue(medianPrice);\n                fastSmaValue = fastSMA.nextValue(medianPrice);\n                if (slowSmaValue !== undefined && fastSmaValue !== undefined) {\n                    result = fastSmaValue - slowSmaValue;\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: lows[index],\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return this.format(result.value);\n        }\n    }\n    ;\n}\nAwesomeOscillator.calculate = awesomeoscillator;\nexport function awesomeoscillator(input) {\n    Indicator.reverseInputs(input);\n    var result = new AwesomeOscillator(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class VWAPInput extends IndicatorInput {\n}\n;\nexport class VWAP extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var volumes = input.volume;\n        var format = this.format;\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var tick = yield;\n            let cumulativeTotal = 0;\n            let cumulativeVolume = 0;\n            while (true) {\n                let typicalPrice = (tick.high + tick.low + tick.close) / 3;\n                let total = tick.volume * typicalPrice;\n                cumulativeTotal = cumulativeTotal + total;\n                cumulativeVolume = cumulativeVolume + tick.volume;\n                tick = yield cumulativeTotal / cumulativeVolume;\n                ;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index],\n                volume: volumes[index]\n            });\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    ;\n    nextValue(price) {\n        let result = this.generator.next(price).value;\n        if (result != undefined) {\n            return result;\n        }\n    }\n    ;\n}\nVWAP.calculate = vwap;\nexport function vwap(input) {\n    Indicator.reverseInputs(input);\n    var result = new VWAP(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class VolumeProfileInput extends IndicatorInput {\n}\nexport class VolumeProfileOutput {\n}\nexport function priceFallsBetweenBarRange(low, high, low1, high1) {\n    return (low <= low1 && high >= low1) || (low1 <= low && high1 >= low);\n}\nexport class VolumeProfile extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var closes = input.close;\n        var opens = input.open;\n        var volumes = input.volume;\n        var bars = input.noOfBars;\n        if (!((lows.length === highs.length) && (highs.length === closes.length) && (highs.length === volumes.length))) {\n            throw ('Inputs(low,high, close, volumes) not of equal size');\n        }\n        this.result = [];\n        var max = Math.max(...highs, ...lows, ...closes, ...opens);\n        var min = Math.min(...highs, ...lows, ...closes, ...opens);\n        var barRange = (max - min) / bars;\n        var lastEnd = min;\n        for (let i = 0; i < bars; i++) {\n            let rangeStart = lastEnd;\n            let rangeEnd = rangeStart + barRange;\n            lastEnd = rangeEnd;\n            let bullishVolume = 0;\n            let bearishVolume = 0;\n            let totalVolume = 0;\n            for (let priceBar = 0; priceBar < highs.length; priceBar++) {\n                let priceBarStart = lows[priceBar];\n                let priceBarEnd = highs[priceBar];\n                let priceBarOpen = opens[priceBar];\n                let priceBarClose = closes[priceBar];\n                let priceBarVolume = volumes[priceBar];\n                if (priceFallsBetweenBarRange(rangeStart, rangeEnd, priceBarStart, priceBarEnd)) {\n                    totalVolume = totalVolume + priceBarVolume;\n                    if (priceBarOpen > priceBarClose) {\n                        bearishVolume = bearishVolume + priceBarVolume;\n                    }\n                    else {\n                        bullishVolume = bullishVolume + priceBarVolume;\n                    }\n                }\n            }\n            this.result.push({\n                rangeStart, rangeEnd, bullishVolume, bearishVolume, totalVolume\n            });\n        }\n    }\n    ;\n    nextValue(price) {\n        throw ('Next value not supported for volume profile');\n    }\n    ;\n}\nVolumeProfile.calculate = volumeprofile;\nexport function volumeprofile(input) {\n    Indicator.reverseInputs(input);\n    var result = new VolumeProfile(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/4/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class TypicalPriceInput extends IndicatorInput {\n}\nexport class TypicalPrice extends Indicator {\n    constructor(input) {\n        super(input);\n        this.result = [];\n        this.generator = (function* () {\n            let priceInput = yield;\n            while (true) {\n                priceInput = yield (priceInput.high + priceInput.low + priceInput.close) / 3;\n            }\n        })();\n        this.generator.next();\n        input.low.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: input.high[index],\n                low: input.low[index],\n                close: input.close[index],\n            });\n            this.result.push(result.value);\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        return result;\n    }\n    ;\n}\nTypicalPrice.calculate = typicalprice;\nexport function typicalprice(input) {\n    Indicator.reverseInputs(input);\n    var result = new TypicalPrice(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/17/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { TypicalPrice } from '../chart_types/TypicalPrice';\nimport FixedSizeLinkedList from '../Utils/FixedSizeLinkedList';\nexport class MFIInput extends IndicatorInput {\n}\nexport class MFI extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var closes = input.close;\n        var volumes = input.volume;\n        var period = input.period;\n        var typicalPrice = new TypicalPrice({ low: [], high: [], close: [] });\n        var positiveFlow = new FixedSizeLinkedList(period, false, false, true);\n        var negativeFlow = new FixedSizeLinkedList(period, false, false, true);\n        if (!((lows.length === highs.length) && (highs.length === closes.length) && (highs.length === volumes.length))) {\n            throw ('Inputs(low,high, close, volumes) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var lastClose;\n            var positiveFlowForPeriod;\n            var rawMoneyFlow = 0;\n            var moneyFlowRatio;\n            var negativeFlowForPeriod;\n            let typicalPriceValue = null;\n            let prevousTypicalPrice = null;\n            tick = yield;\n            lastClose = tick.close; //Fist value \n            tick = yield;\n            while (true) {\n                var { high, low, close, volume } = tick;\n                var positionMoney = 0;\n                var negativeMoney = 0;\n                typicalPriceValue = typicalPrice.nextValue({ high, low, close });\n                rawMoneyFlow = typicalPriceValue * volume;\n                if ((typicalPriceValue != null) && (prevousTypicalPrice != null)) {\n                    typicalPriceValue > prevousTypicalPrice ? positionMoney = rawMoneyFlow : negativeMoney = rawMoneyFlow;\n                    positiveFlow.push(positionMoney);\n                    negativeFlow.push(negativeMoney);\n                    positiveFlowForPeriod = positiveFlow.periodSum;\n                    negativeFlowForPeriod = negativeFlow.periodSum;\n                    if ((positiveFlow.totalPushed >= period) && (positiveFlow.totalPushed >= period)) {\n                        moneyFlowRatio = positiveFlowForPeriod / negativeFlowForPeriod;\n                        result = 100 - 100 / (1 + moneyFlowRatio);\n                    }\n                }\n                prevousTypicalPrice = typicalPriceValue;\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: lows[index],\n                close: closes[index],\n                volume: volumes[index]\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(parseFloat(result.value.toFixed(2)));\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return (parseFloat(result.value.toFixed(2)));\n        }\n    }\n    ;\n}\nMFI.calculate = mfi;\nexport function mfi(input) {\n    Indicator.reverseInputs(input);\n    var result = new MFI(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/10/16.\n */\n\"use strict\";\nimport { SMA } from '../moving_averages/SMA';\nimport { RSI } from '../oscillators/RSI';\nimport { Stochastic } from '../momentum/Stochastic';\nexport class StochasticRsiInput extends IndicatorInput {\n}\n;\nexport class StochasticRSIOutput {\n}\n;\nexport class StochasticRSI extends Indicator {\n    constructor(input) {\n        super(input);\n        let closes = input.values;\n        let rsiPeriod = input.rsiPeriod;\n        let stochasticPeriod = input.stochasticPeriod;\n        let kPeriod = input.kPeriod;\n        let dPeriod = input.dPeriod;\n        let format = this.format;\n        this.result = [];\n        this.generator = (function* () {\n            let index = 1;\n            let rsi = new RSI({ period: rsiPeriod, values: [] });\n            let stochastic = new Stochastic({ period: stochasticPeriod, high: [], low: [], close: [], signalPeriod: kPeriod });\n            let dSma = new SMA({\n                period: dPeriod,\n                values: [],\n                format: (v) => { return v; }\n            });\n            let lastRSI, stochasticRSI, d, result;\n            var tick = yield;\n            while (true) {\n                lastRSI = rsi.nextValue(tick);\n                if (lastRSI !== undefined) {\n                    var stochasticInput = { high: lastRSI, low: lastRSI, close: lastRSI };\n                    stochasticRSI = stochastic.nextValue(stochasticInput);\n                    if (stochasticRSI !== undefined && stochasticRSI.d !== undefined) {\n                        d = dSma.nextValue(stochasticRSI.d);\n                        if (d !== undefined)\n                            result = {\n                                stochRSI: stochasticRSI.k,\n                                k: stochasticRSI.d,\n                                d: d\n                            };\n                    }\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        closes.forEach((tick, index) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(input) {\n        let nextResult = this.generator.next(input);\n        if (nextResult.value !== undefined)\n            return nextResult.value;\n    }\n    ;\n}\nStochasticRSI.calculate = stochasticrsi;\nexport function stochasticrsi(input) {\n    Indicator.reverseInputs(input);\n    var result = new StochasticRSI(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport FixedSizedLinkedList from './FixedSizeLinkedList';\nexport class HighestInput extends IndicatorInput {\n}\nexport class Highest extends Indicator {\n    constructor(input) {\n        super(input);\n        var values = input.values;\n        var period = input.period;\n        this.result = [];\n        var periodList = new FixedSizedLinkedList(period, true, false, false);\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var high;\n            tick = yield;\n            while (true) {\n                periodList.push(tick);\n                if (periodList.totalPushed >= period) {\n                    high = periodList.periodHigh;\n                }\n                tick = yield high;\n            }\n        })();\n        this.generator.next();\n        values.forEach((value, index) => {\n            var result = this.generator.next(value);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nHighest.calculate = highest;\nexport function highest(input) {\n    Indicator.reverseInputs(input);\n    var result = new Highest(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport FixedSizedLinkedList from './FixedSizeLinkedList';\nexport class LowestInput extends IndicatorInput {\n}\nexport class Lowest extends Indicator {\n    constructor(input) {\n        super(input);\n        var values = input.values;\n        var period = input.period;\n        this.result = [];\n        var periodList = new FixedSizedLinkedList(period, false, true, false);\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var high;\n            tick = yield;\n            while (true) {\n                periodList.push(tick);\n                if (periodList.totalPushed >= period) {\n                    high = periodList.periodLow;\n                }\n                tick = yield high;\n            }\n        })();\n        this.generator.next();\n        values.forEach((value, index) => {\n            var result = this.generator.next(value);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nLowest.calculate = lowest;\nexport function lowest(input) {\n    Indicator.reverseInputs(input);\n    var result = new Lowest(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport FixedSizedLinkedList from './FixedSizeLinkedList';\nexport class SumInput extends IndicatorInput {\n}\nexport class Sum extends Indicator {\n    constructor(input) {\n        super(input);\n        var values = input.values;\n        var period = input.period;\n        this.result = [];\n        var periodList = new FixedSizedLinkedList(period, false, false, true);\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var high;\n            tick = yield;\n            while (true) {\n                periodList.push(tick);\n                if (periodList.totalPushed >= period) {\n                    high = periodList.periodSum;\n                }\n                tick = yield high;\n            }\n        })();\n        this.generator.next();\n        values.forEach((value, index) => {\n            var result = this.generator.next(value);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nSum.calculate = sum;\nexport function sum(input) {\n    Indicator.reverseInputs(input);\n    var result = new Sum(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { CandleList } from '../StockData';\nimport { atr } from '../directionalmovement/ATR';\n/**\n * Created by AAravindan on 5/4/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class RenkoInput extends IndicatorInput {\n}\nclass <PERSON><PERSON> extends Indicator {\n    constructor(input) {\n        super(input);\n        var format = this.format;\n        let useATR = input.useATR;\n        let brickSize = input.brickSize || 0;\n        if (useATR) {\n            let atrResult = atr(Object.assign({}, input));\n            brickSize = atrResult[atrResult.length - 1];\n        }\n        this.result = new CandleList();\n        ;\n        if (brickSize === 0) {\n            console.error('Not enough data to calculate brickSize for renko when using ATR');\n            return;\n        }\n        let lastOpen = 0;\n        let lastHigh = 0;\n        let lastLow = Infinity;\n        let lastClose = 0;\n        let lastVolume = 0;\n        let lastTimestamp = 0;\n        this.generator = (function* () {\n            let candleData = yield;\n            while (true) {\n                //Calculating first bar\n                if (lastOpen === 0) {\n                    lastOpen = candleData.close;\n                    lastHigh = candleData.high;\n                    lastLow = candleData.low;\n                    lastClose = candleData.close;\n                    lastVolume = candleData.volume;\n                    lastTimestamp = candleData.timestamp;\n                    candleData = yield;\n                    continue;\n                }\n                let absoluteMovementFromClose = Math.abs(candleData.close - lastClose);\n                let absoluteMovementFromOpen = Math.abs(candleData.close - lastOpen);\n                if ((absoluteMovementFromClose >= brickSize) && (absoluteMovementFromOpen >= brickSize)) {\n                    let reference = absoluteMovementFromClose > absoluteMovementFromOpen ? lastOpen : lastClose;\n                    let calculated = {\n                        open: reference,\n                        high: lastHigh > candleData.high ? lastHigh : candleData.high,\n                        low: lastLow < candleData.Low ? lastLow : candleData.low,\n                        close: reference > candleData.close ? (reference - brickSize) : (reference + brickSize),\n                        volume: lastVolume + candleData.volume,\n                        timestamp: candleData.timestamp\n                    };\n                    lastOpen = calculated.open;\n                    lastHigh = calculated.close;\n                    lastLow = calculated.close;\n                    lastClose = calculated.close;\n                    lastVolume = 0;\n                    candleData = yield calculated;\n                }\n                else {\n                    lastHigh = lastHigh > candleData.high ? lastHigh : candleData.high;\n                    lastLow = lastLow < candleData.Low ? lastLow : candleData.low;\n                    lastVolume = lastVolume + candleData.volume;\n                    lastTimestamp = candleData.timestamp;\n                    candleData = yield;\n                }\n            }\n        })();\n        this.generator.next();\n        input.low.forEach((tick, index) => {\n            var result = this.generator.next({\n                open: input.open[index],\n                high: input.high[index],\n                low: input.low[index],\n                close: input.close[index],\n                volume: input.volume[index],\n                timestamp: input.timestamp[index]\n            });\n            if (result.value) {\n                this.result.open.push(result.value.open);\n                this.result.high.push(result.value.high);\n                this.result.low.push(result.value.low);\n                this.result.close.push(result.value.close);\n                this.result.volume.push(result.value.volume);\n                this.result.timestamp.push(result.value.timestamp);\n            }\n        });\n    }\n    nextValue(price) {\n        console.error('Cannot calculate next value on Renko, Every value has to be recomputed for every change, use calcualte method');\n        return null;\n    }\n    ;\n}\nRenko.calculate = renko;\nexport function renko(input) {\n    Indicator.reverseInputs(input);\n    var result = new Renko(input).result;\n    if (input.reversedInput) {\n        result.open.reverse();\n        result.high.reverse();\n        result.low.reverse();\n        result.close.reverse();\n        result.volume.reverse();\n        result.timestamp.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { CandleList } from '../StockData';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/4/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class HeikinAshiInput extends IndicatorInput {\n}\nexport class <PERSON>ikin<PERSON><PERSON> extends Indicator {\n    constructor(input) {\n        super(input);\n        var format = this.format;\n        this.result = new CandleList();\n        let lastOpen = null;\n        let lastHigh = 0;\n        let lastLow = Infinity;\n        let lastClose = 0;\n        let lastVolume = 0;\n        let lastTimestamp = 0;\n        this.generator = (function* () {\n            let candleData = yield;\n            let calculated = null;\n            while (true) {\n                if (lastOpen === null) {\n                    lastOpen = (candleData.close + candleData.open) / 2;\n                    lastHigh = candleData.high;\n                    lastLow = candleData.low;\n                    lastClose = (candleData.close + candleData.open + candleData.high + candleData.low) / 4;\n                    lastVolume = (candleData.volume || 0);\n                    lastTimestamp = (candleData.timestamp || 0);\n                    calculated = {\n                        open: lastOpen,\n                        high: lastHigh,\n                        low: lastLow,\n                        close: lastClose,\n                        volume: candleData.volume || 0,\n                        timestamp: (candleData.timestamp || 0)\n                    };\n                }\n                else {\n                    let newClose = (candleData.close + candleData.open + candleData.high + candleData.low) / 4;\n                    let newOpen = (lastOpen + lastClose) / 2;\n                    let newHigh = Math.max(newOpen, newClose, candleData.high);\n                    let newLow = Math.min(candleData.low, newOpen, newClose);\n                    calculated = {\n                        close: newClose,\n                        open: newOpen,\n                        high: newHigh,\n                        low: newLow,\n                        volume: (candleData.volume || 0),\n                        timestamp: (candleData.timestamp || 0)\n                    };\n                    lastClose = newClose;\n                    lastOpen = newOpen;\n                    lastHigh = newHigh;\n                    lastLow = newLow;\n                }\n                candleData = yield calculated;\n            }\n        })();\n        this.generator.next();\n        input.low.forEach((tick, index) => {\n            var result = this.generator.next({\n                open: input.open[index],\n                high: input.high[index],\n                low: input.low[index],\n                close: input.close[index],\n                volume: input.volume ? input.volume[index] : input.volume,\n                timestamp: input.timestamp ? input.timestamp[index] : input.timestamp\n            });\n            if (result.value) {\n                this.result.open.push(result.value.open);\n                this.result.high.push(result.value.high);\n                this.result.low.push(result.value.low);\n                this.result.close.push(result.value.close);\n                this.result.volume.push(result.value.volume);\n                this.result.timestamp.push(result.value.timestamp);\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        return result;\n    }\n    ;\n}\nHeikinAshi.calculate = heikinashi;\nexport function heikinashi(input) {\n    Indicator.reverseInputs(input);\n    var result = new HeikinAshi(input).result;\n    if (input.reversedInput) {\n        result.open.reverse();\n        result.high.reverse();\n        result.low.reverse();\n        result.close.reverse();\n        result.volume.reverse();\n        result.timestamp.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "export default class CandlestickFinder {\n    constructor() {\n        // if (new.target === Abstract) {\n        //     throw new TypeError(\"Abstract class\");\n        // }\n    }\n    approximateEqual(a, b) {\n        let left = parseFloat(Math.abs(a - b).toPrecision(4)) * 1;\n        let right = parseFloat((a * 0.001).toPrecision(4)) * 1;\n        return left <= right;\n    }\n    logic(data) {\n        throw \"this has to be implemented\";\n    }\n    getAllPatternIndex(data) {\n        if (data.close.length < this.requiredCount) {\n            console.warn('Data count less than data required for the strategy ', this.name);\n            return [];\n        }\n        if (data.reversedInput) {\n            data.open.reverse();\n            data.high.reverse();\n            data.low.reverse();\n            data.close.reverse();\n        }\n        let strategyFn = this.logic;\n        return this._generateDataForCandleStick(data)\n            .map((current, index) => {\n            return strategyFn.call(this, current) ? index : undefined;\n        }).filter((hasIndex) => {\n            return hasIndex;\n        });\n    }\n    hasPattern(data) {\n        if (data.close.length < this.requiredCount) {\n            console.warn('Data count less than data required for the strategy ', this.name);\n            return false;\n        }\n        if (data.reversedInput) {\n            data.open.reverse();\n            data.high.reverse();\n            data.low.reverse();\n            data.close.reverse();\n        }\n        let strategyFn = this.logic;\n        return strategyFn.call(this, this._getLastDataForCandleStick(data));\n    }\n    _getLastDataForCandleStick(data) {\n        let requiredCount = this.requiredCount;\n        if (data.close.length === requiredCount) {\n            return data;\n        }\n        else {\n            let returnVal = {\n                open: [],\n                high: [],\n                low: [],\n                close: []\n            };\n            let i = 0;\n            let index = data.close.length - requiredCount;\n            while (i < requiredCount) {\n                returnVal.open.push(data.open[index + i]);\n                returnVal.high.push(data.high[index + i]);\n                returnVal.low.push(data.low[index + i]);\n                returnVal.close.push(data.close[index + i]);\n                i++;\n            }\n            return returnVal;\n        }\n    }\n    _generateDataForCandleStick(data) {\n        let requiredCount = this.requiredCount;\n        let generatedData = data.close.map(function (currentData, index) {\n            let i = 0;\n            let returnVal = {\n                open: [],\n                high: [],\n                low: [],\n                close: []\n            };\n            while (i < requiredCount) {\n                returnVal.open.push(data.open[index + i]);\n                returnVal.high.push(data.high[index + i]);\n                returnVal.low.push(data.low[index + i]);\n                returnVal.close.push(data.close[index + i]);\n                i++;\n            }\n            return returnVal;\n        }).filter((val, index) => { return (index <= (data.close.length - requiredCount)); });\n        return generatedData;\n    }\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class MorningStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'MorningStar';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let isSmallBodyExists = ((firstdaysLow > seconddaysLow) &&\n            (firstdaysLow > seconddaysHigh));\n        let isThirdBullish = thirddaysOpen < thirddaysClose;\n        let gapExists = ((seconddaysHigh < firstdaysLow) &&\n            (seconddaysLow < firstdaysLow) &&\n            (thirddaysOpen > seconddaysHigh) &&\n            (seconddaysClose < thirddaysOpen));\n        let doesCloseAboveFirstMidpoint = thirddaysClose > firstdaysMidpoint;\n        return (isFirstBearish && isSmallBodyExists && gapExists && isThirdBullish && doesCloseAboveFirstMidpoint);\n    }\n}\nexport function morningstar(data) {\n    return new MorningStar().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishEngulfingPattern extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishEngulfingPattern';\n        this.requiredCount = 2;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBullishEngulfing = ((firstdaysClose < firstdaysOpen) &&\n            (firstdaysOpen > seconddaysOpen) &&\n            (firstdaysClose > seconddaysOpen) &&\n            (firstdaysOpen < seconddaysClose));\n        return (isBullishEngulfing);\n    }\n}\nexport function bullishengulfingpattern(data) {\n    return new BullishEngulfingPattern().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishHarami extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = \"<PERSON>ishH<PERSON><PERSON>\";\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBullishHaramiPattern = ((firstdaysOpen > seconddaysOpen) &&\n            (firstdaysClose < seconddaysOpen) &&\n            (firstdaysClose < seconddaysClose) &&\n            (firstdaysOpen > seconddaysLow) &&\n            (firstdaysHigh > seconddaysHigh));\n        return (isBullishHaramiPattern);\n    }\n}\nexport function bullishharami(data) {\n    return new BullishHarami().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishHaramiCross extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = 'BullishHaramiCross';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBullishHaramiCrossPattern = ((firstdaysOpen > seconddaysOpen) &&\n            (firstdaysClose < seconddaysOpen) &&\n            (firstdaysClose < seconddaysClose) &&\n            (firstdaysOpen > seconddaysLow) &&\n            (firstdaysHigh > seconddaysHigh));\n        let isSecondDayDoji = this.approximateEqual(seconddaysOpen, seconddaysClose);\n        return (isBullishHaramiCrossPattern && isSecondDayDoji);\n    }\n}\nexport function bullishharamicross(data) {\n    return new BullishHaramiCross().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class <PERSON>ji extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = '<PERSON><PERSON>';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isOpenEqualsClose = this.approximateEqual(daysOpen, daysClose);\n        let isHighEqualsOpen = isOpenEqualsClose && this.approximateEqual(daysOpen, daysHigh);\n        let isLowEqualsClose = isOpenEqualsClose && this.approximateEqual(daysClose, daysLow);\n        return (isOpenEqualsClose && isHighEqualsOpen == isLowEqualsClose);\n    }\n}\nexport function doji(data) {\n    return new Doji().hasPattern(data);\n}\n", "import Doji from './Doji';\nimport CandlestickFinder from './CandlestickFinder';\nexport default class MorningDojiStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'MorningDojiStar';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let dojiExists = new Doji().hasPattern({\n            \"open\": [seconddaysOpen],\n            \"close\": [seconddaysClose],\n            \"high\": [seconddaysHigh],\n            \"low\": [seconddaysLow]\n        });\n        let isThirdBullish = thirddaysOpen < thirddaysClose;\n        let gapExists = ((seconddaysHigh < firstdaysLow) &&\n            (seconddaysLow < firstdaysLow) &&\n            (thirddaysOpen > seconddaysHigh) &&\n            (seconddaysClose < thirddaysOpen));\n        let doesCloseAboveFirstMidpoint = thirddaysClose > firstdaysMidpoint;\n        return (isFirstBearish && dojiExists && isThirdBullish && gapExists &&\n            doesCloseAboveFirstMidpoint);\n    }\n}\nexport function morningdojistar(data) {\n    return new MorningDojiStar().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class DownsideTasukiGap extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 3;\n        this.name = 'DownsideTasukiGap';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let isSecondBearish = seconddaysClose < seconddaysOpen;\n        let isThirdBullish = thirddaysClose > thirddaysOpen;\n        let isFirstGapExists = seconddaysHigh < firstdaysLow;\n        let isDownsideTasukiGap = ((seconddaysOpen > thirddaysOpen) &&\n            (seconddaysClose < thirddaysOpen) &&\n            (thirddaysClose > seconddaysOpen) &&\n            (thirddaysClose < firstdaysClose));\n        return (isFirstBearish && isSecondBearish && isThirdBullish && isFirstGapExists && isDownsideTasukiGap);\n    }\n}\nexport function downsidetasukigap(data) {\n    return new DownsideTasukiGap().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishMarubozu extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishMarubozu';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBullishMarbozu = this.approximateEqual(daysClose, daysHigh) &&\n            this.approximateEqual(daysLow, daysOpen) &&\n            daysOpen < daysClose &&\n            daysOpen < daysHigh;\n        return (isBullishMarbozu);\n    }\n}\nexport function bullishmarubozu(data) {\n    return new BullishMarubozu().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class PiercingLine extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = 'PiercingLine';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isDowntrend = seconddaysLow < firstdaysLow;\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let isSecondBullish = seconddaysClose > seconddaysOpen;\n        let isPiercingLinePattern = ((firstdaysLow > seconddaysOpen) &&\n            (seconddaysClose > firstdaysMidpoint));\n        return (isDowntrend && isFirstBearish && isPiercingLinePattern && isSecondBullish);\n    }\n}\nexport function piercingline(data) {\n    return new PiercingLine().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class ThreeWhiteSoldiers extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'ThreeWhiteSoldiers';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let isUpTrend = seconddaysHigh > firstdaysHigh &&\n            thirddaysHigh > seconddaysHigh;\n        let isAllBullish = firstdaysOpen < firstdaysClose &&\n            seconddaysOpen < seconddaysClose &&\n            thirddaysOpen < thirddaysClose;\n        let doesOpenWithinPreviousBody = firstdaysClose > seconddaysOpen &&\n            seconddaysOpen < firstdaysHigh &&\n            seconddaysHigh > thirddaysOpen &&\n            thirddaysOpen < seconddaysClose;\n        return (isUpTrend && isAllBullish && doesOpenWithinPreviousBody);\n    }\n}\nexport function threewhitesoldiers(data) {\n    return new ThreeWhiteSoldiers().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishHammerStick extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishHammerStick';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBullishHammer = daysClose > daysOpen;\n        isBullishHammer = isBullishHammer && this.approximateEqual(daysClose, daysHigh);\n        isBullishHammer = isBullishHammer && (daysClose - daysOpen) <= 2 * (daysOpen - daysLow);\n        return isBullishHammer;\n    }\n}\nexport function bullishhammerstick(data) {\n    return new BullishHammerStick().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishInvertedHammerStick extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishInvertedHammerStick';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBullishInvertedHammer = daysClose > daysOpen;\n        isBullishInvertedHammer = isBullishInvertedHammer && this.approximateEqual(daysOpen, daysLow);\n        isBullishInvertedHammer = isBullishInvertedHammer && (daysClose - daysOpen) <= 2 * (daysHigh - daysClose);\n        return isBullishInvertedHammer;\n    }\n}\nexport function bullishinvertedhammerstick(data) {\n    return new BullishInvertedHammerStick().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishHammerStick extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishHammerStick';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBearishHammer = daysOpen > daysClose;\n        isBearishHammer = isBearishHammer && this.approximateEqual(daysOpen, daysHigh);\n        isBearishHammer = isBearishHammer && (daysOpen - daysClose) <= 2 * (daysClose - daysLow);\n        return isBearishHammer;\n    }\n}\nexport function bearishhammerstick(data) {\n    return new BearishHammerStick().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishInvertedHammerStick extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishInvertedHammerStick';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBearishInvertedHammer = daysOpen > daysClose;\n        isBearishInvertedHammer = isBearishInvertedHammer && this.approximateEqual(daysClose, daysLow);\n        isBearishInvertedHammer = isBearishInvertedHammer && (daysOpen - daysClose) <= 2 * (daysHigh - daysOpen);\n        return isBearishInvertedHammer;\n    }\n}\nexport function bearishinvertedhammerstick(data) {\n    return new BearishInvertedHammerStick().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nimport { bearishhammerstick } from './BearishHammerStick';\nimport { bearishinvertedhammerstick } from './BearishInvertedHammerStick';\nimport { bullishhammerstick } from './BullishHammerStick';\nimport { bullishinvertedhammerstick } from './BullishInvertedHammerStick';\nexport default class HammerPattern extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'HammerPattern';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        let isPattern = this.downwardTrend(data);\n        isPattern = isPattern && this.includesHammer(data);\n        isPattern = isPattern && this.hasConfirmation(data);\n        return isPattern;\n    }\n    downwardTrend(data, confirm = true) {\n        let end = confirm ? 3 : 4;\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, end), period: end - 1 });\n        let losses = averageloss({ values: data.close.slice(0, end), period: end - 1 });\n        // Downward trend, so more losses than gains\n        return losses > gains;\n    }\n    includesHammer(data, confirm = true) {\n        let start = confirm ? 3 : 4;\n        let end = confirm ? 4 : undefined;\n        let possibleHammerData = {\n            open: data.open.slice(start, end),\n            close: data.close.slice(start, end),\n            low: data.low.slice(start, end),\n            high: data.high.slice(start, end),\n        };\n        let isPattern = bearishhammerstick(possibleHammerData);\n        isPattern = isPattern || bearishinvertedhammerstick(possibleHammerData);\n        isPattern = isPattern || bullishhammerstick(possibleHammerData);\n        isPattern = isPattern || bullishinvertedhammerstick(possibleHammerData);\n        return isPattern;\n    }\n    hasConfirmation(data) {\n        let possibleHammer = {\n            open: data.open[3],\n            close: data.close[3],\n            low: data.low[3],\n            high: data.high[3],\n        };\n        let possibleConfirmation = {\n            open: data.open[4],\n            close: data.close[4],\n            low: data.low[4],\n            high: data.high[4],\n        };\n        // Confirmation candlestick is bullish\n        let isPattern = possibleConfirmation.open < possibleConfirmation.close;\n        return isPattern && possibleHammer.close < possibleConfirmation.close;\n    }\n}\nexport function hammerpattern(data) {\n    return new HammerPattern().hasPattern(data);\n}\n", "import HammerPattern from './HammerPattern';\nexport default class HammerPatternUnconfirmed extends HammerPattern {\n    constructor() {\n        super();\n        this.name = 'HammerPatternUnconfirmed';\n    }\n    logic(data) {\n        let isPattern = this.downwardTrend(data, false);\n        isPattern = isPattern && this.includesHammer(data, false);\n        return isPattern;\n    }\n}\nexport function hammerpatternunconfirmed(data) {\n    return new HammerPatternUnconfirmed().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nexport default class TweezerBottom extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'TweezerBottom';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        return this.downwardTrend(data) && data.low[3] == data.low[4];\n    }\n    downwardTrend(data) {\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, 3), period: 2 });\n        let losses = averageloss({ values: data.close.slice(0, 3), period: 2 });\n        // Downward trend, so more losses than gains\n        return losses > gains;\n    }\n}\nexport function tweezerbottom(data) {\n    return new TweezerBottom().hasPattern(data);\n}\n", "import MorningStar from './MorningStar';\nimport BullishEngulfingPattern from './BullishEngulfingPattern';\nimport <PERSON><PERSON><PERSON><PERSON><PERSON> from './BullishHarami';\nimport BullishHaramiCross from './BullishHaramiCross';\nimport MorningDojiStar from './MorningDojiStar';\nimport DownsideTasukiGap from './DownsideTasukiGap';\nimport BullishMarubozu from './BullishMarubozu';\nimport PiercingLine from './PiercingLine';\nimport ThreeWhiteSoldiers from './ThreeWhiteSoldiers';\nimport BullishHammerStick from './BullishHammerStick';\nimport BullishInvertedHammerStick from './BullishInvertedHammerStick';\nimport HammerPattern from './HammerPattern';\nimport HammerPatternUnconfirmed from './HammerPatternUnconfirmed';\nimport CandlestickFinder from './CandlestickFinder';\nimport TweezerBottom from './TweezerBottom';\nlet bullishPatterns = [\n    new BullishEngulfingPattern(),\n    new DownsideTasukiGap(),\n    new BullishHarami(),\n    new BullishHaramiCross(),\n    new MorningDojiStar(),\n    new MorningStar(),\n    new BullishMarubozu(),\n    new PiercingLine(),\n    new ThreeWhiteSoldiers(),\n    new BullishHammerStick(),\n    new BullishInvertedHammerStick(),\n    new HammerPattern(),\n    new HammerPatternUnconfirmed(),\n    new TweezerBottom()\n];\nexport default class BullishPatterns extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'Bullish Candlesticks';\n    }\n    hasPattern(data) {\n        return bullishPatterns.reduce(function (state, pattern) {\n            let result = pattern.hasPattern(data);\n            return state || result;\n        }, false);\n    }\n}\nexport function bullish(data) {\n    return new BullishPatterns().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishEngulfingPattern extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishEngulfingPattern';\n        this.requiredCount = 2;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBearishEngulfing = ((firstdaysClose > firstdaysOpen) &&\n            (firstdaysOpen < seconddaysOpen) &&\n            (firstdaysClose < seconddaysOpen) &&\n            (firstdaysOpen > seconddaysClose));\n        return (isBearishEngulfing);\n    }\n}\nexport function bearishengulfingpattern(data) {\n    return new BearishEngulfingPattern().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishHarami extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = '<PERSON><PERSON><PERSON><PERSON><PERSON>';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBearishHaramiPattern = ((firstdaysOpen < seconddaysOpen) &&\n            (firstdaysClose > seconddaysOpen) &&\n            (firstdaysClose > seconddaysClose) &&\n            (firstdaysOpen < seconddaysLow) &&\n            (firstdaysHigh > seconddaysHigh));\n        return (isBearishHaramiPattern);\n    }\n}\nexport function bearishharami(data) {\n    return new BearishHarami().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishHaramiCross extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = 'BearishHaramiCross';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBearishHaramiCrossPattern = ((firstdaysOpen < seconddaysOpen) &&\n            (firstdaysClose > seconddaysOpen) &&\n            (firstdaysClose > seconddaysClose) &&\n            (firstdaysOpen < seconddaysLow) &&\n            (firstdaysHigh > seconddaysHigh));\n        let isSecondDayDoji = this.approximateEqual(seconddaysOpen, seconddaysClose);\n        return (isBearishHaramiCrossPattern && isSecondDayDoji);\n    }\n}\nexport function bearishharamicross(data) {\n    return new BearishHaramiCross().hasPattern(data);\n}\n", "import Doji from './Doji';\nimport CandlestickFinder from './CandlestickFinder';\nexport default class EveningDojiStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'EveningDojiStar';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isFirstBullish = firstdaysClose > firstdaysOpen;\n        let dojiExists = new Doji().hasPattern({\n            \"open\": [seconddaysOpen],\n            \"close\": [seconddaysClose],\n            \"high\": [seconddaysHigh],\n            \"low\": [seconddaysLow]\n        });\n        let isThirdBearish = thirddaysOpen > thirddaysClose;\n        let gapExists = ((seconddaysHigh > firstdaysHigh) &&\n            (seconddaysLow > firstdaysHigh) &&\n            (thirddaysOpen < seconddaysLow) &&\n            (seconddaysClose > thirddaysOpen));\n        let doesCloseBelowFirstMidpoint = thirddaysClose < firstdaysMidpoint;\n        return (isFirstBullish && dojiExists && gapExists && isThirdBearish && doesCloseBelowFirstMidpoint);\n    }\n}\nexport function eveningdojistar(data) {\n    return new EveningDojiStar().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class EveningStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'EveningStar';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isFirstBullish = firstdaysClose > firstdaysOpen;\n        let isSmallBodyExists = ((firstdaysHigh < seconddaysLow) &&\n            (firstdaysHigh < seconddaysHigh));\n        let isThirdBearish = thirddaysOpen > thirddaysClose;\n        let gapExists = ((seconddaysHigh > firstdaysHigh) &&\n            (seconddaysLow > firstdaysHigh) &&\n            (thirddaysOpen < seconddaysLow) &&\n            (seconddaysClose > thirddaysOpen));\n        let doesCloseBelowFirstMidpoint = thirddaysClose < firstdaysMidpoint;\n        return (isFirstBullish && isSmallBodyExists && gapExists && isThirdBearish && doesCloseBelowFirstMidpoint);\n    }\n}\nexport function eveningstar(data) {\n    return new EveningStar().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishMarubozu extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishMarubozu';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBearishMarbozu = this.approximateEqual(daysOpen, daysHigh) &&\n            this.approximateEqual(daysLow, daysClose) &&\n            daysOpen > daysClose &&\n            daysOpen > daysLow;\n        return (isBearishMarbozu);\n    }\n}\nexport function bearishmarubozu(data) {\n    return new BearishMarubozu().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class ThreeBlackCrows extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'ThreeBlackCrows';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let isDownTrend = firstdaysLow > seconddaysLow &&\n            seconddaysLow > thirddaysLow;\n        let isAllBearish = firstdaysOpen > firstdaysClose &&\n            seconddaysOpen > seconddaysClose &&\n            thirddaysOpen > thirddaysClose;\n        let doesOpenWithinPreviousBody = firstdaysOpen > seconddaysOpen &&\n            seconddaysOpen > firstdaysClose &&\n            seconddaysOpen > thirddaysOpen &&\n            thirddaysOpen > seconddaysClose;\n        return (isDownTrend && isAllBearish && doesOpenWithinPreviousBody);\n    }\n}\nexport function threeblackcrows(data) {\n    return new ThreeBlackCrows().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nimport { bearishhammerstick } from './BearishHammerStick';\nimport { bullishhammerstick } from './BullishHammerStick';\nexport default class HangingMan extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'HangingMan';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        let isPattern = this.upwardTrend(data);\n        isPattern = isPattern && this.includesHammer(data);\n        isPattern = isPattern && this.hasConfirmation(data);\n        return isPattern;\n    }\n    upwardTrend(data, confirm = true) {\n        let end = confirm ? 3 : 4;\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, end), period: end - 1 });\n        let losses = averageloss({ values: data.close.slice(0, end), period: end - 1 });\n        // Upward trend, so more gains than losses\n        return gains > losses;\n    }\n    includesHammer(data, confirm = true) {\n        let start = confirm ? 3 : 4;\n        let end = confirm ? 4 : undefined;\n        let possibleHammerData = {\n            open: data.open.slice(start, end),\n            close: data.close.slice(start, end),\n            low: data.low.slice(start, end),\n            high: data.high.slice(start, end),\n        };\n        let isPattern = bearishhammerstick(possibleHammerData);\n        isPattern = isPattern || bullishhammerstick(possibleHammerData);\n        return isPattern;\n    }\n    hasConfirmation(data) {\n        let possibleHammer = {\n            open: data.open[3],\n            close: data.close[3],\n            low: data.low[3],\n            high: data.high[3],\n        };\n        let possibleConfirmation = {\n            open: data.open[4],\n            close: data.close[4],\n            low: data.low[4],\n            high: data.high[4],\n        };\n        // Confirmation candlestick is bearish\n        let isPattern = possibleConfirmation.open > possibleConfirmation.close;\n        return isPattern && possibleHammer.close > possibleConfirmation.close;\n    }\n}\nexport function hangingman(data) {\n    return new HangingMan().hasPattern(data);\n}\n", "import HangingMan from './HangingMan';\nexport default class HangingManUnconfirmed extends HangingMan {\n    constructor() {\n        super();\n        this.name = 'HangingManUnconfirmed';\n    }\n    logic(data) {\n        let isPattern = this.upwardTrend(data, false);\n        isPattern = isPattern && this.includesHammer(data, false);\n        return isPattern;\n    }\n}\nexport function hangingmanunconfirmed(data) {\n    return new HangingManUnconfirmed().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nimport { bearishinvertedhammerstick } from './BearishInvertedHammerStick';\nimport { bullishinvertedhammerstick } from './BullishInvertedHammerStick';\nexport default class ShootingStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'ShootingStar';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        let isPattern = this.upwardTrend(data);\n        isPattern = isPattern && this.includesHammer(data);\n        isPattern = isPattern && this.hasConfirmation(data);\n        return isPattern;\n    }\n    upwardTrend(data, confirm = true) {\n        let end = confirm ? 3 : 4;\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, end), period: end - 1 });\n        let losses = averageloss({ values: data.close.slice(0, end), period: end - 1 });\n        // Upward trend, so more gains than losses\n        return gains > losses;\n    }\n    includesHammer(data, confirm = true) {\n        let start = confirm ? 3 : 4;\n        let end = confirm ? 4 : undefined;\n        let possibleHammerData = {\n            open: data.open.slice(start, end),\n            close: data.close.slice(start, end),\n            low: data.low.slice(start, end),\n            high: data.high.slice(start, end),\n        };\n        let isPattern = bearishinvertedhammerstick(possibleHammerData);\n        isPattern = isPattern || bullishinvertedhammerstick(possibleHammerData);\n        return isPattern;\n    }\n    hasConfirmation(data) {\n        let possibleHammer = {\n            open: data.open[3],\n            close: data.close[3],\n            low: data.low[3],\n            high: data.high[3],\n        };\n        let possibleConfirmation = {\n            open: data.open[4],\n            close: data.close[4],\n            low: data.low[4],\n            high: data.high[4],\n        };\n        // Confirmation candlestick is bearish\n        let isPattern = possibleConfirmation.open > possibleConfirmation.close;\n        return isPattern && possibleHammer.close > possibleConfirmation.close;\n    }\n}\nexport function shootingstar(data) {\n    return new ShootingStar().hasPattern(data);\n}\n", "import ShootingStar from './ShootingStar';\nexport default class ShootingStarUnconfirmed extends ShootingStar {\n    constructor() {\n        super();\n        this.name = 'ShootingStarUnconfirmed';\n    }\n    logic(data) {\n        let isPattern = this.upwardTrend(data, false);\n        isPattern = isPattern && this.includesHammer(data, false);\n        return isPattern;\n    }\n}\nexport function shootingstarunconfirmed(data) {\n    return new ShootingStarUnconfirmed().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nexport default class TweezerTop extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'TweezerTop';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        return this.upwardTrend(data) && data.high[3] == data.high[4];\n    }\n    upwardTrend(data) {\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, 3), period: 2 });\n        let losses = averageloss({ values: data.close.slice(0, 3), period: 2 });\n        // Upward trend, so more gains than losses\n        return gains > losses;\n    }\n}\nexport function tweezertop(data) {\n    return new TweezerTop().hasPattern(data);\n}\n", "import BearishEngulfingPattern from './BearishEngulfingPattern';\nimport Bearish<PERSON><PERSON><PERSON> from './BearishHarami';\nimport BearishHaramiCross from './BearishHaramiCross';\nimport EveningDojiStar from './EveningDojiStar';\nimport EveningStar from './EveningStar';\nimport BearishMaru<PERSON>zu from './BearishMarubozu';\nimport ThreeBlackCrows from './ThreeBlackCrows';\nimport BearishHammerStick from './BearishHammerStick';\nimport BearishInvertedHammerStick from './BearishInvertedHammerStick';\nimport HangingMan from './HangingMan';\nimport HangingManUnconfirmed from './HangingManUnconfirmed';\nimport ShootingStar from './ShootingStar';\nimport ShootingStarUnconfirmed from './ShootingStarUnconfirmed';\nimport TweezerTop from './TweezerTop';\nimport CandlestickFinder from './CandlestickFinder';\nlet bearishPatterns = [\n    new BearishEngulfingPattern(),\n    new BearishHarami(),\n    new BearishHaramiCross(),\n    new EveningDojiStar(),\n    new EveningStar(),\n    new BearishMarubozu(),\n    new ThreeBlackCrows(),\n    new BearishHammerStick(),\n    new BearishInvertedHammerStick(),\n    new HangingMan(),\n    new HangingManUnconfirmed(),\n    new ShootingStar(),\n    new ShootingStarUnconfirmed(),\n    new TweezerTop()\n];\nexport default class BearishPatterns extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'Bearish Candlesticks';\n    }\n    hasPattern(data) {\n        return bearishPatterns.reduce(function (state, pattern) {\n            return state || pattern.hasPattern(data);\n        }, false);\n    }\n}\nexport function bearish(data) {\n    return new BearishPatterns().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport Doji from './Doji';\nexport default class AbandonedBaby extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'AbandonedBaby';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let dojiExists = new Doji().hasPattern({\n            \"open\": [seconddaysOpen],\n            \"close\": [seconddaysClose],\n            \"high\": [seconddaysHigh],\n            \"low\": [seconddaysLow]\n        });\n        let gapExists = ((seconddaysHigh < firstdaysLow) &&\n            (thirddaysLow > seconddaysHigh) &&\n            (thirddaysClose > thirddaysOpen));\n        let isThirdBullish = (thirddaysHigh < firstdaysOpen);\n        return (isFirstBearish && dojiExists && gapExists && isThirdBullish);\n    }\n}\nexport function abandonedbaby(data) {\n    return new AbandonedBaby().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class DarkCloudCover extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'DarkCloudCover';\n        this.requiredCount = 2;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let firstdayMidpoint = ((firstdaysClose + firstdaysOpen) / 2);\n        let isFirstBullish = firstdaysClose > firstdaysOpen;\n        let isSecondBearish = seconddaysClose < seconddaysOpen;\n        let isDarkCloudPattern = ((seconddaysOpen > firstdaysHigh) &&\n            (seconddaysClose < firstdayMidpoint) &&\n            (seconddaysClose > firstdaysOpen));\n        return (isFirstBullish && isSecondBearish && isDarkCloudPattern);\n    }\n}\nexport function darkcloudcover(data) {\n    return new DarkCloudCover().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class DragonFlyDoji extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 1;\n        this.name = 'DragonFlyDoji';\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isOpenEqualsClose = this.approximateEqual(daysOpen, daysClose);\n        let isHighEqualsOpen = isOpenEqualsClose && this.approximateEqual(daysOpen, daysHigh);\n        let isLowEqualsClose = isOpenEqualsClose && this.approximateEqual(daysClose, daysLow);\n        return (isOpenEqualsClose && isHighEqualsOpen && !isLowEqualsClose);\n    }\n}\nexport function dragonflydoji(data) {\n    return new DragonFlyDoji().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class GraveStoneDoji extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 1;\n        this.name = 'GraveStoneDoji';\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isOpenEqualsClose = this.approximateEqual(daysOpen, daysClose);\n        let isHighEqualsOpen = isOpenEqualsClose && this.approximateEqual(daysOpen, daysHigh);\n        let isLowEqualsClose = isOpenEqualsClose && this.approximateEqual(daysClose, daysLow);\n        return (isOpenEqualsClose && isLowEqualsClose && !isHighEqualsOpen);\n    }\n}\nexport function gravestonedoji(data) {\n    return new GraveStoneDoji().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishSpinningTop extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishSpinningTop';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let bodyLength = Math.abs(daysClose - daysOpen);\n        let upperShadowLength = Math.abs(daysHigh - daysClose);\n        let lowerShadowLength = Math.abs(daysOpen - daysLow);\n        let isBullishSpinningTop = bodyLength < upperShadowLength &&\n            bodyLength < lowerShadowLength;\n        return isBullishSpinningTop;\n    }\n}\nexport function bullishspinningtop(data) {\n    return new BullishSpinningTop().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishSpinningTop extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishSpinningTop';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let bodyLength = Math.abs(daysClose - daysOpen);\n        let upperShadowLength = Math.abs(daysHigh - daysOpen);\n        let lowerShadowLength = Math.abs(daysHigh - daysLow);\n        let isBearishSpinningTop = bodyLength < upperShadowLength &&\n            bodyLength < lowerShadowLength;\n        return isBearishSpinningTop;\n    }\n}\nexport function bearishspinningtop(data) {\n    return new BearishSpinningTop().hasPattern(data);\n}\n", "/**\n * Cal<PERSON>ultes the fibonacci retracements for given start and end points\n *\n * If calculating for up trend start should be low and end should be high and vice versa\n *\n * returns an array of retracements level containing [0 , 23.6, 38.2, 50, 61.8, 78.6, 100, 127.2, 161.8, 261.8, 423.6]\n *\n * @export\n * @param {number} start\n * @param {number} end\n * @returns {number[]}\n */\nexport function fibonacciretracement(start, end) {\n    let levels = [0, 23.6, 38.2, 50, 61.8, 78.6, 100, 127.2, 161.8, 261.8, 423.6];\n    let retracements;\n    if (start < end) {\n        retracements = levels.map(function (level) {\n            let calculated = end - Math.abs(start - end) * (level) / 100;\n            return calculated > 0 ? calculated : 0;\n        });\n    }\n    else {\n        retracements = levels.map(function (level) {\n            let calculated = end + Math.abs(start - end) * (level) / 100;\n            return calculated > 0 ? calculated : 0;\n        });\n    }\n    return retracements;\n}\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class IchimokuCloudInput extends IndicatorInput {\n    constructor() {\n        super(...arguments);\n        this.conversionPeriod = 9;\n        this.basePeriod = 26;\n        this.spanPeriod = 52;\n        this.displacement = 26;\n    }\n}\nexport class IchimokuCloudOutput {\n}\nexport class IchimokuCloud extends Indicator {\n    constructor(input) {\n        super(input);\n        this.result = [];\n        var defaults = {\n            conversionPeriod: 9,\n            basePeriod: 26,\n            spanPeriod: 52,\n            displacement: 26\n        };\n        var params = Object.assign({}, defaults, input);\n        var currentConversionData = new LinkedList(params.conversionPeriod * 2, true, true, false);\n        var currentBaseData = new LinkedList(params.basePeriod * 2, true, true, false);\n        var currenSpanData = new LinkedList(params.spanPeriod * 2, true, true, false);\n        this.generator = (function* () {\n            let result;\n            let tick;\n            let period = Math.max(params.conversionPeriod, params.basePeriod, params.spanPeriod, params.displacement);\n            let periodCounter = 1;\n            tick = yield;\n            while (true) {\n                // Keep a list of lows/highs for the max period\n                currentConversionData.push(tick.high);\n                currentConversionData.push(tick.low);\n                currentBaseData.push(tick.high);\n                currentBaseData.push(tick.low);\n                currenSpanData.push(tick.high);\n                currenSpanData.push(tick.low);\n                if (periodCounter < period) {\n                    periodCounter++;\n                }\n                else {\n                    // Tenkan-sen (ConversionLine): (9-period high + 9-period low)/2))\n                    let conversionLine = (currentConversionData.periodHigh + currentConversionData.periodLow) / 2;\n                    // Kijun-sen (Base Line): (26-period high + 26-period low)/2))\n                    let baseLine = (currentBaseData.periodHigh + currentBaseData.periodLow) / 2;\n                    // Senkou Span A (Leading Span A): (Conversion Line + Base Line)/2))\n                    let spanA = (conversionLine + baseLine) / 2;\n                    // Senkou Span B (Leading Span B): (52-period high + 52-period low)/2))\n                    let spanB = (currenSpanData.periodHigh + currenSpanData.periodLow) / 2;\n                    // Senkou Span A / Senkou Span B offset by 26 periods\n                    // if(spanCounter < params.displacement) {\n                    // \tspanCounter++\n                    // } else {\n                    // \tspanA = spanAs.shift()\n                    // \tspanB = spanBs.shift()\n                    // }\n                    result = {\n                        conversion: conversionLine,\n                        base: baseLine,\n                        spanA: spanA,\n                        spanB: spanB\n                    };\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        input.low.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: input.high[index],\n                low: input.low[index],\n            });\n            if (result.value) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n}\nIchimokuCloud.calculate = ichimokucloud;\nexport function ichimokucloud(input) {\n    Indicator.reverseInputs(input);\n    var result = new IchimokuCloud(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport { EMA } from '../moving_averages/EMA';\nimport { ATR } from '../directionalmovement/ATR';\nexport class KeltnerChannelsInput extends IndicatorInput {\n    constructor() {\n        super(...arguments);\n        this.maPeriod = 20;\n        this.atrPeriod = 10;\n        this.useSMA = false;\n        this.multiplier = 1;\n    }\n}\nexport class KeltnerChannelsOutput extends IndicatorInput {\n}\n;\nexport class KeltnerChannels extends Indicator {\n    constructor(input) {\n        super(input);\n        var maType = input.useSMA ? SMA : EMA;\n        var maProducer = new maType({ period: input.maPeriod, values: [], format: (v) => { return v; } });\n        var atrProducer = new ATR({ period: input.atrPeriod, high: [], low: [], close: [], format: (v) => { return v; } });\n        var tick;\n        this.result = [];\n        this.generator = (function* () {\n            var KeltnerChannelsOutput;\n            var result;\n            tick = yield;\n            while (true) {\n                var { close } = tick;\n                var ma = maProducer.nextValue(close);\n                var atr = atrProducer.nextValue(tick);\n                if (ma != undefined && atr != undefined) {\n                    result = {\n                        middle: ma,\n                        upper: ma + (input.multiplier * (atr)),\n                        lower: ma - (input.multiplier * (atr))\n                    };\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        var highs = input.high;\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: input.low[index],\n                close: input.close[index],\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nKeltnerChannels.calculate = keltnerchannels;\nexport function keltnerchannels(input) {\n    Indicator.reverseInputs(input);\n    var result = new KeltnerChannels(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { ATR } from '../directionalmovement/ATR';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class ChandelierExitInput extends IndicatorInput {\n    constructor() {\n        super(...arguments);\n        this.period = 22;\n        this.multiplier = 3;\n    }\n}\nexport class ChandelierExitOutput extends IndicatorInput {\n}\n;\nexport class ChandelierExit extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var closes = input.close;\n        this.result = [];\n        var atrProducer = new ATR({ period: input.period, high: [], low: [], close: [], format: (v) => { return v; } });\n        var dataCollector = new LinkedList(input.period * 2, true, true, false);\n        this.generator = (function* () {\n            var result;\n            var tick = yield;\n            var atr;\n            while (true) {\n                var { high, low } = tick;\n                dataCollector.push(high);\n                dataCollector.push(low);\n                atr = atrProducer.nextValue(tick);\n                if ((dataCollector.totalPushed >= (2 * input.period)) && atr != undefined) {\n                    result = {\n                        exitLong: dataCollector.periodHigh - atr * input.multiplier,\n                        exitShort: dataCollector.periodLow + atr * input.multiplier\n                    };\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: lows[index],\n                close: closes[index],\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nChandelierExit.calculate = chandelierexit;\nexport function chandelierexit(input) {\n    Indicator.reverseInputs(input);\n    var result = new ChandelierExit(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class CrossInput extends IndicatorInput {\n    constructor(lineA, lineB) {\n        super();\n        this.lineA = lineA;\n        this.lineB = lineB;\n    }\n}\nexport class CrossUp extends Indicator {\n    constructor(input) {\n        super(input);\n        this.lineA = input.lineA;\n        this.lineB = input.lineB;\n        var currentLineA = [];\n        var currentLineB = [];\n        const genFn = (function* () {\n            var current = yield;\n            var result = false;\n            while (true) {\n                currentLineA.unshift(current.valueA);\n                currentLineB.unshift(current.valueB);\n                result = current.valueA > current.valueB;\n                var pointer = 1;\n                while (result === true && currentLineA[pointer] >= currentLineB[pointer]) {\n                    if (currentLineA[pointer] > currentLineB[pointer]) {\n                        result = false;\n                    }\n                    else if (currentLineA[pointer] < currentLineB[pointer]) {\n                        result = true;\n                    }\n                    else if (currentLineA[pointer] === currentLineB[pointer]) {\n                        pointer += 1;\n                    }\n                }\n                if (result === true) {\n                    currentLineA = [current.valueA];\n                    currentLineB = [current.valueB];\n                }\n                current = yield result;\n            }\n        });\n        this.generator = genFn();\n        this.generator.next();\n        this.result = [];\n        this.lineA.forEach((value, index) => {\n            var result = this.generator.next({\n                valueA: this.lineA[index],\n                valueB: this.lineB[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    static reverseInputs(input) {\n        if (input.reversedInput) {\n            input.lineA ? input.lineA.reverse() : undefined;\n            input.lineB ? input.lineB.reverse() : undefined;\n        }\n    }\n    nextValue(valueA, valueB) {\n        return this.generator.next({\n            valueA: valueA,\n            valueB: valueB\n        }).value;\n    }\n    ;\n}\nCrossUp.calculate = crossUp;\nexport function crossUp(input) {\n    Indicator.reverseInputs(input);\n    var result = new CrossUp(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class CrossInput extends IndicatorInput {\n    constructor(lineA, lineB) {\n        super();\n        this.lineA = lineA;\n        this.lineB = lineB;\n    }\n}\nexport class CrossDown extends Indicator {\n    constructor(input) {\n        super(input);\n        this.lineA = input.lineA;\n        this.lineB = input.lineB;\n        var currentLineA = [];\n        var currentLineB = [];\n        const genFn = (function* () {\n            var current = yield;\n            var result = false;\n            while (true) {\n                currentLineA.unshift(current.valueA);\n                currentLineB.unshift(current.valueB);\n                result = current.valueA < current.valueB;\n                var pointer = 1;\n                while (result === true && currentLineA[pointer] <= currentLineB[pointer]) {\n                    if (currentLineA[pointer] < currentLineB[pointer]) {\n                        result = false;\n                    }\n                    else if (currentLineA[pointer] > currentLineB[pointer]) {\n                        result = true;\n                    }\n                    else if (currentLineA[pointer] === currentLineB[pointer]) {\n                        pointer += 1;\n                    }\n                }\n                if (result === true) {\n                    currentLineA = [current.valueA];\n                    currentLineB = [current.valueB];\n                }\n                current = yield result;\n            }\n        });\n        this.generator = genFn();\n        this.generator.next();\n        this.result = [];\n        this.lineA.forEach((value, index) => {\n            var result = this.generator.next({\n                valueA: this.lineA[index],\n                valueB: this.lineB[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    static reverseInputs(input) {\n        if (input.reversedInput) {\n            input.lineA ? input.lineA.reverse() : undefined;\n            input.lineB ? input.lineB.reverse() : undefined;\n        }\n    }\n    nextValue(valueA, valueB) {\n        return this.generator.next({\n            valueA: valueA,\n            valueB: valueB\n        }).value;\n    }\n    ;\n}\nCrossDown.calculate = crossDown;\nexport function crossDown(input) {\n    Indicator.reverseInputs(input);\n    var result = new CrossDown(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n"], "mappings": ";;;AAAA,IAAM,OAAN,MAAW;AAAA,EACP,YAAY,MAAM,MAAM,MAAM;AAC1B,SAAK,OAAO;AACZ,QAAI;AACA,WAAK,OAAO;AAChB,SAAK,OAAO;AACZ,QAAI;AACA,WAAK,OAAO;AAChB,SAAK,OAAO;AAAA,EAChB;AACJ;AACO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,OAAO;AACP,WAAO,KAAK,SAAS,KAAK,MAAM;AAAA,EACpC;AAAA,EACA,IAAI,OAAO;AACP,WAAO,KAAK,SAAS,KAAK,MAAM;AAAA,EACpC;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK,YAAY,KAAK,SAAS;AAAA,EAC1C;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,SAAK,QAAQ,IAAI,KAAK,MAAM,KAAK,KAAK;AACtC,QAAI,KAAK,YAAY,GAAG;AACpB,WAAK,QAAQ,KAAK;AAClB,WAAK,WAAW,KAAK;AACrB,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,SAAK;AAAA,EACT;AAAA,EACA,MAAM;AACF,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,YAAY,GAAG;AACpB;AAAA,IACJ;AACA,SAAK;AACL,QAAI,KAAK,YAAY,GAAG;AACpB,WAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ;AACvD,aAAO,KAAK;AAAA,IAChB;AACA,SAAK,QAAQ,KAAK;AAClB,SAAK,MAAM,OAAO;AAClB,QAAI,KAAK,aAAa,MAAM;AACxB,WAAK,WAAW,KAAK;AACrB,WAAK,QAAQ;AAAA,IACjB;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ;AACJ,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,YAAY,GAAG;AACpB;AAAA,IACJ;AACA,SAAK;AACL,QAAI,KAAK,YAAY,GAAG;AACpB,WAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ;AACvD,aAAO,KAAK;AAAA,IAChB;AACA,SAAK,QAAQ,KAAK,MAAM;AACxB,QAAI,KAAK,aAAa,MAAM;AACxB,WAAK,WAAW,KAAK;AACrB,WAAK,QAAQ,KAAK,SAAS;AAAA,IAC/B;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ,MAAM;AACV,SAAK,QAAQ,IAAI,KAAK,MAAM,QAAW,KAAK,KAAK;AACjD,QAAI,KAAK,YAAY,GAAG;AACpB,WAAK,QAAQ,KAAK;AAClB,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,SAAK;AAAA,EACT;AAAA,EACA,iBAAiB;AACb,QAAI,UAAU,KAAK;AACnB,QAAI,YAAY,KAAK,SAAS,KAAK,UAAU,GAAG;AAC5C,aAAO,WAAW,QAAQ;AAAA,IAC9B;AAEA,QAAI,YAAY,KAAK,OAAO;AACxB,WAAK,QAAQ,QAAQ;AACrB,WAAK,MAAM,OAAO;AAClB,WAAK,WAAW,KAAK;AAAA,IACzB,OACK;AACD,cAAQ,KAAK,OAAO,QAAQ;AAC5B,cAAQ,KAAK,OAAO,QAAQ;AAC5B,WAAK,WAAW,QAAQ;AAAA,IAC5B;AACA,SAAK,QAAQ,KAAK,SAAS;AAE3B,YAAQ,OAAO,KAAK;AACpB,YAAQ,OAAO;AACf,SAAK,MAAM,OAAO;AAClB,SAAK,QAAQ;AACb,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,gBAAgB;AACZ,QAAI,UAAU,KAAK;AACnB,QAAI,KAAK,YAAY,GAAG;AACpB;AAAA,IACJ;AACA,SAAK;AACL,QAAI,KAAK,YAAY,GAAG;AACpB,WAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ;AACvD,aAAO,QAAQ;AAAA,IACnB;AACA,QAAI,YAAY,KAAK,OAAO;AACxB,WAAK,QAAQ,QAAQ;AACrB,WAAK,MAAM,OAAO;AAClB,WAAK,WAAW,KAAK;AAAA,IACzB,WACS,YAAY,KAAK,OAAO;AAC7B,WAAK,QAAQ,QAAQ;AACrB,WAAK,MAAM,OAAO;AAClB,WAAK,WAAW,KAAK;AAAA,IACzB,OACK;AACD,cAAQ,KAAK,OAAO,QAAQ;AAC5B,cAAQ,KAAK,OAAO,QAAQ;AAC5B,WAAK,WAAW,QAAQ;AAAA,IAC5B;AACA,SAAK,QAAQ,KAAK,SAAS;AAC3B,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,cAAc;AACV,SAAK,WAAW,KAAK,QAAQ,KAAK;AAClC,WAAO;AAAA,EACX;AAAA,EACA,OAAO;AACH,QAAI,OAAO,KAAK;AAChB,QAAI,SAAS,QAAW;AACpB,WAAK,QAAQ,KAAK;AAClB,WAAK,WAAW;AAChB,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AACJ;;;AC3IA,IAAqB,sBAArB,cAAiD,WAAW;AAAA,EACxD,YAAY,MAAM,cAAc,aAAa,aAAa;AACtD,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,QAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACnC,YAAO;AAAA,IACX;AACA,SAAK,QAAQ,KAAK;AAClB,SAAK,OAAO,SAAU,MAAM;AACxB,WAAK,IAAI,IAAI;AACb,WAAK;AAAA,IACT;AAAA,EACJ;AAAA,EACA,IAAI,MAAM;AACN,QAAI,KAAK,WAAW,KAAK,MAAM;AAC3B,WAAK,YAAY,KAAK,MAAM;AAC5B,WAAK,MAAM,IAAI;AAEf,UAAI,KAAK;AACL,YAAI,KAAK,aAAa,KAAK;AACvB,eAAK,oBAAoB;AAAA;AACjC,UAAI,KAAK;AACL,YAAI,KAAK,aAAa,KAAK;AACvB,eAAK,mBAAmB;AAAA;AAChC,UAAI,KAAK,aAAa;AAClB,aAAK,YAAY,KAAK,YAAY,KAAK;AAAA,MAC3C;AAAA,IACJ,OACK;AACD,WAAK,MAAM,IAAI;AAAA,IACnB;AAEA,QAAI,KAAK;AACL,UAAI,KAAK,cAAc;AACnB,QAAC,KAAK,aAAa;AAAA;AAC3B,QAAI,KAAK;AACL,UAAI,KAAK,aAAa;AAClB,QAAC,KAAK,YAAY;AAAA;AAC1B,QAAI,KAAK,aAAa;AAClB,WAAK,YAAY,KAAK,YAAY;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,CAAC,WAAW;AACR,SAAK,YAAY;AACjB,WAAO,KAAK,KAAK,GAAG;AAChB,YAAM,KAAK;AAAA,IACf;AAAA,EACJ;AAAA,EACA,sBAAsB;AAClB,SAAK,YAAY;AACjB,QAAI,KAAK,KAAK;AACV,WAAK,aAAa,KAAK;AAC3B,WAAO,KAAK,KAAK,GAAG;AAChB,UAAI,KAAK,cAAc,KAAK,SAAS;AACjC,aAAK,aAAa,KAAK;AAAA,MAC3B;AACA;AAAA,IACJ;AACA;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,SAAK,YAAY;AACjB,QAAI,KAAK,KAAK;AACV,WAAK,YAAY,KAAK;AAC1B,WAAO,KAAK,KAAK,GAAG;AAChB,UAAI,KAAK,aAAa,KAAK,SAAS;AAChC,aAAK,YAAY,KAAK;AAAA,MAC1B;AACA;AAAA,IACJ;AACA;AAAA,EACJ;AACJ;;;AC1EO,IAAM,aAAN,MAAiB;AACxB;AACO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,OAAO,CAAC;AACb,SAAK,OAAO,CAAC;AACb,SAAK,MAAM,CAAC;AACZ,SAAK,QAAQ,CAAC;AACd,SAAK,SAAS,CAAC;AACf,SAAK,YAAY,CAAC;AAAA,EACtB;AACJ;;;ACpBA,IAAI,SAAS,CAAC;AACP,SAAS,UAAU,KAAK,OAAO;AAClC,SAAO,GAAG,IAAI;AAClB;AACO,SAAS,UAAU,KAAK;AAC3B,SAAO,OAAO,GAAG;AACrB;;;ACLO,SAAS,OAAO,GAAG;AACtB,MAAI,YAAY,UAAU,WAAW;AACrC,MAAI,WAAW;AACX,WAAO,WAAW,EAAE,YAAY,SAAS,CAAC;AAAA,EAC9C;AACA,SAAO;AACX;;;ACNO,IAAM,iBAAN,MAAqB;AAC5B;AAGO,IAAM,YAAN,MAAgB;AAAA,EACnB,YAAY,OAAO;AACf,SAAK,SAAS,MAAM,UAAU;AAAA,EAClC;AAAA,EACA,OAAO,cAAc,OAAO;AACxB,QAAI,MAAM,eAAe;AACrB,YAAM,SAAS,MAAM,OAAO,QAAQ,IAAI;AACxC,YAAM,OAAO,MAAM,KAAK,QAAQ,IAAI;AACpC,YAAM,OAAO,MAAM,KAAK,QAAQ,IAAI;AACpC,YAAM,MAAM,MAAM,IAAI,QAAQ,IAAI;AAClC,YAAM,QAAQ,MAAM,MAAM,QAAQ,IAAI;AACtC,YAAM,SAAS,MAAM,OAAO,QAAQ,IAAI;AACxC,YAAM,YAAY,MAAM,UAAU,QAAQ,IAAI;AAAA,IAClD;AAAA,EACJ;AAAA,EACA,YAAY;AACR,WAAO,KAAK;AAAA,EAChB;AACJ;;;ACXO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,SAAS,MAAM;AACpB,SAAK,QAAQ,MAAM;AACnB,QAAI,QAAS,WAAW,QAAQ;AAC5B,UAAI,OAAO,IAAI,WAAW;AAC1B,UAAIA,OAAM;AACV,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI;AACJ,WAAK,KAAK,CAAC;AACX,aAAO,MAAM;AACT,YAAI,UAAU,QAAQ;AAClB;AACA,eAAK,KAAK,OAAO;AACjB,UAAAA,OAAMA,OAAM;AAAA,QAChB,OACK;AACD,UAAAA,OAAMA,OAAM,KAAK,MAAM,IAAI;AAC3B,mBAAWA,OAAO;AAClB,eAAK,KAAK,OAAO;AAAA,QACrB;AACA,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ;AACA,SAAK,YAAY,MAAM,KAAK,MAAM;AAClC,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,MAAM,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU;AACV,aAAO,KAAK,OAAO,MAAM;AAAA,EACjC;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC9DO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,aAAa,MAAM;AACvB,QAAI,WAAY,KAAK,SAAS;AAC9B,QAAIC;AACJ,SAAK,SAAS,CAAC;AACf,IAAAA,OAAM,IAAI,IAAI,EAAE,QAAgB,QAAQ,CAAC,EAAE,CAAC;AAC5C,QAAI,QAAS,aAAa;AACtB,UAAI,OAAO;AACX,UAAI;AACJ,aAAO,MAAM;AACT,YAAI,YAAY,UAAa,SAAS,QAAW;AAC7C,qBAAY,OAAO,WAAW,WAAY;AAC1C,iBAAO,MAAM;AAAA,QACjB,OACK;AACD,iBAAO;AACP,oBAAUA,KAAI,UAAU,IAAI;AAC5B,cAAI;AACA,mBAAO,MAAM;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,YAAY,MAAM;AACvB,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,KAAK;AACpB,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU;AACV,aAAO,KAAK,OAAO,MAAM;AAAA,EACjC;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AClDO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,aAAa,MAAM;AACvB,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,OAAO,IAAI,WAAW;AAC1B,UAAI,cAAc,UAAU,SAAS,KAAK;AAC1C,aAAO,MAAM;AACT,YAAK,KAAK,SAAU,QAAQ;AACxB,eAAK,KAAK,KAAK;AAAA,QACnB,OACK;AACD,eAAK,YAAY;AACjB,cAAI,SAAS;AACb,mBAAS,IAAI,GAAG,KAAK,QAAQ,KAAK;AAC9B,qBAAS,SAAU,KAAK,KAAK,IAAI,IAAK;AAAA,UAC1C;AACA,cAAI,OAAO,MAAM;AACjB,eAAK,MAAM;AACX,eAAK,KAAK,IAAI;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,eAAW,QAAQ,CAAC,MAAM,UAAU;AAChC,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU;AACV,aAAO,KAAK,OAAO,MAAM;AAAA,EACjC;AAEJ;AACA,IAAI,YAAY;AAET,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACpDO,IAAM,OAAN,cAAmB,UAAU;AAAA,EAChC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,aAAa,MAAM;AACvB,QAAI,WAAW,IAAI;AACnB,QAAIC;AACJ,SAAK,SAAS,CAAC;AACf,IAAAA,OAAM,IAAI,IAAI,EAAE,QAAgB,QAAQ,CAAC,EAAE,CAAC;AAC5C,QAAI,QAAS,aAAa;AACtB,UAAI,OAAO;AACX,UAAI;AACJ,aAAO,MAAM;AACT,YAAI,YAAY,UAAa,SAAS,QAAW;AAC7C,qBAAY,OAAO,WAAW,WAAY;AAC1C,iBAAO,MAAM;AAAA,QACjB,OACK;AACD,iBAAO;AACP,oBAAUA,KAAI,UAAU,IAAI;AAC5B,cAAI,YAAY;AACZ,mBAAO,MAAM;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,YAAY,MAAM;AACvB,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,KAAK;AACpB,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU;AACV,aAAO,KAAK,OAAO,MAAM;AAAA,EACjC;AAEJ;AACA,KAAK,YAAY;AACV,SAAS,KAAK,OAAO;AACxB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,KAAK,KAAK,EAAE;AAC7B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACrCO,IAAM,OAAN,cAAmB,UAAU;AAAA,EAChC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,mBAAmB,MAAM,qBAAqB,MAAM;AACxD,QAAI,eAAe,MAAM,iBAAiB,MAAM;AAChD,QAAI,iBAAiB,IAAI,iBAAiB,EAAE,QAAQ,MAAM,YAAY,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChH,QAAI,iBAAiB,IAAI,iBAAiB,EAAE,QAAQ,MAAM,YAAY,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChH,QAAI,mBAAmB,IAAI,aAAa,EAAE,QAAQ,MAAM,cAAc,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChH,QAAIC,UAAS,KAAK;AAClB,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAIC,OAAM,QAAQ,WAAW,MAAM;AACnC,aAAO,MAAM;AACT,YAAI,QAAQ,MAAM,YAAY;AAC1B,iBAAO;AACP,iBAAO,eAAe,UAAU,IAAI;AACpC,iBAAO,eAAe,UAAU,IAAI;AACpC;AACA;AAAA,QACJ;AACA,YAAI,QAAQ,MAAM;AACd,UAAAA,QAAO,OAAO;AACd,mBAAS,iBAAiB,UAAUA,KAAI;AAAA,QAC5C;AACA,oBAAYA,QAAO;AACnB,eAAO,MAAO;AAAA;AAAA;AAAA,UAGV,MAAMD,QAAOC,KAAI;AAAA,UACjB,QAAQ,SAASD,QAAO,MAAM,IAAI;AAAA,UAClC,WAAW,MAAM,SAAS,IAAI,SAAYA,QAAO,SAAS;AAAA,QAC9D;AACA,eAAO,eAAe,UAAU,IAAI;AACpC,eAAO,eAAe,UAAU,IAAI;AAAA,MACxC;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,OAAO,QAAQ,CAAC,SAAS;AAC3B,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,WAAO;AAAA,EACX;AAEJ;AACA,KAAK,YAAY;AACV,SAAS,KAAK,OAAO;AACxB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,KAAK,KAAK,EAAE;AAC7B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC1EO,IAAM,cAAN,cAA0B,UAAU;AAAA,EACvC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAIE,UAAS,KAAK;AAClB,SAAK,YAAa,WAAWC,SAAQ;AACjC,UAAI,eAAe;AACnB,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI;AACJ,UAAI;AACJ,UAAI,YAAY;AAChB,qBAAe;AACf,aAAO,MAAM;AACT,eAAO,eAAe;AACtB,eAAO,OAAO,IAAI,OAAO;AACzB,YAAI,OAAO,GAAG;AACV,oBAAU,UAAU;AAAA,QACxB;AACA,YAAI,UAAUA,SAAQ;AAClB;AAAA,QACJ,WACS,YAAY,QAAW;AAC5B,oBAAU,UAAUA;AAAA,QACxB,OACK;AACD,qBAAY,WAAWA,UAAS,KAAM,QAAQA;AAAA,QAClD;AACA,oBAAY;AACZ,kBAAW,YAAY,SAAaD,QAAO,OAAO,IAAI;AACtD,uBAAe,MAAM;AAAA,MACzB;AAAA,IACJ,EAAG,MAAM;AACT,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS,CAAC;AACf,WAAO,QAAQ,CAAC,SAAS;AACrB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;AACA,YAAY,YAAY;AACjB,SAAS,YAAY,OAAO;AAC/B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,YAAY,KAAK,EAAE;AACpC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACzDO,IAAM,cAAN,cAA0B,UAAU;AAAA,EACvC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAIE,UAAS,KAAK;AAClB,SAAK,YAAa,WAAWC,SAAQ;AACjC,UAAI,eAAe;AACnB,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI;AACJ,UAAI;AACJ,UAAI,YAAY;AAChB,qBAAe;AACf,aAAO,MAAM;AACT,eAAO,YAAY;AACnB,eAAO,OAAO,IAAI,OAAO;AACzB,YAAI,OAAO,GAAG;AACV,oBAAU,UAAU;AAAA,QACxB;AACA,YAAI,UAAUA,SAAQ;AAClB;AAAA,QACJ,WACS,YAAY,QAAW;AAC5B,oBAAU,UAAUA;AAAA,QACxB,OACK;AACD,qBAAY,WAAWA,UAAS,KAAM,QAAQA;AAAA,QAClD;AACA,oBAAY;AACZ,kBAAW,YAAY,SAAaD,QAAO,OAAO,IAAI;AACtD,uBAAe,MAAM;AAAA,MACzB;AAAA,IACJ,EAAG,MAAM;AACT,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS,CAAC;AACf,WAAO,QAAQ,CAAC,SAAS;AACrB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;AACA,YAAY,YAAY;AACjB,SAAS,YAAY,OAAO;AAC/B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,YAAY,KAAK,EAAE;AACpC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACpDO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAI,eAAe,IAAI,YAAY,EAAE,QAAgB,QAAQ,CAAC,EAAE,CAAC;AACjE,QAAI,eAAe,IAAI,YAAY,EAAE,QAAgB,QAAQ,CAAC,EAAE,CAAC;AACjE,QAAI,QAAQ;AACZ,SAAK,YAAa,WAAWE,SAAQ;AACjC,UAAI,UAAU;AACd,UAAI,aAAa,aAAa,IAAI;AAClC,aAAO,MAAM;AACT,sBAAc,aAAa,UAAU,OAAO;AAC5C,sBAAc,aAAa,UAAU,OAAO;AAC5C,YAAK,gBAAgB,UAAe,gBAAgB,QAAY;AAC5D,cAAI,gBAAgB,GAAG;AACnB,yBAAa;AAAA,UACjB,WACS,gBAAgB,GAAG;AACxB,yBAAa;AAAA,UACjB,OACK;AACD,iBAAK,cAAc;AACnB,iBAAK,MAAM,EAAE,IAAI,IAAI;AACrB,yBAAa,YAAY,MAAO,OAAO,IAAI,KAAM,QAAQ,CAAC,CAAC;AAAA,UAC/D;AAAA,QACJ;AACA;AACA,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ,EAAG,MAAM;AACT,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS,CAAC;AACf,WAAO,QAAQ,CAAC,SAAS;AACrB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACrDO,IAAM,KAAN,cAAiB,UAAU;AAAA,EAC9B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,aAAa,MAAM;AACvB,QAAIC,OAAM,IAAI,IAAI,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC9E,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI,aAAa,IAAI,oBAAW,MAAM;AACtC;AACA,aAAO;AACP,UAAIC;AACJ,aAAO,MAAM;AACT,mBAAW,KAAK,IAAI;AACpB,eAAOD,KAAI,UAAU,IAAI;AACzB,YAAI,MAAM;AACN,cAAIE,OAAM;AACV,mBAAS,KAAK,WAAW,SAAS,GAAG;AACjC,YAAAA,OAAMA,OAAO,KAAK,IAAK,IAAI,MAAO,CAAC;AAAA,UACvC;AACA,UAAAD,MAAK,KAAK,KAAKC,OAAO,MAAO;AAAA,QACjC;AACA,eAAO,MAAMD;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,aAAa,KAAK,UAAU,KAAK,KAAK;AAC1C,QAAI,WAAW,SAAS;AACpB,aAAO,KAAK,OAAO,WAAW,KAAK;AAAA,EAC3C;AAEJ;AACA,GAAG,YAAY;AACR,SAAS,GAAG,OAAO;AACtB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,GAAG,KAAK,EAAE;AAC3B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACnDO,IAAM,iBAAN,cAA6B,UAAU;AAAA,EAC1C,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,aAAa,MAAM;AACvB,QAAI,SAAS,MAAM;AACnB,QAAIE,UAAS,KAAK;AAClB,QAAIC,MAAKC;AACT,SAAK,SAAS,CAAC;AACf,IAAAD,OAAM,IAAI,IAAI,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC1E,IAAAC,MAAK,IAAI,GAAG,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AACxE,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,aAAO;AACP,aAAO,MAAM;AACT,kBAAUD,KAAI,UAAU,IAAI;AAC5B,iBAASC,IAAG,UAAU,IAAI;AAC1B,YAAI,SAAS;AACT,cAAI,SAASF,QAAO,OAAO;AAC3B,cAAI,QAAQA,QAAO,UAAW,SAAS,MAAO;AAC9C,cAAI,QAAQA,QAAO,UAAW,SAAS,MAAO;AAC9C,cAAI,KAAKA,SAAQ,OAAO,UAAU,QAAQ,MAAM;AAChD,mBAAS;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;AACA,eAAe,YAAY;AACpB,SAAS,eAAe,OAAO;AAClC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,eAAe,KAAK,EAAE;AACvC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AChEO,IAAM,kBAAN,cAA8B,UAAU;AAAA,EAC3C,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,SAAS,MAAM;AACpB,SAAK,QAAQ,MAAM;AACnB,QAAI,QAAS,WAAW,QAAQ;AAC5B,UAAI,OAAO,IAAI,WAAW;AAC1B,UAAIG,OAAM;AACV,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,SAAS;AACb,aAAO,MAAM;AACT,YAAI,UAAU,QAAQ;AAClB;AACA,UAAAA,OAAMA,OAAM;AACZ,mBAAS;AAAA,QACb,WACS,WAAW,QAAQ;AACxB;AACA,UAAAA,OAAMA,OAAM;AACZ,mBAASA;AAAA,QACb,OACK;AACD,mBAAS,SAAU,SAAS,SAAU;AAAA,QAC1C;AACA,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ;AACA,SAAK,YAAY,MAAM,KAAK,MAAM;AAClC,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,MAAM,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU;AACV,aAAO,KAAK,OAAO,MAAM;AAAA,EACjC;AAEJ;AACA,gBAAgB,YAAY;AACrB,SAAS,gBAAgB,OAAO;AACnC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,gBAAgB,KAAK,EAAE;AACxC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACjDO,IAAM,MAAN,MAAM,aAAY,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAIC,UAAS,KAAK;AAClB,QAAI,KAAK,UAAU,MAAM,QAAQ;AAC7B,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI,UAAU;AACd,UAAI;AACJ,aAAO,MAAM;AACT,YAAI,MAAM;AACN,cAAI,SAAU,QAAQ,OAAO,KAAK;AAClC,cAAI,WAAY,KAAK,MAAM,QAAQ;AACnC,oBAAUA,QAAQ,WAAW,UAAU,WAAW,IAAK,WAAW,CAAC;AAAA,QACvE;AACA,eAAO;AACP,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,MACnB,CAAC;AACD,UAAI,OAAO,UAAU;AACjB,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,IACrC,CAAC;AAAA,EACL;AAAA,EAEA,OAAO,UAAU,OAAO;AACpB,cAAU,cAAc,KAAK;AAC7B,QAAI,SAAS,IAAI,KAAI,KAAK,EAAE;AAC5B,QAAI,MAAM,eAAe;AACrB,aAAO,QAAQ;AAAA,IACnB;AACA,cAAU,cAAc,KAAK;AAC7B,WAAO;AAAA,EACX;AAAA,EAEA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;;;AClDO,IAAM,MAAN,MAAM,aAAY,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAIC,UAAS,KAAK;AAClB,QAAI,KAAK,UAAU,MAAM,QAAQ;AAC7B,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI,UAAU;AACd,UAAI;AACJ,aAAO,MAAM;AACT,YAAI,MAAM;AACN,cAAI,SAAU,QAAQ,OAAO,KAAK;AAClC,cAAI,WAAY,KAAK,MAAM,QAAQ;AACnC,mBAASA,QAAQ,SAAS,YAAY,SAAS,IAAK,SAAS,CAAC;AAAA,QAClE;AACA,eAAO;AACP,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,MACnB,CAAC;AACD,UAAI,OAAO,UAAU;AACjB,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,IACrC,CAAC;AAAA,EACL;AAAA,EAEA,OAAO,UAAU,OAAO;AACpB,cAAU,cAAc,KAAK;AAC7B,QAAI,SAAS,IAAI,KAAI,KAAK,EAAE;AAC5B,QAAI,MAAM,eAAe;AACrB,aAAO,QAAQ;AAAA,IACnB;AACA,cAAU,cAAc,KAAK;AAC7B,WAAO;AAAA,EACX;AAAA,EAEA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;;;AC7CO,IAAM,YAAN,cAAwB,UAAU;AAAA,EACrC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAIC,UAAS,KAAK;AAClB,QAAI,KAAK,UAAU,MAAM,QAAQ;AAC7B,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,UAAU;AACd,UAAI,eAAe;AACnB,aAAO,MAAM;AACT,YAAI,kBAAkB,QAAW;AAC7B,0BAAgB,QAAQ;AACxB,oBAAU,MAAM;AAAA,QACpB;AACA,iBAAS,KAAK,IAAI,QAAQ,OAAO,QAAQ,KAAK,MAAM,KAAK,IAAI,QAAQ,OAAO,aAAa,CAAC,IAAI,IAAI,KAAK,IAAI,QAAQ,OAAO,aAAa,GAAG,MAAM,KAAK,IAAI,QAAQ,MAAM,aAAa,CAAC,IAAI,IAAI,KAAK,IAAI,QAAQ,MAAM,aAAa,CAAC;AAClO,wBAAgB,QAAQ;AACxB,YAAI,UAAU,QAAW;AACrB,mBAASA,QAAO,MAAM;AAAA,QAC1B;AACA,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,MACvB,CAAC;AACD,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;AACA,UAAU,YAAY;AACf,SAAS,UAAU,OAAO;AAC7B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,UAAU,KAAK,EAAE;AAClC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACxDO,IAAM,YAAN,cAAwB,eAAe;AAC9C;AAEO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAIC,UAAS,KAAK;AAClB,QAAI,SAAS,IAAI,IAAI;AAAA,MACjB,MAAM,CAAC;AAAA,MACP,KAAK,CAAC;AAAA,IACV,CAAC;AACD,QAAI,UAAU,IAAI,IAAI;AAAA,MAClB,MAAM,CAAC;AAAA,MACP,KAAK,CAAC;AAAA,IACV,CAAC;AACD,QAAI,SAAS,IAAI,gBAAgB,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC7F,QAAI,SAAS,IAAI,gBAAgB,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC7F,QAAI,QAAQ,IAAI,gBAAgB,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC5F,QAAI,QAAQ,IAAI,KAAK,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AACjF,QAAI,KAAK,IAAI,UAAU;AAAA,MACnB,KAAK,CAAC;AAAA,MACN,MAAM,CAAC;AAAA,MACP,OAAO,CAAC;AAAA,IACZ,CAAC;AACD,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,SAAU;AACvE,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf;AACA,SAAK,YAAa,aAAa;AAC3B,UAAI,OAAO;AACX,UAAI,QAAQ;AACZ,UAAI,SAAS,UAAU,UAAU,SAAS,SAAS,QAAQ;AAC3D,gBAAU;AACV,iBAAW;AACX,iBAAW;AACX,aAAO,MAAM;AACT,YAAI,SAAS,GAAG,UAAU,IAAI;AAC9B,YAAI,UAAU,OAAO,UAAU,IAAI;AACnC,YAAI,UAAU,QAAQ,UAAU,IAAI;AACpC,YAAI,WAAW,QAAW;AACtB,iBAAO;AACP;AAAA,QACJ;AACA,YAAIC,WAAU,MAAM,UAAU,MAAM;AACpC,YAAIC,YAAW,OAAO,UAAU,OAAO;AACvC,YAAIC,YAAW,OAAO,UAAU,OAAO;AACvC,YAAKF,YAAW,UAAeC,aAAY,UAAeC,aAAY,QAAY;AAC9E,oBAAWD,YAAY,MAAMD;AAC7B,oBAAWE,YAAY,MAAMF;AAC7B,cAAI,SAAS,KAAK,IAAI,UAAU,OAAO;AACvC,cAAI,QAAS,UAAU;AACvB,mBAAU,SAAS,QAAS;AAC5B,uBAAa,MAAM,UAAU,MAAM;AAAA,QAEvC;AACA,eAAO,MAAM,EAAE,KAAK,YAAY,KAAK,SAAS,KAAK,QAAQ;AAAA,MAC/D;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,MACvB,CAAC;AACD,UAAI,OAAO,SAAS,UAAa,OAAO,MAAM,OAAO,QAAW;AAC5D,aAAK,OAAO,KAAK,EAAE,KAAKD,QAAO,OAAO,MAAM,GAAG,GAAG,KAAKA,QAAO,OAAO,MAAM,GAAG,GAAG,KAAKA,QAAO,OAAO,MAAM,GAAG,EAAE,CAAC;AAAA,MACpH;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAGA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU,UAAa,OAAO,OAAO,QAAW;AAChD,aAAO,EAAE,KAAK,KAAK,OAAO,OAAO,GAAG,GAAG,KAAK,KAAK,OAAO,OAAO,GAAG,GAAG,KAAK,KAAK,OAAO,OAAO,GAAG,EAAE;AAAA,IACtG;AAAA,EACJ;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC7FO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAII,UAAS,KAAK;AAClB,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,SAAU;AACvE,YAAO;AAAA,IACX;AACA,QAAI,YAAY,IAAI,UAAU;AAAA,MAC1B,KAAK,CAAC;AAAA,MACN,MAAM,CAAC;AAAA,MACP,OAAO,CAAC;AAAA,IACZ,CAAC;AACD,QAAIC,QAAO,IAAI,KAAK,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChF,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,OAAO;AACX,UAAI,cAAc;AAClB;AACA,aAAO,MAAM;AACT,iBAAS,UAAU,UAAU;AAAA,UACzB,KAAK,KAAK;AAAA,UACV,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,QAChB,CAAC;AACD,YAAI,WAAW,QAAW;AACtB,yBAAe;AAAA,QACnB,OACK;AACD,yBAAeA,MAAK,UAAU,MAAM;AAAA,QACxC;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,MACvB,CAAC;AACD,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAKD,QAAO,OAAO,KAAK,CAAC;AAAA,MACzC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACtEO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,aAAa,MAAM;AACvB,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,QAAQ;AACZ,UAAI,cAAc,IAAI,oBAAW,MAAM;AACvC;AACA,UAAI,OAAO;AACX,UAAIE;AACJ,aAAO,MAAM;AACT,oBAAY,KAAK,IAAI;AACrB,YAAI,QAAQ,QAAQ;AAChB;AAAA,QACJ,OACK;AACD,UAAAA,QAAQ,OAAO,YAAY,aAAc,YAAY,YAAc;AAAA,QACvE;AACA,eAAO,MAAMA;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,UAAc,CAAC,MAAM,OAAO,KAAK,GAAI;AACrD,aAAK,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,aAAa,KAAK,UAAU,KAAK,KAAK;AAC1C,QAAI,WAAW,SAAS,UAAc,CAAC,MAAM,WAAW,KAAK,GAAI;AAC7D,aAAO,KAAK,OAAO,WAAW,KAAK;AAAA,IACvC;AAAA,EACJ;AAEJ;AACA,IAAI,YAAY;AAET,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC9CO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,aAAa,MAAM;AACvB,QAAI,UAAU,MAAM;AACpB,QAAI,UAAU,MAAM;AACpB,QAAI,UAAU,MAAM;AACpB,QAAI,UAAU,MAAM;AACpB,QAAI,UAAU,MAAM;AACpB,QAAI,UAAU,MAAM;AACpB,QAAI,UAAU,MAAM;AACpB,QAAI,UAAU,MAAM;AACpB,QAAI,eAAe,MAAM;AACzB,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,SAAS,QAAQ,CAAC,EAAE,CAAC;AAClD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,SAAS,QAAQ,CAAC,EAAE,CAAC;AAClD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,SAAS,QAAQ,CAAC,EAAE,CAAC;AAClD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,SAAS,QAAQ,CAAC,EAAE,CAAC;AAClD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,SAAS,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChF,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,SAAS,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChF,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,SAAS,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChF,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,SAAS,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChF,QAAI,YAAY,IAAI,IAAI,EAAE,QAAQ,cAAc,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC1F,QAAIC,UAAS,KAAK;AAClB,SAAK,SAAS,CAAC;AACf,QAAI,cAAc,KAAK,IAAI,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS,UAAU,OAAO;AACrG,SAAK,YAAa,aAAa;AAC3B,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,UAAIC;AACJ,UAAI,OAAO,OAAO,OAAO,OAAO,QAAQ;AACxC,aAAO,MAAM;AACT,YAAI,aAAa,KAAK,UAAU,IAAI;AACpC,YAAI,aAAa,KAAK,UAAU,IAAI;AACpC,YAAI,aAAa,KAAK,UAAU,IAAI;AACpC,YAAI,aAAa,KAAK,UAAU,IAAI;AACpC,gBAAS,eAAe,SAAa,KAAK,UAAU,UAAU,IAAI;AAClE,gBAAS,eAAe,SAAa,KAAK,UAAU,UAAU,IAAI;AAClE,gBAAS,eAAe,SAAa,KAAK,UAAU,UAAU,IAAI;AAClE,gBAAS,eAAe,SAAa,KAAK,UAAU,UAAU,IAAI;AAClE,YAAI,QAAQ,aAAa;AACrB;AAAA,QACJ,OACK;AACD,UAAAA,OAAO,QAAQ,IAAM,QAAQ,IAAM,QAAQ,IAAM,QAAQ;AAAA,QAC7D;AACA,iBAAUA,SAAQ,SAAa,UAAU,UAAUA,IAAG,IAAI;AAC1D,iBAASA,SAAQ,SAAY;AAAA,UACzB,KAAKD,QAAOC,IAAG;AAAA,UACf,QAAQ,SAASD,QAAO,MAAM,IAAI;AAAA,QACtC,IAAI;AACJ,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,aAAa,KAAK,UAAU,KAAK,KAAK;AAC1C,QAAI,WAAW,SAAS;AACpB,aAAO,WAAW;AAAA,EAC1B;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACjDO,IAAM,OAAN,cAAmB,UAAU;AAAA,EAChC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,QAAQ,MAAM,QAAQ,CAAC;AAC3B,QAAI,OAAO,MAAM,OAAO,CAAC;AACzB,QAAI,QAAQ,WAAW,MAAM,KAAK;AAC9B,UAAI,MAAM,SAAS,KAAK;AACxB,UAAI,KAAK;AACT,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,aAAO,MAAM;AACT,YAAI,MAAM;AACN,gBAAM,MAAM,SAAS,UAAU;AAC/B,cAAI,IAAI;AACJ,kBAAM,KAAK,IAAI,KAAK,SAAS,KAAK,KAAK,GAAG;AAC1C,gBAAI,KAAK,OAAO,SAAS;AACrB,wBAAU,KAAK;AACf,sBAAQ,KAAK,IAAI,QAAQ,MAAM,GAAG;AAAA,YACtC;AACA;AAAA,UACJ,OACK;AACD,kBAAM,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,IAAI;AAC5C,gBAAI,KAAK,MAAM,SAAS;AACpB,wBAAU,KAAK;AACf,sBAAQ,KAAK,IAAI,QAAQ,MAAM,GAAG;AAAA,YACtC;AAAA,UACJ;AACA,cAAK,MAAM,KAAK,MAAM,OAAS,CAAC,MAAM,KAAK,OAAO,KAAM;AACpD,oBAAQ;AACR,kBAAM;AACN,iBAAK,CAAC;AACN,sBAAU,CAAC,KAAK,KAAK,MAAM,KAAK;AAAA,UACpC;AAAA,QACJ,OACK;AAED,gBAAM,KAAK;AACX,oBAAU,KAAK;AAAA,QACnB;AACA,mBAAW;AACX,YAAI;AACA,iBAAO;AACX,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ;AACA,SAAK,SAAS,CAAC;AACf,SAAK,YAAY,MAAM,MAAM,MAAM,MAAM,GAAG;AAC5C,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,MACnB,CAAC;AACD,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,aAAa,KAAK,UAAU,KAAK,KAAK;AAC1C,QAAI,WAAW,UAAU;AACrB,aAAO,WAAW;AAAA,EAC1B;AAEJ;AACA,KAAK,YAAY;AACV,SAAS,KAAK,OAAO;AACxB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,KAAK,KAAK,EAAE;AAC7B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACnGO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAI,eAAe,MAAM;AACzB,QAAIE,UAAS,KAAK;AAClB,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,SAAU;AACvE,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AAOf,SAAK,YAAa,aAAa;AAC3B,UAAI,QAAQ;AACZ,UAAI,kBAAkB,IAAI,oBAAW,QAAQ,MAAM,KAAK;AACxD,UAAI,iBAAiB,IAAI,oBAAW,QAAQ,OAAO,IAAI;AACvD,UAAI,OAAO,IAAI,IAAI;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ,CAAC;AAAA,QACT,QAAQ,CAAC,MAAM;AAAE,iBAAO;AAAA,QAAG;AAAA,MAC/B,CAAC;AACD,UAAI,GAAG;AACP,UAAI,OAAO;AACX,aAAO,MAAM;AACT,wBAAgB,KAAK,KAAK,IAAI;AAC9B,uBAAe,KAAK,KAAK,GAAG;AAC5B,YAAI,QAAQ,QAAQ;AAChB;AACA,iBAAO;AACP;AAAA,QACJ;AACA,YAAI,YAAY,eAAe;AAC/B,aAAK,KAAK,QAAQ,cAAc,gBAAgB,aAAa,aAAa;AAC1E,YAAI,MAAM,CAAC,IAAI,IAAI;AACnB,YAAI,KAAK,UAAU,CAAC;AACpB,eAAO,MAAM;AAAA,UACT,GAAGA,QAAO,CAAC;AAAA,UACX,GAAI,MAAM,SAAaA,QAAO,CAAC,IAAI;AAAA,QACvC;AAAA,MACJ;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,MACvB,CAAC;AACD,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,aAAa,KAAK,UAAU,KAAK,KAAK;AAC1C,QAAI,WAAW,UAAU;AACrB,aAAO,WAAW;AAAA,EAC1B;AAEJ;AACA,WAAW,YAAY;AAChB,SAAS,WAAW,OAAO;AAC9B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,WAAW,KAAK,EAAE;AACnC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACrFO,IAAM,YAAN,cAAwB,UAAU;AAAA,EACrC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAIC,UAAS,KAAK;AAClB,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,SAAU;AACvE,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AAKf,SAAK,YAAa,aAAa;AAC3B,UAAI,QAAQ;AACZ,UAAI,kBAAkB,IAAI,oBAAW,QAAQ,MAAM,KAAK;AACxD,UAAI,iBAAiB,IAAI,oBAAW,QAAQ,OAAO,IAAI;AACvD,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO;AACX,UAAI;AACJ,aAAO,MAAM;AACT,wBAAgB,KAAK,KAAK,IAAI;AAC9B,uBAAe,KAAK,KAAK,GAAG;AAC5B,YAAI,QAAQ,QAAQ;AAChB;AACA,iBAAO;AACP;AAAA,QACJ;AACA,oBAAY,eAAe;AAC3B,qBAAa,gBAAgB;AAC7B,oBAAYA,SAAQ,aAAa,KAAK,UAAU,aAAa,aAAa,IAAI;AAC9E,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,KAAK,UAAU;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,MACvB,CAAC;AACD,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,aAAa,KAAK,UAAU,KAAK,KAAK;AAC1C,QAAI,WAAW,SAAS;AACpB,aAAO,KAAK,OAAO,WAAW,KAAK;AAAA,EAC3C;AAEJ;AACA,UAAU,YAAY;AACf,SAAS,UAAU,OAAO;AAC7B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,UAAU,KAAK,EAAE;AAClC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AClEO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,QAAQ,MAAM;AAClB,QAAI,OAAO,MAAM;AACjB,QAAI,SAAS,MAAM;AACnB,QAAI,UAAU,MAAM;AACpB,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,UAAY,MAAM,WAAW,QAAQ,SAAU;AAC5G,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,SAAS;AACb,UAAI;AACJ,aAAO;AACP,aAAO,MAAM;AACT,YAAI,uBAAwB,KAAK,QAAQ,KAAK,OAAQ,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,KAAK;AACnG,8BAAsB,MAAM,mBAAmB,IAAI,IAAI;AACvD,YAAI,kBAAkB,sBAAsB,KAAK;AACjD,iBAAS,SAAS;AAClB,eAAO,MAAM,KAAK,MAAM,MAAM;AAAA,MAClC;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,QAAQ,CAAC,UAAU,UAAU;AAC/B,UAAI,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,QACnB,QAAQ,QAAQ,KAAK;AAAA,MACzB;AACA,UAAI,SAAS,KAAK,UAAU,KAAK,SAAS;AAC1C,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACnDO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,UAAU,MAAM;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,SAAS;AACb,UAAI;AACJ,UAAI;AACJ,aAAO;AACP,UAAI,KAAK,SAAU,OAAO,KAAK,UAAU,UAAW;AAChD,oBAAY,KAAK;AACjB,eAAO;AAAA,MACX;AACA,aAAO,MAAM;AACT,YAAI,YAAY,KAAK,OAAO;AACxB,mBAAS,SAAS,KAAK;AAAA,QAC3B,WACS,KAAK,QAAQ,WAAW;AAC7B,mBAAS,SAAS,KAAK;AAAA,QAC3B;AACA,oBAAY,KAAK;AACjB,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,WAAO,QAAQ,CAAC,OAAO,UAAU;AAC7B,UAAI,YAAY;AAAA,QACZ,OAAO,OAAO,KAAK;AAAA,QACnB,QAAQ,QAAQ,KAAK;AAAA,MACzB;AACA,UAAI,SAAS,KAAK,UAAU,KAAK,SAAS;AAC1C,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACjDO,IAAM,OAAN,cAAmB,UAAU;AAAA,EAChC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,aAAa,MAAM;AACvB,QAAI,SAAS,MAAM;AACnB,QAAIC,UAAS,KAAK;AAClB,QAAIC,OAAM,IAAI,IAAI,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC9E,QAAI,WAAW,IAAI,IAAI,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AACnF,QAAI,gBAAgB,IAAI,IAAI,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AACxF,QAAI,UAAU,IAAI,IAAI,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC7E,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,OAAO;AACX,aAAO,MAAM;AACT,YAAI,aAAaA,KAAI,UAAU,IAAI;AACnC,YAAI,iBAAiB,aAAa,SAAS,UAAU,UAAU,IAAI;AACnE,YAAI,uBAAuB,iBAAiB,cAAc,UAAU,cAAc,IAAI;AACtF,YAAI,SAAS,uBAAuB,QAAQ,UAAU,oBAAoB,IAAI;AAC9E,eAAO,MAAM,SAASD,QAAO,MAAM,IAAI;AAAA,MAC3C;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,aAAa,KAAK,UAAU,KAAK,KAAK;AAC1C,QAAI,WAAW,UAAU;AACrB,aAAO,WAAW;AAAA,EAC1B;AAEJ;AACA,KAAK,YAAY;AACV,SAAS,KAAK,OAAO;AACxB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,KAAK,KAAK,EAAE;AAC7B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC9CO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,UAAU,MAAM;AACpB,QAAI,SAAS,MAAM,UAAU;AAC7B,QAAI,EAAG,QAAQ,WAAW,OAAO,SAAU;AACvC,YAAO;AAAA,IACX;AACA,QAAI,gBAAgB,IAAI,IAAI,EAAE,QAAQ,CAAC,GAAG,OAAe,CAAC;AAC1D,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,eAAe;AACnB,UAAI,OAAO;AACX,UAAI;AACJ,aAAO,MAAM;AACT,sBAAc,KAAK,QAAQ,aAAa,SAAS,KAAK;AACtD,uBAAe;AACf,eAAO,MAAM,cAAc,UAAU,UAAU;AAAA,MACnD;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,YAAQ,QAAQ,CAAC,MAAM,UAAU;AAC7B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,OAAO,OAAO,KAAK;AAAA,QACnB,QAAQ,QAAQ,KAAK;AAAA,MACzB,CAAC;AACD,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAGA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU,QAAW;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AAEJ;AACA,WAAW,YAAY;AAChB,SAAS,WAAW,OAAO;AAC9B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,WAAW,KAAK,EAAE;AACnC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACtDO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAIE,UAAS,KAAK;AAClB,QAAI,WAAW;AACf,QAAI,eAAe,IAAI,oBAAW,MAAM;AACxC;AACA,QAAI,kBAAkB,IAAI,IAAI,EAAE,QAAgB,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC1F,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,SAAU;AACvE,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,OAAO;AACX,aAAO,MAAM;AACT,YAAI,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,SAAS;AAC/C,qBAAa,KAAK,EAAE;AACpB,YAAI,QAAQ,gBAAgB,UAAU,EAAE;AACxC,YAAI,gBAAgB;AACpB,YAAIC;AACJ,YAAIC,OAAM;AACV,YAAI,SAAS,QAAW;AAIpB,mBAAS,KAAK,aAAa,SAAS,GAAG;AACnC,YAAAA,OAAMA,OAAO,KAAK,IAAI,IAAI,KAAK;AAAA,UACnC;AAEA,0BAAgBA,OAAM;AACtB,UAAAD,QAAO,KAAK,UAAU,WAAW;AAAA,QACrC;AACA,eAAO,MAAMA;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,MACvB,CAAC;AACD,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAGA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU,QAAW;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACxEO,IAAM,oBAAN,cAAgC,UAAU;AAAA,EAC7C,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,QAAQ,MAAM;AAClB,QAAI,OAAO,MAAM;AACjB,QAAI,aAAa,MAAM;AACvB,QAAI,aAAa,MAAM;AACvB,QAAI,UAAU,IAAI,IAAI,EAAE,QAAQ,CAAC,GAAG,QAAQ,WAAW,CAAC;AACxD,QAAI,UAAU,IAAI,IAAI,EAAE,QAAQ,CAAC,GAAG,QAAQ,WAAW,CAAC;AACxD,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,aAAO;AACP,aAAO,MAAM;AACT,uBAAe,KAAK,OAAO,KAAK,OAAO;AACvC,uBAAe,QAAQ,UAAU,WAAW;AAC5C,uBAAe,QAAQ,UAAU,WAAW;AAC5C,YAAI,iBAAiB,UAAa,iBAAiB,QAAW;AAC1D,mBAAS,eAAe;AAAA,QAC5B;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,QAAQ,CAAC,UAAU,UAAU;AAC/B,UAAI,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,KAAK,KAAK,KAAK;AAAA,MACnB;AACA,UAAI,SAAS,KAAK,UAAU,KAAK,SAAS;AAC1C,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,QAAI,OAAO,SAAS,QAAW;AAC3B,aAAO,KAAK,OAAO,OAAO,KAAK;AAAA,IACnC;AAAA,EACJ;AAEJ;AACA,kBAAkB,YAAY;AACvB,SAAS,kBAAkB,OAAO;AACrC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,kBAAkB,KAAK,EAAE;AAC1C,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACzDO,IAAM,OAAN,cAAmB,UAAU;AAAA,EAChC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,UAAU,MAAM;AACpB,QAAIE,UAAS,KAAK;AAClB,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,SAAU;AACvE,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,OAAO;AACX,UAAI,kBAAkB;AACtB,UAAI,mBAAmB;AACvB,aAAO,MAAM;AACT,YAAI,gBAAgB,KAAK,OAAO,KAAK,MAAM,KAAK,SAAS;AACzD,YAAI,QAAQ,KAAK,SAAS;AAC1B,0BAAkB,kBAAkB;AACpC,2BAAmB,mBAAmB,KAAK;AAC3C,eAAO,MAAM,kBAAkB;AAC/B;AAAA,MACJ;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,SAAK,QAAQ,CAAC,MAAM,UAAU;AAC1B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK;AAAA,QACjB,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,QACnB,QAAQ,QAAQ,KAAK;AAAA,MACzB,CAAC;AACD,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAGA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,QAAI,UAAU,QAAW;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AAEJ;AACA,KAAK,YAAY;AACV,SAAS,KAAK,OAAO;AACxB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,KAAK,KAAK,EAAE;AAC7B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACxDO,SAAS,0BAA0B,KAAK,MAAM,MAAM,OAAO;AAC9D,SAAQ,OAAO,QAAQ,QAAQ,QAAU,QAAQ,OAAO,SAAS;AACrE;AACO,IAAM,gBAAN,cAA4B,UAAU;AAAA,EACzC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,QAAQ,MAAM;AAClB,QAAI,OAAO,MAAM;AACjB,QAAI,SAAS,MAAM;AACnB,QAAI,QAAQ,MAAM;AAClB,QAAI,UAAU,MAAM;AACpB,QAAI,OAAO,MAAM;AACjB,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,UAAY,MAAM,WAAW,QAAQ,SAAU;AAC5G,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf,QAAI,MAAM,KAAK,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK;AACzD,QAAI,MAAM,KAAK,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK;AACzD,QAAI,YAAY,MAAM,OAAO;AAC7B,QAAI,UAAU;AACd,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,UAAI,aAAa;AACjB,UAAI,WAAW,aAAa;AAC5B,gBAAU;AACV,UAAI,gBAAgB;AACpB,UAAI,gBAAgB;AACpB,UAAI,cAAc;AAClB,eAAS,WAAW,GAAG,WAAW,MAAM,QAAQ,YAAY;AACxD,YAAI,gBAAgB,KAAK,QAAQ;AACjC,YAAI,cAAc,MAAM,QAAQ;AAChC,YAAI,eAAe,MAAM,QAAQ;AACjC,YAAI,gBAAgB,OAAO,QAAQ;AACnC,YAAI,iBAAiB,QAAQ,QAAQ;AACrC,YAAI,0BAA0B,YAAY,UAAU,eAAe,WAAW,GAAG;AAC7E,wBAAc,cAAc;AAC5B,cAAI,eAAe,eAAe;AAC9B,4BAAgB,gBAAgB;AAAA,UACpC,OACK;AACD,4BAAgB,gBAAgB;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,OAAO,KAAK;AAAA,QACb;AAAA,QAAY;AAAA,QAAU;AAAA,QAAe;AAAA,QAAe;AAAA,MACxD,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EAEA,UAAU,OAAO;AACb,UAAO;AAAA,EACX;AAEJ;AACA,cAAc,YAAY;AACnB,SAAS,cAAc,OAAO;AACjC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,cAAc,KAAK,EAAE;AACtC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC9DO,IAAM,eAAN,cAA2B,UAAU;AAAA,EACxC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,aAAa;AACjB,aAAO,MAAM;AACT,qBAAa,OAAO,WAAW,OAAO,WAAW,MAAM,WAAW,SAAS;AAAA,MAC/E;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,IAAI,QAAQ,CAAC,MAAM,UAAU;AAC/B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK,KAAK;AAAA,QACtB,KAAK,MAAM,IAAI,KAAK;AAAA,QACpB,OAAO,MAAM,MAAM,KAAK;AAAA,MAC5B,CAAC;AACD,WAAK,OAAO,KAAK,OAAO,KAAK;AAAA,IACjC,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,WAAO;AAAA,EACX;AAEJ;AACA,aAAa,YAAY;AAClB,SAAS,aAAa,OAAO;AAChC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,aAAa,KAAK,EAAE;AACrC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACjCO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,QAAQ,MAAM;AAClB,QAAI,OAAO,MAAM;AACjB,QAAI,SAAS,MAAM;AACnB,QAAI,UAAU,MAAM;AACpB,QAAI,SAAS,MAAM;AACnB,QAAI,eAAe,IAAI,aAAa,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC;AACpE,QAAI,eAAe,IAAI,oBAAoB,QAAQ,OAAO,OAAO,IAAI;AACrE,QAAI,eAAe,IAAI,oBAAoB,QAAQ,OAAO,OAAO,IAAI;AACrE,QAAI,EAAG,KAAK,WAAW,MAAM,UAAY,MAAM,WAAW,OAAO,UAAY,MAAM,WAAW,QAAQ,SAAU;AAC5G,YAAO;AAAA,IACX;AACA,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe;AACnB,UAAI;AACJ,UAAI;AACJ,UAAI,oBAAoB;AACxB,UAAI,sBAAsB;AAC1B,aAAO;AACP,kBAAY,KAAK;AACjB,aAAO;AACP,aAAO,MAAM;AACT,YAAI,EAAE,MAAM,KAAK,OAAO,OAAO,IAAI;AACnC,YAAI,gBAAgB;AACpB,YAAI,gBAAgB;AACpB,4BAAoB,aAAa,UAAU,EAAE,MAAM,KAAK,MAAM,CAAC;AAC/D,uBAAe,oBAAoB;AACnC,YAAK,qBAAqB,QAAU,uBAAuB,MAAO;AAC9D,8BAAoB,sBAAsB,gBAAgB,eAAe,gBAAgB;AACzF,uBAAa,KAAK,aAAa;AAC/B,uBAAa,KAAK,aAAa;AAC/B,kCAAwB,aAAa;AACrC,kCAAwB,aAAa;AACrC,cAAK,aAAa,eAAe,UAAY,aAAa,eAAe,QAAS;AAC9E,6BAAiB,wBAAwB;AACzC,qBAAS,MAAM,OAAO,IAAI;AAAA,UAC9B;AAAA,QACJ;AACA,8BAAsB;AACtB,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,QAAQ,CAAC,UAAU,UAAU;AAC/B,UAAI,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,QACnB,QAAQ,QAAQ,KAAK;AAAA,MACzB;AACA,UAAI,SAAS,KAAK,UAAU,KAAK,SAAS;AAC1C,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,WAAW,OAAO,MAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,MACxD;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,QAAI,OAAO,SAAS,QAAW;AAC3B,aAAQ,WAAW,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,IAC9C;AAAA,EACJ;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC3EO,IAAM,gBAAN,cAA4B,UAAU;AAAA,EACzC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,YAAY,MAAM;AACtB,QAAI,mBAAmB,MAAM;AAC7B,QAAI,UAAU,MAAM;AACpB,QAAI,UAAU,MAAM;AACpB,QAAIC,UAAS,KAAK;AAClB,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAI,QAAQ;AACZ,UAAIC,OAAM,IAAI,IAAI,EAAE,QAAQ,WAAW,QAAQ,CAAC,EAAE,CAAC;AACnD,UAAIC,cAAa,IAAI,WAAW,EAAE,QAAQ,kBAAkB,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,cAAc,QAAQ,CAAC;AACjH,UAAI,OAAO,IAAI,IAAI;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ,CAAC;AAAA,QACT,QAAQ,CAAC,MAAM;AAAE,iBAAO;AAAA,QAAG;AAAA,MAC/B,CAAC;AACD,UAAI,SAAS,eAAe,GAAG;AAC/B,UAAI,OAAO;AACX,aAAO,MAAM;AACT,kBAAUD,KAAI,UAAU,IAAI;AAC5B,YAAI,YAAY,QAAW;AACvB,cAAI,kBAAkB,EAAE,MAAM,SAAS,KAAK,SAAS,OAAO,QAAQ;AACpE,0BAAgBC,YAAW,UAAU,eAAe;AACpD,cAAI,kBAAkB,UAAa,cAAc,MAAM,QAAW;AAC9D,gBAAI,KAAK,UAAU,cAAc,CAAC;AAClC,gBAAI,MAAM;AACN,uBAAS;AAAA,gBACL,UAAU,cAAc;AAAA,gBACxB,GAAG,cAAc;AAAA,gBACjB;AAAA,cACJ;AAAA,UACR;AAAA,QACJ;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,WAAO,QAAQ,CAAC,MAAM,UAAU;AAC5B,UAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,aAAa,KAAK,UAAU,KAAK,KAAK;AAC1C,QAAI,WAAW,UAAU;AACrB,aAAO,WAAW;AAAA,EAC1B;AAEJ;AACA,cAAc,YAAY;AACnB,SAAS,cAAc,OAAO;AACjC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,cAAc,KAAK,EAAE;AACtC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC1EO,IAAM,UAAN,cAAsB,UAAU;AAAA,EACnC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,SAAK,SAAS,CAAC;AACf,QAAI,aAAa,IAAI,oBAAqB,QAAQ,MAAM,OAAO,KAAK;AACpE,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,aAAO;AACP,aAAO,MAAM;AACT,mBAAW,KAAK,IAAI;AACpB,YAAI,WAAW,eAAe,QAAQ;AAClC,iBAAO,WAAW;AAAA,QACtB;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,WAAO,QAAQ,CAAC,OAAO,UAAU;AAC7B,UAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,QAAI,OAAO,SAAS,QAAW;AAC3B,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AAEJ;AACA,QAAQ,YAAY;AACb,SAAS,QAAQ,OAAO;AAC3B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,QAAQ,KAAK,EAAE;AAChC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC9CO,IAAM,SAAN,cAAqB,UAAU;AAAA,EAClC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,SAAK,SAAS,CAAC;AACf,QAAI,aAAa,IAAI,oBAAqB,QAAQ,OAAO,MAAM,KAAK;AACpE,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,aAAO;AACP,aAAO,MAAM;AACT,mBAAW,KAAK,IAAI;AACpB,YAAI,WAAW,eAAe,QAAQ;AAClC,iBAAO,WAAW;AAAA,QACtB;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,WAAO,QAAQ,CAAC,OAAO,UAAU;AAC7B,UAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,QAAI,OAAO,SAAS,QAAW;AAC3B,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AAEJ;AACA,OAAO,YAAY;AACZ,SAAS,OAAO,OAAO;AAC1B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,OAAO,KAAK,EAAE;AAC/B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC9CO,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC/B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,SAAK,SAAS,CAAC;AACf,QAAI,aAAa,IAAI,oBAAqB,QAAQ,OAAO,OAAO,IAAI;AACpE,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,aAAO;AACP,aAAO,MAAM;AACT,mBAAW,KAAK,IAAI;AACpB,YAAI,WAAW,eAAe,QAAQ;AAClC,iBAAO,WAAW;AAAA,QACtB;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,WAAO,QAAQ,CAAC,OAAO,UAAU;AAC7B,UAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,QAAI,OAAO,SAAS,QAAW;AAC3B,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AAEJ;AACA,IAAI,YAAY;AACT,SAAS,IAAI,OAAO;AACvB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,IAAI,KAAK,EAAE;AAC5B,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC1CA,IAAM,QAAN,cAAoB,UAAU;AAAA,EAC1B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAIC,UAAS,KAAK;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,YAAY,MAAM,aAAa;AACnC,QAAI,QAAQ;AACR,UAAI,YAAY,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5C,kBAAY,UAAU,UAAU,SAAS,CAAC;AAAA,IAC9C;AACA,SAAK,SAAS,IAAI,WAAW;AAC7B;AACA,QAAI,cAAc,GAAG;AACjB,cAAQ,MAAM,iEAAiE;AAC/E;AAAA,IACJ;AACA,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,aAAa;AACjB,QAAI,gBAAgB;AACpB,SAAK,YAAa,aAAa;AAC3B,UAAI,aAAa;AACjB,aAAO,MAAM;AAET,YAAI,aAAa,GAAG;AAChB,qBAAW,WAAW;AACtB,qBAAW,WAAW;AACtB,oBAAU,WAAW;AACrB,sBAAY,WAAW;AACvB,uBAAa,WAAW;AACxB,0BAAgB,WAAW;AAC3B,uBAAa;AACb;AAAA,QACJ;AACA,YAAI,4BAA4B,KAAK,IAAI,WAAW,QAAQ,SAAS;AACrE,YAAI,2BAA2B,KAAK,IAAI,WAAW,QAAQ,QAAQ;AACnE,YAAK,6BAA6B,aAAe,4BAA4B,WAAY;AACrF,cAAI,YAAY,4BAA4B,2BAA2B,WAAW;AAClF,cAAI,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM,WAAW,WAAW,OAAO,WAAW,WAAW;AAAA,YACzD,KAAK,UAAU,WAAW,MAAM,UAAU,WAAW;AAAA,YACrD,OAAO,YAAY,WAAW,QAAS,YAAY,YAAc,YAAY;AAAA,YAC7E,QAAQ,aAAa,WAAW;AAAA,YAChC,WAAW,WAAW;AAAA,UAC1B;AACA,qBAAW,WAAW;AACtB,qBAAW,WAAW;AACtB,oBAAU,WAAW;AACrB,sBAAY,WAAW;AACvB,uBAAa;AACb,uBAAa,MAAM;AAAA,QACvB,OACK;AACD,qBAAW,WAAW,WAAW,OAAO,WAAW,WAAW;AAC9D,oBAAU,UAAU,WAAW,MAAM,UAAU,WAAW;AAC1D,uBAAa,aAAa,WAAW;AACrC,0BAAgB,WAAW;AAC3B,uBAAa;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,IAAI,QAAQ,CAAC,MAAM,UAAU;AAC/B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK,KAAK;AAAA,QACtB,MAAM,MAAM,KAAK,KAAK;AAAA,QACtB,KAAK,MAAM,IAAI,KAAK;AAAA,QACpB,OAAO,MAAM,MAAM,KAAK;AAAA,QACxB,QAAQ,MAAM,OAAO,KAAK;AAAA,QAC1B,WAAW,MAAM,UAAU,KAAK;AAAA,MACpC,CAAC;AACD,UAAI,OAAO,OAAO;AACd,aAAK,OAAO,KAAK,KAAK,OAAO,MAAM,IAAI;AACvC,aAAK,OAAO,KAAK,KAAK,OAAO,MAAM,IAAI;AACvC,aAAK,OAAO,IAAI,KAAK,OAAO,MAAM,GAAG;AACrC,aAAK,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK;AACzC,aAAK,OAAO,OAAO,KAAK,OAAO,MAAM,MAAM;AAC3C,aAAK,OAAO,UAAU,KAAK,OAAO,MAAM,SAAS;AAAA,MACrD;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,YAAQ,MAAM,+GAA+G;AAC7H,WAAO;AAAA,EACX;AAEJ;AACA,MAAM,YAAY;AACX,SAAS,MAAM,OAAO;AACzB,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,MAAM,KAAK,EAAE;AAC9B,MAAI,MAAM,eAAe;AACrB,WAAO,KAAK,QAAQ;AACpB,WAAO,KAAK,QAAQ;AACpB,WAAO,IAAI,QAAQ;AACnB,WAAO,MAAM,QAAQ;AACrB,WAAO,OAAO,QAAQ;AACtB,WAAO,UAAU,QAAQ;AAAA,EAC7B;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACzGO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAIC,UAAS,KAAK;AAClB,SAAK,SAAS,IAAI,WAAW;AAC7B,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,aAAa;AACjB,QAAI,gBAAgB;AACpB,SAAK,YAAa,aAAa;AAC3B,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,aAAO,MAAM;AACT,YAAI,aAAa,MAAM;AACnB,sBAAY,WAAW,QAAQ,WAAW,QAAQ;AAClD,qBAAW,WAAW;AACtB,oBAAU,WAAW;AACrB,uBAAa,WAAW,QAAQ,WAAW,OAAO,WAAW,OAAO,WAAW,OAAO;AACtF,uBAAc,WAAW,UAAU;AACnC,0BAAiB,WAAW,aAAa;AACzC,uBAAa;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA,YACN,KAAK;AAAA,YACL,OAAO;AAAA,YACP,QAAQ,WAAW,UAAU;AAAA,YAC7B,WAAY,WAAW,aAAa;AAAA,UACxC;AAAA,QACJ,OACK;AACD,cAAI,YAAY,WAAW,QAAQ,WAAW,OAAO,WAAW,OAAO,WAAW,OAAO;AACzF,cAAI,WAAW,WAAW,aAAa;AACvC,cAAI,UAAU,KAAK,IAAI,SAAS,UAAU,WAAW,IAAI;AACzD,cAAI,SAAS,KAAK,IAAI,WAAW,KAAK,SAAS,QAAQ;AACvD,uBAAa;AAAA,YACT,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,YACN,KAAK;AAAA,YACL,QAAS,WAAW,UAAU;AAAA,YAC9B,WAAY,WAAW,aAAa;AAAA,UACxC;AACA,sBAAY;AACZ,qBAAW;AACX,qBAAW;AACX,oBAAU;AAAA,QACd;AACA,qBAAa,MAAM;AAAA,MACvB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,IAAI,QAAQ,CAAC,MAAM,UAAU;AAC/B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK,KAAK;AAAA,QACtB,MAAM,MAAM,KAAK,KAAK;AAAA,QACtB,KAAK,MAAM,IAAI,KAAK;AAAA,QACpB,OAAO,MAAM,MAAM,KAAK;AAAA,QACxB,QAAQ,MAAM,SAAS,MAAM,OAAO,KAAK,IAAI,MAAM;AAAA,QACnD,WAAW,MAAM,YAAY,MAAM,UAAU,KAAK,IAAI,MAAM;AAAA,MAChE,CAAC;AACD,UAAI,OAAO,OAAO;AACd,aAAK,OAAO,KAAK,KAAK,OAAO,MAAM,IAAI;AACvC,aAAK,OAAO,KAAK,KAAK,OAAO,MAAM,IAAI;AACvC,aAAK,OAAO,IAAI,KAAK,OAAO,MAAM,GAAG;AACrC,aAAK,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK;AACzC,aAAK,OAAO,OAAO,KAAK,OAAO,MAAM,MAAM;AAC3C,aAAK,OAAO,UAAU,KAAK,OAAO,MAAM,SAAS;AAAA,MACrD;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,EAAE;AACxC,WAAO;AAAA,EACX;AAEJ;AACA,WAAW,YAAY;AAChB,SAAS,WAAW,OAAO;AAC9B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,WAAW,KAAK,EAAE;AACnC,MAAI,MAAM,eAAe;AACrB,WAAO,KAAK,QAAQ;AACpB,WAAO,KAAK,QAAQ;AACpB,WAAO,IAAI,QAAQ;AACnB,WAAO,MAAM,QAAQ;AACrB,WAAO,OAAO,QAAQ;AACtB,WAAO,UAAU,QAAQ;AAAA,EAC7B;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACnGA,IAAqB,oBAArB,MAAuC;AAAA,EACnC,cAAc;AAAA,EAId;AAAA,EACA,iBAAiB,GAAG,GAAG;AACnB,QAAI,OAAO,WAAW,KAAK,IAAI,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,IAAI;AACxD,QAAI,QAAQ,YAAY,IAAI,MAAO,YAAY,CAAC,CAAC,IAAI;AACrD,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,MAAM,MAAM;AACR,UAAM;AAAA,EACV;AAAA,EACA,mBAAmB,MAAM;AACrB,QAAI,KAAK,MAAM,SAAS,KAAK,eAAe;AACxC,cAAQ,KAAK,wDAAwD,KAAK,IAAI;AAC9E,aAAO,CAAC;AAAA,IACZ;AACA,QAAI,KAAK,eAAe;AACpB,WAAK,KAAK,QAAQ;AAClB,WAAK,KAAK,QAAQ;AAClB,WAAK,IAAI,QAAQ;AACjB,WAAK,MAAM,QAAQ;AAAA,IACvB;AACA,QAAI,aAAa,KAAK;AACtB,WAAO,KAAK,4BAA4B,IAAI,EACvC,IAAI,CAAC,SAAS,UAAU;AACzB,aAAO,WAAW,KAAK,MAAM,OAAO,IAAI,QAAQ;AAAA,IACpD,CAAC,EAAE,OAAO,CAAC,aAAa;AACpB,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA,EACA,WAAW,MAAM;AACb,QAAI,KAAK,MAAM,SAAS,KAAK,eAAe;AACxC,cAAQ,KAAK,wDAAwD,KAAK,IAAI;AAC9E,aAAO;AAAA,IACX;AACA,QAAI,KAAK,eAAe;AACpB,WAAK,KAAK,QAAQ;AAClB,WAAK,KAAK,QAAQ;AAClB,WAAK,IAAI,QAAQ;AACjB,WAAK,MAAM,QAAQ;AAAA,IACvB;AACA,QAAI,aAAa,KAAK;AACtB,WAAO,WAAW,KAAK,MAAM,KAAK,2BAA2B,IAAI,CAAC;AAAA,EACtE;AAAA,EACA,2BAA2B,MAAM;AAC7B,QAAI,gBAAgB,KAAK;AACzB,QAAI,KAAK,MAAM,WAAW,eAAe;AACrC,aAAO;AAAA,IACX,OACK;AACD,UAAI,YAAY;AAAA,QACZ,MAAM,CAAC;AAAA,QACP,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,QACN,OAAO,CAAC;AAAA,MACZ;AACA,UAAI,IAAI;AACR,UAAI,QAAQ,KAAK,MAAM,SAAS;AAChC,aAAO,IAAI,eAAe;AACtB,kBAAU,KAAK,KAAK,KAAK,KAAK,QAAQ,CAAC,CAAC;AACxC,kBAAU,KAAK,KAAK,KAAK,KAAK,QAAQ,CAAC,CAAC;AACxC,kBAAU,IAAI,KAAK,KAAK,IAAI,QAAQ,CAAC,CAAC;AACtC,kBAAU,MAAM,KAAK,KAAK,MAAM,QAAQ,CAAC,CAAC;AAC1C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,4BAA4B,MAAM;AAC9B,QAAI,gBAAgB,KAAK;AACzB,QAAI,gBAAgB,KAAK,MAAM,IAAI,SAAU,aAAa,OAAO;AAC7D,UAAI,IAAI;AACR,UAAI,YAAY;AAAA,QACZ,MAAM,CAAC;AAAA,QACP,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,QACN,OAAO,CAAC;AAAA,MACZ;AACA,aAAO,IAAI,eAAe;AACtB,kBAAU,KAAK,KAAK,KAAK,KAAK,QAAQ,CAAC,CAAC;AACxC,kBAAU,KAAK,KAAK,KAAK,KAAK,QAAQ,CAAC,CAAC;AACxC,kBAAU,IAAI,KAAK,KAAK,IAAI,QAAQ,CAAC,CAAC;AACtC,kBAAU,MAAM,KAAK,KAAK,MAAM,QAAQ,CAAC,CAAC;AAC1C;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC,EAAE,OAAO,CAAC,KAAK,UAAU;AAAE,aAAQ,SAAU,KAAK,MAAM,SAAS;AAAA,IAAiB,CAAC;AACpF,WAAO;AAAA,EACX;AACJ;;;AC3FA,IAAqB,cAArB,cAAyC,kBAAkB;AAAA,EACvD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,qBAAsB,gBAAgB,kBAAkB;AAC5D,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,oBAAsB,eAAe,iBACpC,eAAe;AACpB,QAAI,iBAAiB,gBAAgB;AACrC,QAAI,YAAc,iBAAiB,gBAC9B,gBAAgB,gBAChB,gBAAgB,kBAChB,kBAAkB;AACvB,QAAI,8BAA8B,iBAAiB;AACnD,WAAQ,kBAAkB,qBAAqB,aAAa,kBAAkB;AAAA,EAClF;AACJ;AACO,SAAS,YAAY,MAAM;AAC9B,SAAO,IAAI,YAAY,EAAE,WAAW,IAAI;AAC5C;;;AClCA,IAAqB,0BAArB,cAAqD,kBAAkB;AAAA,EACnE,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,qBAAuB,iBAAiB,iBACvC,gBAAgB,kBAChB,iBAAiB,kBACjB,gBAAgB;AACrB,WAAQ;AAAA,EACZ;AACJ;AACO,SAAS,wBAAwB,MAAM;AAC1C,SAAO,IAAI,wBAAwB,EAAE,WAAW,IAAI;AACxD;;;ACxBA,IAAqB,gBAArB,cAA2C,kBAAkB;AAAA,EACzD,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,yBAA2B,gBAAgB,kBAC1C,iBAAiB,kBACjB,iBAAiB,mBACjB,gBAAgB,iBAChB,gBAAgB;AACrB,WAAQ;AAAA,EACZ;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,IAAI,cAAc,EAAE,WAAW,IAAI;AAC9C;;;ACzBA,IAAqB,qBAArB,cAAgD,kBAAkB;AAAA,EAC9D,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,8BAAgC,gBAAgB,kBAC/C,iBAAiB,kBACjB,iBAAiB,mBACjB,gBAAgB,iBAChB,gBAAgB;AACrB,QAAI,kBAAkB,KAAK,iBAAiB,gBAAgB,eAAe;AAC3E,WAAQ,+BAA+B;AAAA,EAC3C;AACJ;AACO,SAAS,mBAAmB,MAAM;AACrC,SAAO,IAAI,mBAAmB,EAAE,WAAW,IAAI;AACnD;;;AC1BA,IAAqB,OAArB,cAAkC,kBAAkB;AAAA,EAChD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,oBAAoB,KAAK,iBAAiB,UAAU,SAAS;AACjE,QAAI,mBAAmB,qBAAqB,KAAK,iBAAiB,UAAU,QAAQ;AACpF,QAAI,mBAAmB,qBAAqB,KAAK,iBAAiB,WAAW,OAAO;AACpF,WAAQ,qBAAqB,oBAAoB;AAAA,EACrD;AACJ;AACO,SAAS,KAAK,MAAM;AACvB,SAAO,IAAI,KAAK,EAAE,WAAW,IAAI;AACrC;;;AClBA,IAAqB,kBAArB,cAA6C,kBAAkB;AAAA,EAC3D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,qBAAsB,gBAAgB,kBAAkB;AAC5D,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,aAAa,IAAI,KAAK,EAAE,WAAW;AAAA,MACnC,QAAQ,CAAC,cAAc;AAAA,MACvB,SAAS,CAAC,eAAe;AAAA,MACzB,QAAQ,CAAC,cAAc;AAAA,MACvB,OAAO,CAAC,aAAa;AAAA,IACzB,CAAC;AACD,QAAI,iBAAiB,gBAAgB;AACrC,QAAI,YAAc,iBAAiB,gBAC9B,gBAAgB,gBAChB,gBAAgB,kBAChB,kBAAkB;AACvB,QAAI,8BAA8B,iBAAiB;AACnD,WAAQ,kBAAkB,cAAc,kBAAkB,aACtD;AAAA,EACR;AACJ;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,IAAI,gBAAgB,EAAE,WAAW,IAAI;AAChD;;;ACxCA,IAAqB,oBAArB,cAA+C,kBAAkB;AAAA,EAC7D,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,kBAAkB,kBAAkB;AACxC,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,mBAAmB,iBAAiB;AACxC,QAAI,sBAAwB,iBAAiB,iBACxC,kBAAkB,iBAClB,iBAAiB,kBACjB,iBAAiB;AACtB,WAAQ,kBAAkB,mBAAmB,kBAAkB,oBAAoB;AAAA,EACvF;AACJ;AACO,SAAS,kBAAkB,MAAM;AACpC,SAAO,IAAI,kBAAkB,EAAE,WAAW,IAAI;AAClD;;;AChCA,IAAqB,kBAArB,cAA6C,kBAAkB;AAAA,EAC3D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,mBAAmB,KAAK,iBAAiB,WAAW,QAAQ,KAC5D,KAAK,iBAAiB,SAAS,QAAQ,KACvC,WAAW,aACX,WAAW;AACf,WAAQ;AAAA,EACZ;AACJ;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,IAAI,gBAAgB,EAAE,WAAW,IAAI;AAChD;;;ACpBA,IAAqB,eAArB,cAA0C,kBAAkB;AAAA,EACxD,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,qBAAsB,gBAAgB,kBAAkB;AAC5D,QAAI,cAAc,gBAAgB;AAClC,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,kBAAkB,kBAAkB;AACxC,QAAI,wBAA0B,eAAe,kBACxC,kBAAkB;AACvB,WAAQ,eAAe,kBAAkB,yBAAyB;AAAA,EACtE;AACJ;AACO,SAAS,aAAa,MAAM;AAC/B,SAAO,IAAI,aAAa,EAAE,WAAW,IAAI;AAC7C;;;AC1BA,IAAqB,qBAArB,cAAgD,kBAAkB;AAAA,EAC9D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,YAAY,iBAAiB,iBAC7B,gBAAgB;AACpB,QAAI,eAAe,gBAAgB,kBAC/B,iBAAiB,mBACjB,gBAAgB;AACpB,QAAI,6BAA6B,iBAAiB,kBAC9C,iBAAiB,iBACjB,iBAAiB,iBACjB,gBAAgB;AACpB,WAAQ,aAAa,gBAAgB;AAAA,EACzC;AACJ;AACO,SAAS,mBAAmB,MAAM;AACrC,SAAO,IAAI,mBAAmB,EAAE,WAAW,IAAI;AACnD;;;ACjCA,IAAqB,qBAArB,cAAgD,kBAAkB;AAAA,EAC9D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,kBAAkB,YAAY;AAClC,sBAAkB,mBAAmB,KAAK,iBAAiB,WAAW,QAAQ;AAC9E,sBAAkB,mBAAoB,YAAY,YAAa,KAAK,WAAW;AAC/E,WAAO;AAAA,EACX;AACJ;AACO,SAAS,mBAAmB,MAAM;AACrC,SAAO,IAAI,mBAAmB,EAAE,WAAW,IAAI;AACnD;;;ACnBA,IAAqB,6BAArB,cAAwD,kBAAkB;AAAA,EACtE,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,0BAA0B,YAAY;AAC1C,8BAA0B,2BAA2B,KAAK,iBAAiB,UAAU,OAAO;AAC5F,8BAA0B,2BAA4B,YAAY,YAAa,KAAK,WAAW;AAC/F,WAAO;AAAA,EACX;AACJ;AACO,SAAS,2BAA2B,MAAM;AAC7C,SAAO,IAAI,2BAA2B,EAAE,WAAW,IAAI;AAC3D;;;ACnBA,IAAqB,qBAArB,cAAgD,kBAAkB;AAAA,EAC9D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,kBAAkB,WAAW;AACjC,sBAAkB,mBAAmB,KAAK,iBAAiB,UAAU,QAAQ;AAC7E,sBAAkB,mBAAoB,WAAW,aAAc,KAAK,YAAY;AAChF,WAAO;AAAA,EACX;AACJ;AACO,SAAS,mBAAmB,MAAM;AACrC,SAAO,IAAI,mBAAmB,EAAE,WAAW,IAAI;AACnD;;;ACnBA,IAAqB,6BAArB,cAAwD,kBAAkB;AAAA,EACtE,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,0BAA0B,WAAW;AACzC,8BAA0B,2BAA2B,KAAK,iBAAiB,WAAW,OAAO;AAC7F,8BAA0B,2BAA4B,WAAW,aAAc,KAAK,WAAW;AAC/F,WAAO;AAAA,EACX;AACJ;AACO,SAAS,2BAA2B,MAAM;AAC7C,SAAO,IAAI,2BAA2B,EAAE,WAAW,IAAI;AAC3D;;;ACbA,IAAqB,gBAArB,cAA2C,kBAAkB;AAAA,EACzD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,YAAY,KAAK,cAAc,IAAI;AACvC,gBAAY,aAAa,KAAK,eAAe,IAAI;AACjD,gBAAY,aAAa,KAAK,gBAAgB,IAAI;AAClD,WAAO;AAAA,EACX;AAAA,EACA,cAAc,MAAM,UAAU,MAAM;AAChC,QAAI,MAAM,UAAU,IAAI;AAExB,QAAI,QAAQ,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,GAAG,GAAG,QAAQ,MAAM,EAAE,CAAC;AAC7E,QAAI,SAAS,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,GAAG,GAAG,QAAQ,MAAM,EAAE,CAAC;AAE9E,WAAO,SAAS;AAAA,EACpB;AAAA,EACA,eAAe,MAAM,UAAU,MAAM;AACjC,QAAI,QAAQ,UAAU,IAAI;AAC1B,QAAI,MAAM,UAAU,IAAI;AACxB,QAAI,qBAAqB;AAAA,MACrB,MAAM,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,MAChC,OAAO,KAAK,MAAM,MAAM,OAAO,GAAG;AAAA,MAClC,KAAK,KAAK,IAAI,MAAM,OAAO,GAAG;AAAA,MAC9B,MAAM,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,IACpC;AACA,QAAI,YAAY,mBAAmB,kBAAkB;AACrD,gBAAY,aAAa,2BAA2B,kBAAkB;AACtE,gBAAY,aAAa,mBAAmB,kBAAkB;AAC9D,gBAAY,aAAa,2BAA2B,kBAAkB;AACtE,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,MAAM;AAClB,QAAI,iBAAiB;AAAA,MACjB,MAAM,KAAK,KAAK,CAAC;AAAA,MACjB,OAAO,KAAK,MAAM,CAAC;AAAA,MACnB,KAAK,KAAK,IAAI,CAAC;AAAA,MACf,MAAM,KAAK,KAAK,CAAC;AAAA,IACrB;AACA,QAAI,uBAAuB;AAAA,MACvB,MAAM,KAAK,KAAK,CAAC;AAAA,MACjB,OAAO,KAAK,MAAM,CAAC;AAAA,MACnB,KAAK,KAAK,IAAI,CAAC;AAAA,MACf,MAAM,KAAK,KAAK,CAAC;AAAA,IACrB;AAEA,QAAI,YAAY,qBAAqB,OAAO,qBAAqB;AACjE,WAAO,aAAa,eAAe,QAAQ,qBAAqB;AAAA,EACpE;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,IAAI,cAAc,EAAE,WAAW,IAAI;AAC9C;;;AC7DA,IAAqB,2BAArB,cAAsD,cAAc;AAAA,EAChE,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,YAAY,KAAK,cAAc,MAAM,KAAK;AAC9C,gBAAY,aAAa,KAAK,eAAe,MAAM,KAAK;AACxD,WAAO;AAAA,EACX;AACJ;AACO,SAAS,yBAAyB,MAAM;AAC3C,SAAO,IAAI,yBAAyB,EAAE,WAAW,IAAI;AACzD;;;ACXA,IAAqB,gBAArB,cAA2C,kBAAkB;AAAA,EACzD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,WAAO,KAAK,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAChE;AAAA,EACA,cAAc,MAAM;AAEhB,QAAI,QAAQ,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,CAAC,GAAG,QAAQ,EAAE,CAAC;AACrE,QAAI,SAAS,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,CAAC,GAAG,QAAQ,EAAE,CAAC;AAEtE,WAAO,SAAS;AAAA,EACpB;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,IAAI,cAAc,EAAE,WAAW,IAAI;AAC9C;;;ACPA,IAAI,kBAAkB;AAAA,EAClB,IAAI,wBAAwB;AAAA,EAC5B,IAAI,kBAAkB;AAAA,EACtB,IAAI,cAAc;AAAA,EAClB,IAAI,mBAAmB;AAAA,EACvB,IAAI,gBAAgB;AAAA,EACpB,IAAI,YAAY;AAAA,EAChB,IAAI,gBAAgB;AAAA,EACpB,IAAI,aAAa;AAAA,EACjB,IAAI,mBAAmB;AAAA,EACvB,IAAI,mBAAmB;AAAA,EACvB,IAAI,2BAA2B;AAAA,EAC/B,IAAI,cAAc;AAAA,EAClB,IAAI,yBAAyB;AAAA,EAC7B,IAAI,cAAc;AACtB;AACA,IAAqB,kBAArB,cAA6C,kBAAkB;AAAA,EAC3D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,WAAW,MAAM;AACb,WAAO,gBAAgB,OAAO,SAAU,OAAO,SAAS;AACpD,UAAI,SAAS,QAAQ,WAAW,IAAI;AACpC,aAAO,SAAS;AAAA,IACpB,GAAG,KAAK;AAAA,EACZ;AACJ;AACO,SAAS,QAAQ,MAAM;AAC1B,SAAO,IAAI,gBAAgB,EAAE,WAAW,IAAI;AAChD;;;AC5CA,IAAqB,0BAArB,cAAqD,kBAAkB;AAAA,EACnE,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,qBAAuB,iBAAiB,iBACvC,gBAAgB,kBAChB,iBAAiB,kBACjB,gBAAgB;AACrB,WAAQ;AAAA,EACZ;AACJ;AACO,SAAS,wBAAwB,MAAM;AAC1C,SAAO,IAAI,wBAAwB,EAAE,WAAW,IAAI;AACxD;;;ACxBA,IAAqB,gBAArB,cAA2C,kBAAkB;AAAA,EACzD,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,yBAA2B,gBAAgB,kBAC1C,iBAAiB,kBACjB,iBAAiB,mBACjB,gBAAgB,iBAChB,gBAAgB;AACrB,WAAQ;AAAA,EACZ;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,IAAI,cAAc,EAAE,WAAW,IAAI;AAC9C;;;ACzBA,IAAqB,qBAArB,cAAgD,kBAAkB;AAAA,EAC9D,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,8BAAgC,gBAAgB,kBAC/C,iBAAiB,kBACjB,iBAAiB,mBACjB,gBAAgB,iBAChB,gBAAgB;AACrB,QAAI,kBAAkB,KAAK,iBAAiB,gBAAgB,eAAe;AAC3E,WAAQ,+BAA+B;AAAA,EAC3C;AACJ;AACO,SAAS,mBAAmB,MAAM;AACrC,SAAO,IAAI,mBAAmB,EAAE,WAAW,IAAI;AACnD;;;ACzBA,IAAqB,kBAArB,cAA6C,kBAAkB;AAAA,EAC3D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,qBAAsB,gBAAgB,kBAAkB;AAC5D,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,aAAa,IAAI,KAAK,EAAE,WAAW;AAAA,MACnC,QAAQ,CAAC,cAAc;AAAA,MACvB,SAAS,CAAC,eAAe;AAAA,MACzB,QAAQ,CAAC,cAAc;AAAA,MACvB,OAAO,CAAC,aAAa;AAAA,IACzB,CAAC;AACD,QAAI,iBAAiB,gBAAgB;AACrC,QAAI,YAAc,iBAAiB,iBAC9B,gBAAgB,iBAChB,gBAAgB,iBAChB,kBAAkB;AACvB,QAAI,8BAA8B,iBAAiB;AACnD,WAAQ,kBAAkB,cAAc,aAAa,kBAAkB;AAAA,EAC3E;AACJ;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,IAAI,gBAAgB,EAAE,WAAW,IAAI;AAChD;;;ACvCA,IAAqB,cAArB,cAAyC,kBAAkB;AAAA,EACvD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,qBAAsB,gBAAgB,kBAAkB;AAC5D,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,oBAAsB,gBAAgB,iBACrC,gBAAgB;AACrB,QAAI,iBAAiB,gBAAgB;AACrC,QAAI,YAAc,iBAAiB,iBAC9B,gBAAgB,iBAChB,gBAAgB,iBAChB,kBAAkB;AACvB,QAAI,8BAA8B,iBAAiB;AACnD,WAAQ,kBAAkB,qBAAqB,aAAa,kBAAkB;AAAA,EAClF;AACJ;AACO,SAAS,YAAY,MAAM;AAC9B,SAAO,IAAI,YAAY,EAAE,WAAW,IAAI;AAC5C;;;AClCA,IAAqB,kBAArB,cAA6C,kBAAkB;AAAA,EAC3D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,mBAAmB,KAAK,iBAAiB,UAAU,QAAQ,KAC3D,KAAK,iBAAiB,SAAS,SAAS,KACxC,WAAW,aACX,WAAW;AACf,WAAQ;AAAA,EACZ;AACJ;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,IAAI,gBAAgB,EAAE,WAAW,IAAI;AAChD;;;ACpBA,IAAqB,kBAArB,cAA6C,kBAAkB;AAAA,EAC3D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,cAAc,eAAe,iBAC7B,gBAAgB;AACpB,QAAI,eAAe,gBAAgB,kBAC/B,iBAAiB,mBACjB,gBAAgB;AACpB,QAAI,6BAA6B,gBAAgB,kBAC7C,iBAAiB,kBACjB,iBAAiB,iBACjB,gBAAgB;AACpB,WAAQ,eAAe,gBAAgB;AAAA,EAC3C;AACJ;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,IAAI,gBAAgB,EAAE,WAAW,IAAI;AAChD;;;AC7BA,IAAqB,aAArB,cAAwC,kBAAkB;AAAA,EACtD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,YAAY,KAAK,YAAY,IAAI;AACrC,gBAAY,aAAa,KAAK,eAAe,IAAI;AACjD,gBAAY,aAAa,KAAK,gBAAgB,IAAI;AAClD,WAAO;AAAA,EACX;AAAA,EACA,YAAY,MAAM,UAAU,MAAM;AAC9B,QAAI,MAAM,UAAU,IAAI;AAExB,QAAI,QAAQ,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,GAAG,GAAG,QAAQ,MAAM,EAAE,CAAC;AAC7E,QAAI,SAAS,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,GAAG,GAAG,QAAQ,MAAM,EAAE,CAAC;AAE9E,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,eAAe,MAAM,UAAU,MAAM;AACjC,QAAI,QAAQ,UAAU,IAAI;AAC1B,QAAI,MAAM,UAAU,IAAI;AACxB,QAAI,qBAAqB;AAAA,MACrB,MAAM,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,MAChC,OAAO,KAAK,MAAM,MAAM,OAAO,GAAG;AAAA,MAClC,KAAK,KAAK,IAAI,MAAM,OAAO,GAAG;AAAA,MAC9B,MAAM,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,IACpC;AACA,QAAI,YAAY,mBAAmB,kBAAkB;AACrD,gBAAY,aAAa,mBAAmB,kBAAkB;AAC9D,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,MAAM;AAClB,QAAI,iBAAiB;AAAA,MACjB,MAAM,KAAK,KAAK,CAAC;AAAA,MACjB,OAAO,KAAK,MAAM,CAAC;AAAA,MACnB,KAAK,KAAK,IAAI,CAAC;AAAA,MACf,MAAM,KAAK,KAAK,CAAC;AAAA,IACrB;AACA,QAAI,uBAAuB;AAAA,MACvB,MAAM,KAAK,KAAK,CAAC;AAAA,MACjB,OAAO,KAAK,MAAM,CAAC;AAAA,MACnB,KAAK,KAAK,IAAI,CAAC;AAAA,MACf,MAAM,KAAK,KAAK,CAAC;AAAA,IACrB;AAEA,QAAI,YAAY,qBAAqB,OAAO,qBAAqB;AACjE,WAAO,aAAa,eAAe,QAAQ,qBAAqB;AAAA,EACpE;AACJ;AACO,SAAS,WAAW,MAAM;AAC7B,SAAO,IAAI,WAAW,EAAE,WAAW,IAAI;AAC3C;;;ACzDA,IAAqB,wBAArB,cAAmD,WAAW;AAAA,EAC1D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,YAAY,KAAK,YAAY,MAAM,KAAK;AAC5C,gBAAY,aAAa,KAAK,eAAe,MAAM,KAAK;AACxD,WAAO;AAAA,EACX;AACJ;AACO,SAAS,sBAAsB,MAAM;AACxC,SAAO,IAAI,sBAAsB,EAAE,WAAW,IAAI;AACtD;;;ACTA,IAAqB,eAArB,cAA0C,kBAAkB;AAAA,EACxD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,YAAY,KAAK,YAAY,IAAI;AACrC,gBAAY,aAAa,KAAK,eAAe,IAAI;AACjD,gBAAY,aAAa,KAAK,gBAAgB,IAAI;AAClD,WAAO;AAAA,EACX;AAAA,EACA,YAAY,MAAM,UAAU,MAAM;AAC9B,QAAI,MAAM,UAAU,IAAI;AAExB,QAAI,QAAQ,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,GAAG,GAAG,QAAQ,MAAM,EAAE,CAAC;AAC7E,QAAI,SAAS,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,GAAG,GAAG,QAAQ,MAAM,EAAE,CAAC;AAE9E,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,eAAe,MAAM,UAAU,MAAM;AACjC,QAAI,QAAQ,UAAU,IAAI;AAC1B,QAAI,MAAM,UAAU,IAAI;AACxB,QAAI,qBAAqB;AAAA,MACrB,MAAM,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,MAChC,OAAO,KAAK,MAAM,MAAM,OAAO,GAAG;AAAA,MAClC,KAAK,KAAK,IAAI,MAAM,OAAO,GAAG;AAAA,MAC9B,MAAM,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,IACpC;AACA,QAAI,YAAY,2BAA2B,kBAAkB;AAC7D,gBAAY,aAAa,2BAA2B,kBAAkB;AACtE,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,MAAM;AAClB,QAAI,iBAAiB;AAAA,MACjB,MAAM,KAAK,KAAK,CAAC;AAAA,MACjB,OAAO,KAAK,MAAM,CAAC;AAAA,MACnB,KAAK,KAAK,IAAI,CAAC;AAAA,MACf,MAAM,KAAK,KAAK,CAAC;AAAA,IACrB;AACA,QAAI,uBAAuB;AAAA,MACvB,MAAM,KAAK,KAAK,CAAC;AAAA,MACjB,OAAO,KAAK,MAAM,CAAC;AAAA,MACnB,KAAK,KAAK,IAAI,CAAC;AAAA,MACf,MAAM,KAAK,KAAK,CAAC;AAAA,IACrB;AAEA,QAAI,YAAY,qBAAqB,OAAO,qBAAqB;AACjE,WAAO,aAAa,eAAe,QAAQ,qBAAqB;AAAA,EACpE;AACJ;AACO,SAAS,aAAa,MAAM;AAC/B,SAAO,IAAI,aAAa,EAAE,WAAW,IAAI;AAC7C;;;ACzDA,IAAqB,0BAArB,cAAqD,aAAa;AAAA,EAC9D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,YAAY,KAAK,YAAY,MAAM,KAAK;AAC5C,gBAAY,aAAa,KAAK,eAAe,MAAM,KAAK;AACxD,WAAO;AAAA,EACX;AACJ;AACO,SAAS,wBAAwB,MAAM;AAC1C,SAAO,IAAI,wBAAwB,EAAE,WAAW,IAAI;AACxD;;;ACXA,IAAqB,aAArB,cAAwC,kBAAkB;AAAA,EACtD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,WAAO,KAAK,YAAY,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChE;AAAA,EACA,YAAY,MAAM;AAEd,QAAI,QAAQ,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,CAAC,GAAG,QAAQ,EAAE,CAAC;AACrE,QAAI,SAAS,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,GAAG,CAAC,GAAG,QAAQ,EAAE,CAAC;AAEtE,WAAO,QAAQ;AAAA,EACnB;AACJ;AACO,SAAS,WAAW,MAAM;AAC7B,SAAO,IAAI,WAAW,EAAE,WAAW,IAAI;AAC3C;;;ACPA,IAAI,kBAAkB;AAAA,EAClB,IAAI,wBAAwB;AAAA,EAC5B,IAAI,cAAc;AAAA,EAClB,IAAI,mBAAmB;AAAA,EACvB,IAAI,gBAAgB;AAAA,EACpB,IAAI,YAAY;AAAA,EAChB,IAAI,gBAAgB;AAAA,EACpB,IAAI,gBAAgB;AAAA,EACpB,IAAI,mBAAmB;AAAA,EACvB,IAAI,2BAA2B;AAAA,EAC/B,IAAI,WAAW;AAAA,EACf,IAAI,sBAAsB;AAAA,EAC1B,IAAI,aAAa;AAAA,EACjB,IAAI,wBAAwB;AAAA,EAC5B,IAAI,WAAW;AACnB;AACA,IAAqB,kBAArB,cAA6C,kBAAkB;AAAA,EAC3D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,WAAW,MAAM;AACb,WAAO,gBAAgB,OAAO,SAAU,OAAO,SAAS;AACpD,aAAO,SAAS,QAAQ,WAAW,IAAI;AAAA,IAC3C,GAAG,KAAK;AAAA,EACZ;AACJ;AACO,SAAS,QAAQ,MAAM;AAC1B,SAAO,IAAI,gBAAgB,EAAE,WAAW,IAAI;AAChD;;;AC1CA,IAAqB,gBAArB,cAA2C,kBAAkB;AAAA,EACzD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,aAAa,IAAI,KAAK,EAAE,WAAW;AAAA,MACnC,QAAQ,CAAC,cAAc;AAAA,MACvB,SAAS,CAAC,eAAe;AAAA,MACzB,QAAQ,CAAC,cAAc;AAAA,MACvB,OAAO,CAAC,aAAa;AAAA,IACzB,CAAC;AACD,QAAI,YAAc,iBAAiB,gBAC9B,eAAe,kBACf,iBAAiB;AACtB,QAAI,iBAAkB,gBAAgB;AACtC,WAAQ,kBAAkB,cAAc,aAAa;AAAA,EACzD;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,IAAI,cAAc,EAAE,WAAW,IAAI;AAC9C;;;ACpCA,IAAqB,iBAArB,cAA4C,kBAAkB;AAAA,EAC1D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,iBAAiB,KAAK,MAAM,CAAC;AACjC,QAAI,gBAAgB,KAAK,KAAK,CAAC;AAC/B,QAAI,eAAe,KAAK,IAAI,CAAC;AAC7B,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,kBAAkB,KAAK,MAAM,CAAC;AAClC,QAAI,iBAAiB,KAAK,KAAK,CAAC;AAChC,QAAI,gBAAgB,KAAK,IAAI,CAAC;AAC9B,QAAI,oBAAqB,iBAAiB,iBAAiB;AAC3D,QAAI,iBAAiB,iBAAiB;AACtC,QAAI,kBAAkB,kBAAkB;AACxC,QAAI,qBAAuB,iBAAiB,iBACvC,kBAAkB,oBAClB,kBAAkB;AACvB,WAAQ,kBAAkB,mBAAmB;AAAA,EACjD;AACJ;AACO,SAAS,eAAe,MAAM;AACjC,SAAO,IAAI,eAAe,EAAE,WAAW,IAAI;AAC/C;;;AC1BA,IAAqB,gBAArB,cAA2C,kBAAkB;AAAA,EACzD,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,oBAAoB,KAAK,iBAAiB,UAAU,SAAS;AACjE,QAAI,mBAAmB,qBAAqB,KAAK,iBAAiB,UAAU,QAAQ;AACpF,QAAI,mBAAmB,qBAAqB,KAAK,iBAAiB,WAAW,OAAO;AACpF,WAAQ,qBAAqB,oBAAoB,CAAC;AAAA,EACtD;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,IAAI,cAAc,EAAE,WAAW,IAAI;AAC9C;;;ACnBA,IAAqB,iBAArB,cAA4C,kBAAkB;AAAA,EAC1D,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,oBAAoB,KAAK,iBAAiB,UAAU,SAAS;AACjE,QAAI,mBAAmB,qBAAqB,KAAK,iBAAiB,UAAU,QAAQ;AACpF,QAAI,mBAAmB,qBAAqB,KAAK,iBAAiB,WAAW,OAAO;AACpF,WAAQ,qBAAqB,oBAAoB,CAAC;AAAA,EACtD;AACJ;AACO,SAAS,eAAe,MAAM;AACjC,SAAO,IAAI,eAAe,EAAE,WAAW,IAAI;AAC/C;;;ACnBA,IAAqB,qBAArB,cAAgD,kBAAkB;AAAA,EAC9D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,aAAa,KAAK,IAAI,YAAY,QAAQ;AAC9C,QAAI,oBAAoB,KAAK,IAAI,WAAW,SAAS;AACrD,QAAI,oBAAoB,KAAK,IAAI,WAAW,OAAO;AACnD,QAAI,uBAAuB,aAAa,qBACpC,aAAa;AACjB,WAAO;AAAA,EACX;AACJ;AACO,SAAS,mBAAmB,MAAM;AACrC,SAAO,IAAI,mBAAmB,EAAE,WAAW,IAAI;AACnD;;;ACrBA,IAAqB,qBAArB,cAAgD,kBAAkB;AAAA,EAC9D,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC;AAC1B,QAAI,UAAU,KAAK,IAAI,CAAC;AACxB,QAAI,aAAa,KAAK,IAAI,YAAY,QAAQ;AAC9C,QAAI,oBAAoB,KAAK,IAAI,WAAW,QAAQ;AACpD,QAAI,oBAAoB,KAAK,IAAI,WAAW,OAAO;AACnD,QAAI,uBAAuB,aAAa,qBACpC,aAAa;AACjB,WAAO;AAAA,EACX;AACJ;AACO,SAAS,mBAAmB,MAAM;AACrC,SAAO,IAAI,mBAAmB,EAAE,WAAW,IAAI;AACnD;;;ACVO,SAAS,qBAAqB,OAAO,KAAK;AAC7C,MAAI,SAAS,CAAC,GAAG,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,OAAO,OAAO,OAAO,KAAK;AAC5E,MAAI;AACJ,MAAI,QAAQ,KAAK;AACb,mBAAe,OAAO,IAAI,SAAU,OAAO;AACvC,UAAI,aAAa,MAAM,KAAK,IAAI,QAAQ,GAAG,IAAK,QAAS;AACzD,aAAO,aAAa,IAAI,aAAa;AAAA,IACzC,CAAC;AAAA,EACL,OACK;AACD,mBAAe,OAAO,IAAI,SAAU,OAAO;AACvC,UAAI,aAAa,MAAM,KAAK,IAAI,QAAQ,GAAG,IAAK,QAAS;AACzD,aAAO,aAAa,IAAI,aAAa;AAAA,IACzC,CAAC;AAAA,EACL;AACA,SAAO;AACX;;;ACfO,IAAM,gBAAN,cAA4B,UAAU;AAAA,EACzC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,SAAS,CAAC;AACf,QAAI,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB;AACA,QAAI,SAAS,OAAO,OAAO,CAAC,GAAG,UAAU,KAAK;AAC9C,QAAI,wBAAwB,IAAI,oBAAW,OAAO,mBAAmB,GAAG,MAAM,MAAM,KAAK;AACzF,QAAI,kBAAkB,IAAI,oBAAW,OAAO,aAAa,GAAG,MAAM,MAAM,KAAK;AAC7E,QAAI,iBAAiB,IAAI,oBAAW,OAAO,aAAa,GAAG,MAAM,MAAM,KAAK;AAC5E,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI,SAAS,KAAK,IAAI,OAAO,kBAAkB,OAAO,YAAY,OAAO,YAAY,OAAO,YAAY;AACxG,UAAI,gBAAgB;AACpB,aAAO;AACP,aAAO,MAAM;AAET,8BAAsB,KAAK,KAAK,IAAI;AACpC,8BAAsB,KAAK,KAAK,GAAG;AACnC,wBAAgB,KAAK,KAAK,IAAI;AAC9B,wBAAgB,KAAK,KAAK,GAAG;AAC7B,uBAAe,KAAK,KAAK,IAAI;AAC7B,uBAAe,KAAK,KAAK,GAAG;AAC5B,YAAI,gBAAgB,QAAQ;AACxB;AAAA,QACJ,OACK;AAED,cAAI,kBAAkB,sBAAsB,aAAa,sBAAsB,aAAa;AAE5F,cAAI,YAAY,gBAAgB,aAAa,gBAAgB,aAAa;AAE1E,cAAI,SAAS,iBAAiB,YAAY;AAE1C,cAAI,SAAS,eAAe,aAAa,eAAe,aAAa;AAQrE,mBAAS;AAAA,YACL,YAAY;AAAA,YACZ,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,IAAI,QAAQ,CAAC,MAAM,UAAU;AAC/B,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,MAAM,MAAM,KAAK,KAAK;AAAA,QACtB,KAAK,MAAM,IAAI,KAAK;AAAA,MACxB,CAAC;AACD,UAAI,OAAO,OAAO;AACd,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,EAAE;AAAA,EACtC;AACJ;AACA,cAAc,YAAY;AACnB,SAAS,cAAc,OAAO;AACjC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,cAAc,KAAK,EAAE;AACtC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC1FO,IAAM,uBAAN,cAAmC,eAAe;AAAA,EACrD,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACtB;AACJ;AACO,IAAM,wBAAN,cAAoC,eAAe;AAC1D;AAEO,IAAM,kBAAN,cAA8B,UAAU;AAAA,EAC3C,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,SAAS,MAAM,SAAS,MAAM;AAClC,QAAI,aAAa,IAAI,OAAO,EAAE,QAAQ,MAAM,UAAU,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAChG,QAAI,cAAc,IAAI,IAAI,EAAE,QAAQ,MAAM,WAAW,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AACjH,QAAI;AACJ,SAAK,SAAS,CAAC;AACf,SAAK,YAAa,aAAa;AAC3B,UAAIC;AACJ,UAAI;AACJ,aAAO;AACP,aAAO,MAAM;AACT,YAAI,EAAE,MAAM,IAAI;AAChB,YAAI,KAAK,WAAW,UAAU,KAAK;AACnC,YAAIC,OAAM,YAAY,UAAU,IAAI;AACpC,YAAI,MAAM,UAAaA,QAAO,QAAW;AACrC,mBAAS;AAAA,YACL,QAAQ;AAAA,YACR,OAAO,KAAM,MAAM,aAAcA;AAAA,YACjC,OAAO,KAAM,MAAM,aAAcA;AAAA,UACrC;AAAA,QACJ;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,QAAI,QAAQ,MAAM;AAClB,UAAM,QAAQ,CAAC,UAAU,UAAU;AAC/B,UAAI,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,KAAK,MAAM,IAAI,KAAK;AAAA,QACpB,OAAO,MAAM,MAAM,KAAK;AAAA,MAC5B;AACA,UAAI,SAAS,KAAK,UAAU,KAAK,SAAS;AAC1C,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,QAAI,OAAO,SAAS,QAAW;AAC3B,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AAEJ;AACA,gBAAgB,YAAY;AACrB,SAAS,gBAAgB,OAAO;AACnC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,gBAAgB,KAAK,EAAE;AACxC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACvEO,IAAM,sBAAN,cAAkC,eAAe;AAAA,EACpD,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACtB;AACJ;AACO,IAAM,uBAAN,cAAmC,eAAe;AACzD;AAEO,IAAM,iBAAN,cAA6B,UAAU;AAAA,EAC1C,YAAY,OAAO;AACf,UAAM,KAAK;AACX,QAAI,QAAQ,MAAM;AAClB,QAAI,OAAO,MAAM;AACjB,QAAI,SAAS,MAAM;AACnB,SAAK,SAAS,CAAC;AACf,QAAI,cAAc,IAAI,IAAI,EAAE,QAAQ,MAAM,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,QAAQ,CAAC,MAAM;AAAE,aAAO;AAAA,IAAG,EAAE,CAAC;AAC9G,QAAI,gBAAgB,IAAI,oBAAW,MAAM,SAAS,GAAG,MAAM,MAAM,KAAK;AACtE,SAAK,YAAa,aAAa;AAC3B,UAAI;AACJ,UAAI,OAAO;AACX,UAAIC;AACJ,aAAO,MAAM;AACT,YAAI,EAAE,MAAM,IAAI,IAAI;AACpB,sBAAc,KAAK,IAAI;AACvB,sBAAc,KAAK,GAAG;AACtB,QAAAA,OAAM,YAAY,UAAU,IAAI;AAChC,YAAK,cAAc,eAAgB,IAAI,MAAM,UAAYA,QAAO,QAAW;AACvE,mBAAS;AAAA,YACL,UAAU,cAAc,aAAaA,OAAM,MAAM;AAAA,YACjD,WAAW,cAAc,YAAYA,OAAM,MAAM;AAAA,UACrD;AAAA,QACJ;AACA,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ,EAAG;AACH,SAAK,UAAU,KAAK;AACpB,UAAM,QAAQ,CAAC,UAAU,UAAU;AAC/B,UAAI,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,KAAK,KAAK,KAAK;AAAA,QACf,OAAO,OAAO,KAAK;AAAA,MACvB;AACA,UAAI,SAAS,KAAK,UAAU,KAAK,SAAS;AAC1C,UAAI,OAAO,SAAS,QAAW;AAC3B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,UAAU,OAAO;AACb,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK;AACtC,QAAI,OAAO,SAAS,QAAW;AAC3B,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AAEJ;AACA,eAAe,YAAY;AACpB,SAAS,eAAe,OAAO;AAClC,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,eAAe,KAAK,EAAE;AACvC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;AC/DO,IAAM,UAAN,cAAsB,UAAU;AAAA,EACnC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,QAAQ,MAAM;AACnB,SAAK,QAAQ,MAAM;AACnB,QAAI,eAAe,CAAC;AACpB,QAAI,eAAe,CAAC;AACpB,UAAM,QAAS,aAAa;AACxB,UAAI,UAAU;AACd,UAAI,SAAS;AACb,aAAO,MAAM;AACT,qBAAa,QAAQ,QAAQ,MAAM;AACnC,qBAAa,QAAQ,QAAQ,MAAM;AACnC,iBAAS,QAAQ,SAAS,QAAQ;AAClC,YAAI,UAAU;AACd,eAAO,WAAW,QAAQ,aAAa,OAAO,KAAK,aAAa,OAAO,GAAG;AACtE,cAAI,aAAa,OAAO,IAAI,aAAa,OAAO,GAAG;AAC/C,qBAAS;AAAA,UACb,WACS,aAAa,OAAO,IAAI,aAAa,OAAO,GAAG;AACpD,qBAAS;AAAA,UACb,WACS,aAAa,OAAO,MAAM,aAAa,OAAO,GAAG;AACtD,uBAAW;AAAA,UACf;AAAA,QACJ;AACA,YAAI,WAAW,MAAM;AACjB,yBAAe,CAAC,QAAQ,MAAM;AAC9B,yBAAe,CAAC,QAAQ,MAAM;AAAA,QAClC;AACA,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ;AACA,SAAK,YAAY,MAAM;AACvB,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,MAAM,QAAQ,CAAC,OAAO,UAAU;AACjC,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,QAAQ,KAAK,MAAM,KAAK;AAAA,QACxB,QAAQ,KAAK,MAAM,KAAK;AAAA,MAC5B,CAAC;AACD,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,OAAO,cAAc,OAAO;AACxB,QAAI,MAAM,eAAe;AACrB,YAAM,QAAQ,MAAM,MAAM,QAAQ,IAAI;AACtC,YAAM,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAAA,IAC1C;AAAA,EACJ;AAAA,EACA,UAAU,QAAQ,QAAQ;AACtB,WAAO,KAAK,UAAU,KAAK;AAAA,MACvB;AAAA,MACA;AAAA,IACJ,CAAC,EAAE;AAAA,EACP;AAEJ;AACA,QAAQ,YAAY;AACb,SAAS,QAAQ,OAAO;AAC3B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,QAAQ,KAAK,EAAE;AAChC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;;;ACrEO,IAAM,YAAN,cAAwB,UAAU;AAAA,EACrC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,QAAQ,MAAM;AACnB,SAAK,QAAQ,MAAM;AACnB,QAAI,eAAe,CAAC;AACpB,QAAI,eAAe,CAAC;AACpB,UAAM,QAAS,aAAa;AACxB,UAAI,UAAU;AACd,UAAI,SAAS;AACb,aAAO,MAAM;AACT,qBAAa,QAAQ,QAAQ,MAAM;AACnC,qBAAa,QAAQ,QAAQ,MAAM;AACnC,iBAAS,QAAQ,SAAS,QAAQ;AAClC,YAAI,UAAU;AACd,eAAO,WAAW,QAAQ,aAAa,OAAO,KAAK,aAAa,OAAO,GAAG;AACtE,cAAI,aAAa,OAAO,IAAI,aAAa,OAAO,GAAG;AAC/C,qBAAS;AAAA,UACb,WACS,aAAa,OAAO,IAAI,aAAa,OAAO,GAAG;AACpD,qBAAS;AAAA,UACb,WACS,aAAa,OAAO,MAAM,aAAa,OAAO,GAAG;AACtD,uBAAW;AAAA,UACf;AAAA,QACJ;AACA,YAAI,WAAW,MAAM;AACjB,yBAAe,CAAC,QAAQ,MAAM;AAC9B,yBAAe,CAAC,QAAQ,MAAM;AAAA,QAClC;AACA,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ;AACA,SAAK,YAAY,MAAM;AACvB,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,MAAM,QAAQ,CAAC,OAAO,UAAU;AACjC,UAAI,SAAS,KAAK,UAAU,KAAK;AAAA,QAC7B,QAAQ,KAAK,MAAM,KAAK;AAAA,QACxB,QAAQ,KAAK,MAAM,KAAK;AAAA,MAC5B,CAAC;AACD,UAAI,OAAO,UAAU,QAAW;AAC5B,aAAK,OAAO,KAAK,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,OAAO,cAAc,OAAO;AACxB,QAAI,MAAM,eAAe;AACrB,YAAM,QAAQ,MAAM,MAAM,QAAQ,IAAI;AACtC,YAAM,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAAA,IAC1C;AAAA,EACJ;AAAA,EACA,UAAU,QAAQ,QAAQ;AACtB,WAAO,KAAK,UAAU,KAAK;AAAA,MACvB;AAAA,MACA;AAAA,IACJ,CAAC,EAAE;AAAA,EACP;AAEJ;AACA,UAAU,YAAY;AACf,SAAS,UAAU,OAAO;AAC7B,YAAU,cAAc,KAAK;AAC7B,MAAI,SAAS,IAAI,UAAU,KAAK,EAAE;AAClC,MAAI,MAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,EACnB;AACA,YAAU,cAAc,KAAK;AAC7B,SAAO;AACX;", "names": ["sum", "sma", "sma", "format", "MACD", "format", "period", "format", "period", "period", "sma", "sd", "sum", "format", "sma", "sd", "sum", "format", "format", "format", "format", "lastATR", "lastAPDM", "lastAMDM", "format", "wema", "roc", "format", "kst", "format", "format", "format", "ema", "format", "cci", "sum", "format", "format", "rsi", "stochastic", "format", "format", "KeltnerChannelsOutput", "atr", "atr"]}