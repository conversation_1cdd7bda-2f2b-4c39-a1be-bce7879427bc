{"version": 3, "file": "log.cjs.js", "sources": ["../../src/helpers/log.ts"], "sourcesContent": ["export interface ILogger {\r\n  debug(name: string, ...args: unknown[]): void;\r\n  info(name: string, ...args: unknown[]): void;\r\n  warn(name: string, ...args: unknown[]): void;\r\n  error(name: string, ...args: unknown[]): void;\r\n}\r\n\r\nexport enum Log {\r\n  NONE = 0,\r\n  ERROR = 1,\r\n  WARN = 2,\r\n  INFO = 3,\r\n  DEBUG = 4\r\n}\r\n\r\n// Module state\r\nlet currentLevel = Log.NONE;\r\nlet currentLogger: ILogger = {\r\n  debug: (name, ...args) => console.debug(`[${name}]`, ...args),\r\n  info: (name, ...args) => console.info(`[${name}]`, ...args),\r\n  warn: (name, ...args) => console.warn(`[${name}]`, ...args),\r\n  error: (name, ...args) => console.error(`[${name}]`, ...args)\r\n};\r\n\r\nexport const LogManager = {\r\n  reset: () => { currentLevel = Log.INFO; },\r\n  setLevel: (value: Log) => { currentLevel = value; },\r\n  setLogger: (value: ILogger) => { currentLogger = value; }\r\n};\r\n\r\nexport class Logger {\r\n  constructor(private name: string, private method?: string) {}\r\n\r\n  debug(...args: unknown[]): void {\r\n    if (currentLevel >= Log.DEBUG) currentLogger.debug(this.format(), ...args);\r\n  }\r\n\r\n  info(...args: unknown[]): void {\r\n    if (currentLevel >= Log.INFO) currentLogger.info(this.format(), ...args);\r\n  }\r\n\r\n  warn(...args: unknown[]): void {\r\n    if (currentLevel >= Log.WARN) currentLogger.warn(this.format(), ...args);\r\n  }\r\n\r\n  error(...args: unknown[]): void {\r\n    if (currentLevel >= Log.ERROR) currentLogger.error(this.format(), ...args);\r\n  }\r\n\r\n  private format(): string {\r\n    return this.method ? `${this.name}.${this.method}` : this.name;\r\n  }\r\n}\r\n\r\nexport const log = new Logger('Chart');\r\n"], "names": ["Log", "currentLevel", "<PERSON><PERSON><PERSON><PERSON>", "name", "args", "LogManager", "value", "<PERSON><PERSON>", "method", "log"], "mappings": "gFAOY,IAAAA,GAAAA,IACVA,EAAAA,EAAA,KAAO,CAAP,EAAA,OACAA,EAAAA,EAAA,MAAQ,CAAR,EAAA,QACAA,EAAAA,EAAA,KAAO,CAAP,EAAA,OACAA,EAAAA,EAAA,KAAO,CAAP,EAAA,OACAA,EAAAA,EAAA,MAAQ,CAAR,EAAA,QALUA,IAAAA,GAAA,CAAA,CAAA,EASZ,IAAIC,EAAe,EACfC,EAAyB,CAC3B,MAAO,CAACC,KAASC,IAAS,QAAQ,MAAM,IAAID,CAAI,IAAK,GAAGC,CAAI,EAC5D,KAAM,CAACD,KAASC,IAAS,QAAQ,KAAK,IAAID,CAAI,IAAK,GAAGC,CAAI,EAC1D,KAAM,CAACD,KAASC,IAAS,QAAQ,KAAK,IAAID,CAAI,IAAK,GAAGC,CAAI,EAC1D,MAAO,CAACD,KAASC,IAAS,QAAQ,MAAM,IAAID,CAAI,IAAK,GAAGC,CAAI,CAC9D,EAEO,MAAMC,EAAa,CACxB,MAAO,IAAM,CAAiBJ,EAAA,CAAU,EACxC,SAAWK,GAAe,CAAiBL,EAAAK,CAAO,EAClD,UAAYA,GAAmB,CAAkBJ,EAAAI,CAAA,CACnD,EAEO,MAAMC,CAAO,CAClB,YAAoBJ,EAAsBK,EAAiB,CAAvC,KAAA,KAAAL,EAAsB,KAAA,OAAAK,CAAA,CAE1C,SAASJ,EAAuB,CAC1BH,GAAgB,GAAyBC,EAAA,MAAM,KAAK,OAAO,EAAG,GAAGE,CAAI,CAAA,CAG3E,QAAQA,EAAuB,CACzBH,GAAgB,GAAwBC,EAAA,KAAK,KAAK,OAAO,EAAG,GAAGE,CAAI,CAAA,CAGzE,QAAQA,EAAuB,CACzBH,GAAgB,GAAwBC,EAAA,KAAK,KAAK,OAAO,EAAG,GAAGE,CAAI,CAAA,CAGzE,SAASA,EAAuB,CAC1BH,GAAgB,GAAyBC,EAAA,MAAM,KAAK,OAAO,EAAG,GAAGE,CAAI,CAAA,CAGnE,QAAiB,CAChB,OAAA,KAAK,OAAS,GAAG,KAAK,IAAI,IAAI,KAAK,MAAM,GAAK,KAAK,IAAA,CAE9D,CAEa,MAAAK,EAAM,IAAIF,EAAO,OAAO"}