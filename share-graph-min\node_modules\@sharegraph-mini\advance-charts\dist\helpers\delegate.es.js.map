{"version": 3, "file": "delegate.es.js", "sources": ["../../src/helpers/delegate.ts"], "sourcesContent": ["export type Callback<T1 = void, T2 = void, T3 = void> = (param1: T1, param2: T2, param3: T3) => void;\r\n\r\nexport interface ISubscription<T1 = void, T2 = void, T3 = void> {\r\n\tsubscribe(callback: Callback<T1, T2, T3>, linkedObject?: unknown, singleshot?: boolean): void;\r\n\tunsubscribe(callback: Callback<T1, T2, T3>): void;\r\n\tunsubscribeAll(linkedObject: unknown): void;\r\n  lastParams(): [T1, T2, T3];\r\n}\r\n\r\ninterface Listener<T1, T2, T3> {\r\n\tcallback: Callback<T1, T2, T3>;\r\n\tlinkedObject?: unknown;\r\n\tsingleshot: boolean;\r\n}\r\n\r\nexport class Delegate<T1 = void, T2 = void, T3 = void> implements ISubscription<T1, T2, T3> {\r\n  private _listeners: Listener<T1, T2, T3>[] = [];\r\n\r\n  private _params: [T1, T2, T3] = [undefined as unknown as T1, undefined as unknown as T2, undefined as unknown as T3];\r\n\r\n  public fire(param1: T1, param2: T2, param3: T3) {\r\n    this._params = [param1, param2, param3];\r\n    const listenersSnapshot = [...this._listeners]\r\n    this._listeners = this._listeners.filter(listener => !listener.singleshot);\r\n\r\n    listenersSnapshot.forEach(listener => listener.callback(param1, param2, param3))\r\n  }\r\n\r\n  public lastParams(): [T1, T2, T3] {\r\n    return this._params;\r\n  }\r\n\r\n  public subscribe(callback: Callback<T1, T2, T3>, linkedObject?: unknown, singleshot?: boolean): void {\r\n    this._listeners.push({callback, linkedObject, singleshot: Boolean(singleshot)})\r\n  }\r\n\r\n  public unsubscribe(callback: Callback<T1, T2, T3>): void {\r\n    const index = this._listeners.findIndex(listener => listener.callback === callback);\r\n\r\n    if(index > -1) {\r\n      this._listeners.splice(index, 1)\r\n    }\r\n  }\r\n\r\n  public unsubscribeAll(linkedObject: unknown): void {\r\n    this._listeners = this._listeners.filter(listener => listener.linkedObject !== linkedObject)\r\n  }\r\n\r\n\r\n  hasListener() {\r\n    return Boolean(this._listeners.length)\r\n  }\r\n  destroy() {\r\n    this._listeners = []\r\n  }\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nexport type IPublicDelegate<T extends Delegate<any, any, any>> = T extends Delegate<infer T1, infer T2, infer T3>\r\n  ? ISubscription<T1, T2, T3>\r\n  : never;\r\n\r\n"], "names": ["Delegate", "__publicField", "param1", "param2", "param3", "listenersSnapshot", "listener", "callback", "linkedObject", "singleshot", "index"], "mappings": ";;;AAeO,MAAMA,EAA+E;AAAA,EAArF;AACG,IAAAC,EAAA,oBAAqC,CAAC;AAEtC,IAAAA,EAAA,iBAAwB,CAAC,QAA4B,QAA4B,MAA0B;AAAA;AAAA,EAE5G,KAAKC,GAAYC,GAAYC,GAAY;AAC9C,SAAK,UAAU,CAACF,GAAQC,GAAQC,CAAM;AACtC,UAAMC,IAAoB,CAAC,GAAG,KAAK,UAAU;AAC7C,SAAK,aAAa,KAAK,WAAW,OAAO,CAAYC,MAAA,CAACA,EAAS,UAAU,GAEzED,EAAkB,QAAQ,CAAYC,MAAAA,EAAS,SAASJ,GAAQC,GAAQC,CAAM,CAAC;AAAA,EAAA;AAAA,EAG1E,aAA2B;AAChC,WAAO,KAAK;AAAA,EAAA;AAAA,EAGP,UAAUG,GAAgCC,GAAwBC,GAA4B;AAC9F,SAAA,WAAW,KAAK,EAAC,UAAAF,GAAU,cAAAC,GAAc,YAAY,EAAQC,GAAY;AAAA,EAAA;AAAA,EAGzE,YAAYF,GAAsC;AACvD,UAAMG,IAAQ,KAAK,WAAW,UAAU,CAAYJ,MAAAA,EAAS,aAAaC,CAAQ;AAElF,IAAGG,IAAQ,MACJ,KAAA,WAAW,OAAOA,GAAO,CAAC;AAAA,EACjC;AAAA,EAGK,eAAeF,GAA6B;AACjD,SAAK,aAAa,KAAK,WAAW,OAAO,CAAYF,MAAAA,EAAS,iBAAiBE,CAAY;AAAA,EAAA;AAAA,EAI7F,cAAc;AACL,WAAA,EAAQ,KAAK,WAAW;AAAA,EAAM;AAAA,EAEvC,UAAU;AACR,SAAK,aAAa,CAAC;AAAA,EAAA;AAEvB;"}