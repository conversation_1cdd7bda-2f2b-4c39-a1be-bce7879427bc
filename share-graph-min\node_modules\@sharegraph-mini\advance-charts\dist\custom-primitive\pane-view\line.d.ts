import { BitmapCoordinatesRenderingScope } from 'fancy-canvas';
import { PrimitivePaneViewBase } from '../primitive-base';
import { Coordinate, LineStyle, Time } from 'lightweight-charts';

interface LinePrimitiveOptions {
    lineColor: string;
    lineWidth: number;
    lineDash: LineStyle;
}
export declare const LinePrimitiveOptionsDefault: LinePrimitiveOptions;
export type LineData = {
    x: Coordinate;
    price: number;
} | {
    time: Time;
    price: number;
};
export declare class LinePrimitivePaneView extends PrimitivePaneViewBase<LinePrimitiveOptions, LineData> {
    getXCoordinate(data: LineData): Coordinate | null;
    _drawImpl(renderingScope: BitmapCoordinatesRenderingScope): void;
    defaultOptions(): LinePrimitiveOptions;
}
export {};
