import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface MomentumIndicatorOptions extends ChartIndicatorOptions {
    period: number;
    usePercentage: boolean;
    color: string;
    zeroLineColor: string;
}
export declare const defaultOptions: MomentumIndicatorOptions;
export type MomentumData = readonly [Nominal<number, 'Momentum'>];
export default class MomentumIndicator extends ChartIndicator<MomentumIndicatorOptions, MomentumData> {
    momentumSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<MomentumIndicatorOptions>, paneIndex?: number);
    applyIndicatorData(): void;
    formula(c: Context): MomentumData | undefined;
    _applyOptions(options: Partial<MomentumIndicatorOptions>): void;
    getDefaultOptions(): MomentumIndicatorOptions;
    remove(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
