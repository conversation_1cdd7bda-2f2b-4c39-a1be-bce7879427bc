"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("./dayjs-setup.cjs.js");const s=require("dayjs");function u(e){if(typeof e=="number")return new Date(e*1e3);if(typeof e=="string")return new Date(e);if(typeof e=="object")return new Date(e.year,e.month-1,e.day);throw new Error("Do not support time")}function y(e){return Math.floor(e.getTime()/1e3)}function T(e){return s(u(e))}function d(e){return typeof e=="number"?e:Math.floor(u(e).getTime()/1e3)}function h(e){return e.unix()}const c=(e,n)=>e===n?0:e<n?-1:1;function l(e,n,t,r=c){let o=0,i=e.length-1;for(;o<=i;){const a=Math.floor((o+i)/2),m=t(e[a]),f=r(m,n);if(f===0)return a;f<0?o=a+1:i=a-1}return-1}function p(e,n,t,r=c){const o=l(e,n,t,r);return o!==-1?e[o]:void 0}function x(e){return n=>{const t=n(),r=(t==null?void 0:t.priceRange)??e;return{priceRange:{maxValue:Math.max(e.maxValue,r.maxValue),minValue:Math.min(e.minValue,r.minValue)}}}}exports.autoScaleInfoProviderCreator=x;exports.binarySearch=p;exports.binarySearchIndex=l;exports.dateToTime=y;exports.dayjsToTime=h;exports.defaultCompare=c;exports.timeToDate=u;exports.timeToDayjs=T;exports.timeToUnix=d;
//# sourceMappingURL=utils.cjs.js.map
