import { PrimitivePaneViewBase as m } from "../primitive-base.es.js";
import { ensureNotNull as d } from "../../helpers/assertions.es.js";
import { binarySearchIndex as f, timeToUnix as s } from "../../helpers/utils.es.js";
const u = {
  backgroundColor: "#2196f31a"
};
class P extends m {
  dataVisible() {
    const n = this.getVisibleRange();
    if (!n) return [];
    const t = this.data;
    let o = f(t, s(n.from), (e) => s(e.time));
    o === -1 && (o = 1);
    let i = f(t, s(n.to), (e) => s(e.time));
    return i === -1 && (i = t.length), t.slice(o - 1, i + 2);
  }
  drawBackground(n) {
    const t = this.dataVisible().map((o) => ({
      x: d(this.timeToCoordinate(o.time)),
      upperCoor: d(this.priceToCoordinate(o.upper)),
      lowerCoor: d(this.priceToCoordinate(o.lower))
    }));
    t.length < 2 || n.useBitmapCoordinateSpace((o) => {
      const i = o.context;
      i.scale(o.horizontalPixelRatio, o.verticalPixelRatio), i.beginPath();
      const e = new Path2D(), c = 0, p = t.length, l = t[c];
      e.moveTo(
        l.x,
        l.upperCoor
      );
      for (let r = c + 1; r < p; r++) {
        const a = t[r];
        e.lineTo(
          a.x,
          a.upperCoor
        );
      }
      for (let r = p - 1; r >= c; r--) {
        const a = t[r];
        e.lineTo(a.x, a.lowerCoor);
      }
      e.lineTo(
        l.x,
        l.lowerCoor
      ), e.closePath(), i.fillStyle = this.options.backgroundColor, i.fill(e);
    });
  }
  defaultOptions() {
    return u;
  }
}
export {
  u as BandPrimitiveOptionsDefault,
  P as BandPrimitivePaneView
};
//# sourceMappingURL=band.es.js.map
