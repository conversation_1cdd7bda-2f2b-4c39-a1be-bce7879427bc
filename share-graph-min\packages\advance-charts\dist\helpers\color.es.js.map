{"version": 3, "file": "color.es.js", "sources": ["../../src/helpers/color.ts"], "sourcesContent": ["import {memoize} from \"es-toolkit\";\r\n\r\n/**\r\n * For colors which fall within the sRGB space, the browser can\r\n * be used to convert the color string into a rgb /rgba string.\r\n *\r\n * For other colors, it will be returned as specified (i.e. for\r\n * newer formats like display-p3)\r\n *\r\n * See: https://www.w3.org/TR/css-color-4/#serializing-sRGB-values\r\n */\r\nfunction getRgbStringViaBrowser(color: string): string {\r\n\tconst element = document.createElement('div');\r\n\telement.style.display = 'none';\r\n\t// We append to the body as it is the most reliable way to get a color reading\r\n\t// appending to the chart container or similar element can result in the following\r\n\t// getComputedStyle returning empty strings on each check.\r\n\tdocument.body.appendChild(element);\r\n\telement.style.color = color;\r\n\tconst computed = window.getComputedStyle(element).color;\r\n\tdocument.body.removeChild(element);\r\n\treturn computed;\r\n}\r\n\r\nexport const parseColor = memoize((color: string) => {\r\n  const computed = getRgbStringViaBrowser(color);\r\n\r\n  const match = computed.match(\r\n    /^rgba?\\s*\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d+))?\\)$/\r\n  );\r\n\r\n  if (!match) {\r\n    throw new Error(`Failed to parse color: ${color}`);\r\n  }\r\n\r\n  return [\r\n    parseInt(match[1], 10) as number,\r\n    parseInt(match[2], 10) as number,\r\n    parseInt(match[3], 10) as number,\r\n    (match[4] ? parseFloat(match[4]) : 1) as number,\r\n  ];\r\n})\r\n\r\nexport class Color {\r\n  /**\r\n  * We fallback to RGBA here since supporting alpha transformations\r\n  * on wider color gamuts would currently be a lot of extra code\r\n  * for very little benefit due to actual usage.\r\n  */\r\n  static applyAlpha(color: string, alpha: number): string {\r\n   // special case optimization\r\n   if (color === 'transparent') {\r\n     return color;\r\n   }\r\n   if(alpha === 1) return color;\r\n\r\n   const originRgba = parseColor(color);\r\n   const originAlpha = originRgba[3];\r\n   return `rgba(${originRgba[0]}, ${originRgba[1]}, ${originRgba[2]}, ${\r\n     alpha * originAlpha\r\n   })`;\r\n }\r\n\r\n static parseColor(color: string) {\r\n  return parseColor(color)\r\n }\r\n}\r\n"], "names": ["getRgbStringViaBrowser", "color", "element", "computed", "parseColor", "memoize", "match", "Color", "alpha", "originRgba", "originAlpha"], "mappings": ";AAWA,SAASA,EAAuBC,GAAuB;AAChD,QAAAC,IAAU,SAAS,cAAc,KAAK;AAC5C,EAAAA,EAAQ,MAAM,UAAU,QAIf,SAAA,KAAK,YAAYA,CAAO,GACjCA,EAAQ,MAAM,QAAQD;AACtB,QAAME,IAAW,OAAO,iBAAiBD,CAAO,EAAE;AACzC,kBAAA,KAAK,YAAYA,CAAO,GAC1BC;AACR;AAEa,MAAAC,IAAaC,EAAQ,CAACJ,MAAkB;AAGnD,QAAMK,IAFWN,EAAuBC,CAAK,EAEtB;AAAA,IACrB;AAAA,EACF;AAEA,MAAI,CAACK;AACH,UAAM,IAAI,MAAM,0BAA0BL,CAAK,EAAE;AAG5C,SAAA;AAAA,IACL,SAASK,EAAM,CAAC,GAAG,EAAE;AAAA,IACrB,SAASA,EAAM,CAAC,GAAG,EAAE;AAAA,IACrB,SAASA,EAAM,CAAC,GAAG,EAAE;AAAA,IACpBA,EAAM,CAAC,IAAI,WAAWA,EAAM,CAAC,CAAC,IAAI;AAAA,EACrC;AACF,CAAC;AAEM,MAAMC,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,OAAO,WAAWN,GAAeO,GAAuB;AAKpD,QAHCP,MAAU,iBAGXO,MAAU,EAAU,QAAAP;AAEjB,UAAAQ,IAAaL,EAAWH,CAAK,GAC7BS,IAAcD,EAAW,CAAC;AAChC,WAAO,QAAQA,EAAW,CAAC,CAAC,KAAKA,EAAW,CAAC,CAAC,KAAKA,EAAW,CAAC,CAAC,KAC9DD,IAAQE,CACV;AAAA,EAAA;AAAA,EAGF,OAAO,WAAWT,GAAe;AAChC,WAAOG,EAAWH,CAAK;AAAA,EAAA;AAEzB;"}