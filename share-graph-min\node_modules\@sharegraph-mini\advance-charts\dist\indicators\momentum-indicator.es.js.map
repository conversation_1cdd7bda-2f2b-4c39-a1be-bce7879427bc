{"version": 3, "file": "momentum-indicator.es.js", "sources": ["../../src/indicators/momentum-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface MomentumIndicatorOptions extends ChartIndicatorOptions {\r\n  period: number,\r\n  usePercentage: boolean,\r\n  color: string,\r\n  zeroLineColor: string\r\n};\r\n\r\nexport const defaultOptions: MomentumIndicatorOptions = {\r\n  period: 14,\r\n  usePercentage: false,\r\n  color: '#2b97f1',\r\n  zeroLineColor: '#808080',\r\n  overlay: false\r\n}\r\n\r\nexport type MomentumData = readonly [Nominal<number, 'Momentum'>]\r\n\r\nexport default class MomentumIndicator extends ChartIndicator<MomentumIndicatorOptions, MomentumData> {\r\n  momentumSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<MomentumIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options);\r\n\r\n    this.momentumSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 2,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'momentum'\r\n    }, paneIndex)\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n    const momentumData: SingleValueData[] = []\r\n\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      const time = bar.time as Time;\r\n\r\n      if(!value) continue;\r\n      const [momentum] = value;\r\n      if(!isNaN(momentum)) momentumData.push({time, value: momentum})\r\n    }\r\n\r\n    this.momentumSeries.setData(momentumData)\r\n  }\r\n\r\n  formula(c: Context) {\r\n      const period = this.options.period;\r\n      const currentClose = c.symbol.close;\r\n\r\n      const closeSeries = c.new_var(currentClose, period + 1);\r\n      if (!closeSeries.calculable()) return;\r\n\r\n      const closeNPeriodsAgo = closeSeries.get(period);\r\n\r\n      let momentum: number;\r\n\r\n      if (this.options.usePercentage) {\r\n          momentum = ((currentClose - closeNPeriodsAgo) / closeNPeriodsAgo) * 100;\r\n      } else {\r\n          momentum = currentClose - closeNPeriodsAgo;\r\n      }\r\n\r\n      return [momentum as Nominal<number, 'Momentum'>] as MomentumData;\r\n  }\r\n\r\n  _applyOptions(options: Partial<MomentumIndicatorOptions>): void {\r\n    if(options.period || options.usePercentage) {\r\n      this.calcIndicatorData()\r\n    }\r\n\r\n    if(options.color) this.momentumSeries.applyOptions({color: options.color})\r\n\r\n    this.applyIndicatorData()\r\n  }\r\n\r\n  getDefaultOptions() {\r\n    return defaultOptions\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove()\r\n    this.chart.removeSeries(this.momentumSeries)\r\n  }\r\n\r\n  setPaneIndex(paneIndex: number): void {\r\n    this.momentumSeries.moveToPane(paneIndex);\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.momentumSeries.getPane().paneIndex()\r\n  }\r\n}\r\n"], "names": ["defaultOptions", "MomentumIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "momentumData", "bar", "value", "time", "momentum", "c", "period", "currentClose", "closeSeries", "closeNPeriodsAgo"], "mappings": ";;;;;AAWO,MAAMA,IAA2C;AAAA,EACtD,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,OAAO;AAAA,EACP,eAAe;AAAA,EACf,SAAS;AACX;AAIA,MAAqBC,UAA0BC,EAAuD;AAAA,EAGpG,YAAYC,GAAkBC,GAA6CC,GAAoB;AAC7F,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAKO,SAAA,iBAAiBH,EAAM,UAAUI,GAAY;AAAA,MAChD,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,OACbF,CAAS;AAAA,EAAA;AAAA,EAGd,qBAA2B;AACzB,UAAMG,IAAkC,CAAC;AAE/B,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI,OACZE,IAAOF,EAAI;AAEjB,UAAG,CAACC,EAAO;AACL,YAAA,CAACE,CAAQ,IAAIF;AAChB,MAAC,MAAME,CAAQ,KAAGJ,EAAa,KAAK,EAAC,MAAAG,GAAM,OAAOC,GAAS;AAAA,IAAA;AAG3D,SAAA,eAAe,QAAQJ,CAAY;AAAA,EAAA;AAAA,EAG1C,QAAQK,GAAY;AACV,UAAAC,IAAS,KAAK,QAAQ,QACtBC,IAAeF,EAAE,OAAO,OAExBG,IAAcH,EAAE,QAAQE,GAAcD,IAAS,CAAC;AAClD,QAAA,CAACE,EAAY,aAAc;AAEzB,UAAAC,IAAmBD,EAAY,IAAIF,CAAM;AAE3C,QAAAF;AAEA,WAAA,KAAK,QAAQ,gBACAA,KAAAG,IAAeE,KAAoBA,IAAoB,MAEpEL,IAAWG,IAAeE,GAGvB,CAACL,CAAuC;AAAA,EAAA;AAAA,EAGnD,cAAcR,GAAkD;AAC3D,KAAAA,EAAQ,UAAUA,EAAQ,kBAC3B,KAAK,kBAAkB,GAGtBA,EAAQ,SAAY,KAAA,eAAe,aAAa,EAAC,OAAOA,EAAQ,OAAM,GAEzE,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,oBAAoB;AACX,WAAAJ;AAAA,EAAA;AAAA,EAGT,SAAe;AACb,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,cAAc;AAAA,EAAA;AAAA,EAG7C,aAAaK,GAAyB;AAC/B,SAAA,eAAe,WAAWA,CAAS;AAAA,EAAA;AAAA,EAG1C,eAAuB;AACrB,WAAO,KAAK,eAAe,QAAQ,EAAE,UAAU;AAAA,EAAA;AAEnD;"}