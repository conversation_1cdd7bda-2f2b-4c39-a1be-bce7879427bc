import { defaultHorzScaleBehavior as u } from "lightweight-charts";
import { Period as t } from "./i-advance-chart.es.js";
const S = new (u())(), s = 60, i = s * 60, n = i * 24, E = n * 7, _ = n * 3;
function T(a, c) {
  const e = S.key(a), m = c.period, o = c.times;
  switch (m) {
    case t.minute:
      return e - e % (s * o);
    case t.hour:
      return e - e % (i * o);
    case t.day:
      return e - e % (n * o);
    case t.week: {
      const r = e + _;
      return e - r % (E * o);
    }
    case t.month: {
      const r = new Date(e * 1e3);
      return Math.floor(Date.UTC(r.getUTCFullYear(), r.getUTCMonth(), 1, 0, 0, 0) / 1e3);
    }
    default:
      throw new Error("Invalid period");
  }
}
export {
  T as timeKey
};
//# sourceMappingURL=time-key.es.js.map
