{"version": 3, "file": "advance-chart.es.js", "sources": ["../../src/advance-chart/advance-chart.ts"], "sourcesContent": ["import {\r\n  AreaSeries,\r\n  BarSeries,\r\n  BaselineSeries,\r\n  CandlestickSeries,\r\n  createChart,\r\n  DeepPartial,\r\n  IChartApi,\r\n  ISeriesApi,\r\n  LastPriceAnimationMode,\r\n  LineSeries,\r\n  Logical,\r\n  PriceScaleMode,\r\n  SeriesType,\r\n  TickMarkFormatter,\r\n  Time,\r\n} from 'lightweight-charts';\r\nimport {\r\n  ChartIndicator,\r\n  downColor,\r\n  upColor,\r\n} from '../indicators/abstract-indicator';\r\nimport { OHLCVExtraData, OHLCVSimple } from '../interface';\r\nimport { Delegate, IPublicDelegate, ISubscription } from '../helpers/delegate';\r\nimport { IndicatorFactory, VolumeIndicator } from '../indicators';\r\nimport { binarySearchIndex, timeToDate, timeToUnix } from '../helpers/utils';\r\nimport { cloneDeep, merge } from 'es-toolkit';\r\nimport {NumberFormatterFactory} from '../helpers/number-formatter';\r\nimport {VolumeIndicatorOptions} from '../indicators/volume-indicator';\r\nimport {IAdvanceChart, IAdvanceChartOptions, IAdvanceChartType, IGroupIndicatorByPane, Interval, Period} from './i-advance-chart';\r\nimport {DisplayTimezone} from './DisplayTimezone';\r\n\r\n\r\nexport const defaultAdvanceChartOptions: IAdvanceChartOptions = {\r\n  upColor,\r\n  downColor,\r\n  mainColor: '#3594e7',\r\n  highLowLineVisible: false,\r\n  highLineColor: upColor,\r\n  lowLineColor: downColor,\r\n  priceScaleMode: PriceScaleMode.Normal,\r\n  priceLineVisible: false,\r\n  locale: 'en',\r\n  gridColor: '#f2f2f2',\r\n  axesColor: '#333',\r\n  tzDisplay: Intl.DateTimeFormat().resolvedOptions().timeZone,\r\n  height: 500\r\n};\r\n\r\n\r\nexport class AdvanceChart implements IAdvanceChart {\r\n  options: IAdvanceChartOptions;\r\n  chartApi: IChartApi;\r\n  chartType: IAdvanceChartType | null = null;\r\n  mainSeries: ISeriesApi<SeriesType> | null = null;\r\n  dataInterval: Interval = {\r\n    period: Period.day,\r\n    times: 1\r\n  };\r\n  __destroyed = false\r\n  \r\n  private data: OHLCVExtraData[] = [];\r\n  // private dataSet: OHLCVExtraData[] = [];\r\n  private indicators = new Map<string, ChartIndicator>();\r\n  private _volumeType: 'volume' | 'volume_overlay' | undefined = undefined;\r\n  // private _displayTimezone = new DisplayTimezoneUtils()\r\n  \r\n\r\n  private _chartTypeChanged = new Delegate();\r\n  private _indicatorChanged = new Delegate<string, 'add' | 'remove'>();\r\n  private _destroyed = new Delegate();\r\n  private _updated = new Delegate();\r\n  private _crosshairMoved = new Delegate<OHLCVExtraData, OHLCVExtraData>();\r\n  private _chartHovered = new Delegate<boolean>();\r\n  private _dataSetChanged = new Delegate<OHLCVExtraData[]>();\r\n  private _loading = new Delegate<boolean>();\r\n  private _optionChanged = new Delegate();\r\n  private _mainSeriesChanged = new Delegate();\r\n\r\n  private _displayTimezone: DisplayTimezone\r\n\r\n  constructor(\r\n    container: HTMLElement,\r\n    options?: DeepPartial<IAdvanceChartOptions>\r\n  ) {\r\n    this.options = Object.freeze(merge(cloneDeep(defaultAdvanceChartOptions), options ?? {}));\r\n    this.chartApi = createChart(container, {\r\n      layout: {\r\n        attributionLogo: false,\r\n        panes: {\r\n          separatorColor: '#e0e3eb',\r\n          enableResize: false,\r\n        },\r\n        fontSize: this.options.fontSize,\r\n        fontFamily: this.options.fontFamily,\r\n        textColor: this.options.axesColor,\r\n      },\r\n      autoSize: true,\r\n      height: this.options.height,\r\n      localization: {\r\n        locale: this.options.locale,\r\n        percentageFormatter: (percentageValue: number) => this.numberFormatter.percent(percentageValue / 100),\r\n        // priceFormatter: (price) => this.numberFormatter.decimal(price),\r\n        timeFormatter: (time: Time) => this._displayTimezone.format(timeToDate(time))\r\n      },\r\n      timeScale: {\r\n        borderVisible: false,\r\n        rightOffset: 10,\r\n        maxBarSpacing: 40,\r\n        minBarSpacing: 4,\r\n        secondsVisible: true,\r\n        timeVisible: true,\r\n        tickMarkFormatter: ((timePoint, tickMarkType) => this._displayTimezone.tickMarkFormatter(timeToDate(timePoint), tickMarkType)) as TickMarkFormatter\r\n      },\r\n      overlayPriceScales: {\r\n        scaleMargins: {\r\n          bottom: 0.05,\r\n          top: 0.05\r\n        }\r\n      },\r\n      leftPriceScale: {\r\n        borderVisible: false,\r\n      },\r\n      handleScale: {\r\n        axisPressedMouseMove: false,\r\n      },\r\n      rightPriceScale: {\r\n        borderVisible: false,\r\n        mode: this.options.priceScaleMode,\r\n      },\r\n\r\n      grid: {\r\n        horzLines: {\r\n          visible: false,\r\n        },\r\n        vertLines: {\r\n          color: this.options.gridColor,\r\n        },\r\n      },\r\n    });\r\n\r\n    this.chartApi.subscribeCrosshairMove((param) => {\r\n      if (param.time === undefined) return;\r\n      const mainSeries = this.mainSeries;\r\n      if (!mainSeries) return;\r\n\r\n      const index = binarySearchIndex(\r\n        this.data,\r\n        timeToUnix(param.time),\r\n        (item) => timeToUnix(item.time)\r\n      );\r\n      if (index === -1) return;\r\n\r\n      const [data, prev] = this.getPointFromIndex(index);\r\n\r\n      this._crosshairMoved.fire(data, prev);\r\n    });\r\n\r\n    this.chartApi.subscribeCrosshairMove((param) => {\r\n      if (param.logical === undefined) return this._chartHovered.fire(false);\r\n      this._chartHovered.fire(true);\r\n    });\r\n\r\n    this.chartApi.timeScale().subscribeVisibleTimeRangeChange((param) => {\r\n      if (!param) return this._dataSetChanged.fire([]);\r\n      // this.dataSet = this.getDataSet(param);\r\n      this._dataSetChanged.fire(this.dataSet);\r\n    });\r\n\r\n    this._dataSetChanged.subscribe(() => {\r\n      this.tryDrawUpDownLine();\r\n      this.updateBaselineChartType()\r\n    });\r\n\r\n    this._displayTimezone = new DisplayTimezone(this)\r\n  }\r\n\r\n  get numberFormatter () {\r\n    return NumberFormatterFactory.formatter(this.options.locale ?? 'en')\r\n  }\r\n\r\n  get dataSet () {\r\n    const range = this.chartApi.timeScale().getVisibleRange();\r\n    if(!range) return [];\r\n    const { from, to } = range;\r\n    const fromIndex = binarySearchIndex(this.data, timeToUnix(from), (item) => timeToUnix(item.time));\r\n    const toIndex = binarySearchIndex(this.data, timeToUnix(to), (item) => timeToUnix(item.time));\r\n    return this.data.slice(fromIndex, toIndex + 1);\r\n  }\r\n\r\n  getData() {\r\n    return this.data;\r\n  }\r\n\r\n  getIndicators() {\r\n    return Array.from(this.indicators.values());\r\n  }\r\n\r\n  getPointFromIndex(index: number) {\r\n    const current = this.data[index];\r\n    const prev = this.data[index > 0 ? index - 1 : index];\r\n\r\n    return [current, prev] as const;\r\n  }\r\n\r\n  lastPoint() {\r\n    return this.getPointFromIndex(this.data.length - 1);\r\n  }\r\n\r\n  setChartType(type: IAdvanceChartType) {\r\n    if (type === this.chartType) return;\r\n    let mainSeries: ISeriesApi<SeriesType>;\r\n\r\n    switch (type) {\r\n      case 'line':\r\n        mainSeries = this.chartApi.addSeries(\r\n          LineSeries,\r\n          { \r\n            color: this.options.mainColor, \r\n            priceLineVisible: this.options.priceLineVisible,\r\n            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,\r\n            lineWidth: 2\r\n          },\r\n          0\r\n        );\r\n        break;\r\n      case 'candle':\r\n        mainSeries = this.chartApi.addSeries(\r\n          CandlestickSeries,\r\n          { upColor: this.options.upColor, downColor: this.options.downColor, priceLineVisible: this.options.priceLineVisible, },\r\n          0\r\n        );\r\n        break;\r\n      case 'mountain':\r\n        mainSeries = this.chartApi.addSeries(\r\n          AreaSeries,\r\n          {\r\n            topColor: this.options.mainColor,\r\n            lineColor: this.options.mainColor,\r\n            bottomColor: '#ffffff00',\r\n            priceLineVisible: this.options.priceLineVisible,\r\n            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,\r\n            lineWidth: 2\r\n          },\r\n          0\r\n        );\r\n        break;\r\n      case 'bar':\r\n        mainSeries = this.chartApi.addSeries(\r\n          BarSeries,\r\n          { upColor: this.options.upColor, downColor: this.options.downColor, priceLineVisible: this.options.priceLineVisible },\r\n          0\r\n        );\r\n        break;\r\n      case 'baseline':\r\n        mainSeries = this.chartApi.addSeries(\r\n          BaselineSeries,\r\n          {\r\n            topLineColor: this.options.upColor,\r\n            bottomLineColor: this.options.downColor,\r\n            bottomFillColor1: 'transparent',\r\n            bottomFillColor2: 'transparent',\r\n            topFillColor1: 'transparent',\r\n            topFillColor2: 'transparent',\r\n            priceLineVisible: this.options.priceLineVisible,\r\n            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,\r\n            lineWidth: 2\r\n          },\r\n          0\r\n        );\r\n        break;\r\n      case 'base-mountain':\r\n        mainSeries = this.chartApi.addSeries(\r\n          BaselineSeries,\r\n          {\r\n            topLineColor: this.options.upColor,\r\n            bottomLineColor: this.options.downColor,\r\n            priceLineVisible: this.options.priceLineVisible,\r\n            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,\r\n            lineWidth: 2\r\n          },\r\n          0\r\n        );\r\n        break;\r\n      default:\r\n        throw new Error('Invalid chart type');\r\n    }\r\n\r\n    if (this.mainSeries) {\r\n      this.chartApi.removeSeries(this.mainSeries);\r\n    }\r\n    this.chartType = type;\r\n    this.mainSeries = mainSeries;\r\n    this.updateBaselineChartType()\r\n    this.applyMainSeriesData();\r\n    this.tryDrawUpDownLine()\r\n    Array.from(this.indicators.values()).forEach(indicator => indicator.mainSeriesChanged?.(mainSeries))\r\n    this._updated.fire();\r\n    this._chartTypeChanged.fire();\r\n  }\r\n\r\n  updateBaselineChartType(){\r\n    if (this.mainSeries && (this.chartType === 'baseline' || this.chartType === 'base-mountain')) {\r\n      const baseValue = this.dataSet.at(0)?.close;\r\n      if (baseValue) {\r\n        (this.mainSeries as unknown as ISeriesApi<'Baseline'>).applyOptions({\r\n          baseValue: { price: baseValue },\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  setData(data: OHLCVSimple[], interval: Interval) {\r\n    this.dataInterval = interval\r\n    this.data = data.map((item) => ({ ...item, value: item.close, customValues: item }));\r\n    this.applyData();\r\n    const range = this.chartApi.timeScale().getVisibleRange();\r\n    if (range) {\r\n      this._dataSetChanged.fire(this.dataSet);\r\n    }\r\n    this._updated.fire();\r\n  }\r\n\r\n  applyMainSeriesData() {\r\n    if (!this.mainSeries) return;\r\n    this.mainSeries.setData(this.data as unknown as OHLCVSimple[]);\r\n  }\r\n  applyData() {\r\n    this.applyMainSeriesData();\r\n    Array.from(this.indicators.values()).map((instance) =>\r\n      instance.setData(this.data)\r\n    );\r\n  }\r\n\r\n  update(bar: OHLCVSimple, replaceLastPoint: boolean = false) {\r\n    const [lastPoint] = this.lastPoint()\r\n    const prepareBar = {\r\n      ...bar,\r\n      value: bar.close,\r\n      customValues: bar,\r\n      time: replaceLastPoint ? lastPoint.time : bar.time\r\n    }\r\n\r\n    if(replaceLastPoint) {\r\n      this.data.splice(this.data.length - 1, 1, prepareBar)\r\n    } else {\r\n      this.data.push(prepareBar)\r\n\r\n      const range = this.chartApi.timeScale().getVisibleLogicalRange();\r\n\r\n      // keep current range when add new tick.\r\n      if(range && (Math.floor(range.to) > this.data.length)) {\r\n        const newRange = {from: range.from, to: range.to}\r\n        this.chartApi.timeScale().setVisibleLogicalRange(newRange)\r\n      }\r\n    }\r\n\r\n    this.mainSeries?.update(prepareBar as unknown as OHLCVSimple)\r\n    Array.from(this.indicators.values()).map((instance) => instance.update());\r\n\r\n    this.tryDrawUpDownLine()\r\n    this._updated.fire();\r\n  }\r\n\r\n  tryDrawUpDownLine() {\r\n    if (!this.mainSeries) return;\r\n    if (!this.options.highLowLineVisible) {\r\n      this.mainSeries.priceLines().forEach(line => this.mainSeries?.removePriceLine(line))\r\n      return;\r\n    }\r\n    const dataSet = this.dataSet;\r\n    const low = dataSet.reduce(\r\n      (acc, item) => Math.min(acc, item.close),\r\n      Number.MAX_SAFE_INTEGER\r\n    );\r\n    const high = dataSet.reduce(\r\n      (acc, item) => Math.max(acc, item.close),\r\n      Number.MIN_SAFE_INTEGER\r\n    );\r\n    const [lowPriceLine, highPriceLine] = this.mainSeries?.priceLines() ?? [];\r\n\r\n    if (lowPriceLine) {\r\n      lowPriceLine.applyOptions({\r\n        price: low,\r\n        color: this.options.downColor,\r\n      });\r\n    } else {\r\n      this.mainSeries.createPriceLine({\r\n        price: low,\r\n        color: this.options.downColor,\r\n      });\r\n    }\r\n    if (highPriceLine) {\r\n      highPriceLine.applyOptions({\r\n        price: high,\r\n        color: this.options.upColor,\r\n      });\r\n    } else {\r\n      this.mainSeries.createPriceLine({\r\n        price: high,\r\n        color: this.options.upColor,\r\n      });\r\n    }\r\n  }\r\n\r\n  applyOptions(options: DeepPartial<IAdvanceChartOptions>) {\r\n    this.options = merge(cloneDeep(this.options), options);\r\n    if (options.highLowLineVisible !== undefined) this.tryDrawUpDownLine();\r\n    if (options.priceScaleMode !== undefined)\r\n      this.chartApi.applyOptions({\r\n        rightPriceScale: { mode: options.priceScaleMode },\r\n      });\r\n\r\n    if(options.priceLineVisible !== undefined) this.mainSeries?.applyOptions({priceLineVisible: options.priceLineVisible})\r\n\r\n    if(options.locale) this.chartApi.applyOptions({localization: { locale: options.locale }})\r\n  }\r\n  isShowVolume() {\r\n    return Boolean(this._volumeType);\r\n  }\r\n\r\n  showVolume(type: typeof this._volumeType = 'volume_overlay', options?: Partial<VolumeIndicatorOptions>) {\r\n    if (!this.mainSeries) return;\r\n    if (!type) return;\r\n    if (this.hasIndicator(type)) return;\r\n    const indicator = this.addIndicator(type) as VolumeIndicator;\r\n    if(options) indicator.applyOptions(options)\r\n    if (this._volumeType !== type) {\r\n      if (this._volumeType) this.removeIndicator(this._volumeType);\r\n      this._volumeType = type;\r\n    }\r\n    const volumePaneIndex = indicator.getPaneIndex();\r\n    if (volumePaneIndex === 0) return;\r\n    const indicatorNeedToMove = Array.from(this.indicators.values()).map(\r\n      (item) => {\r\n        if (item === indicator) return;\r\n        const paneIndex = item.getPaneIndex();\r\n        if (paneIndex === 0) return;\r\n        if (paneIndex >= volumePaneIndex) return;\r\n\r\n        return {\r\n          paneIndex,\r\n          indicator: item,\r\n        };\r\n      }\r\n    );\r\n    indicator.setPaneIndex(1);\r\n    \r\n    indicatorNeedToMove.map((item) => {\r\n      if (!item) return;\r\n      item.indicator.setPaneIndex(item.paneIndex + 1);\r\n    });\r\n  }\r\n\r\n  hiddenVolume() {\r\n    if (!this._volumeType) return;\r\n    this.removeIndicator(this._volumeType);\r\n  }\r\n\r\n  listIndicators() {\r\n    return Array.from(this.indicators.keys()).filter(item => item !== this._volumeType);\r\n  }\r\n\r\n  addIndicator(name: string) {\r\n    const instance = IndicatorFactory.createIndicator(\r\n      name,\r\n      this.chartApi,\r\n      {\r\n        numberFormatter: () => this.numberFormatter,\r\n        upColor: this.options.upColor,\r\n        downColor: this.options.downColor,\r\n      },\r\n      this.chartApi.panes().length\r\n    );\r\n\r\n    instance.setData(this.data);\r\n    this.indicators.set(name, instance);\r\n    this._updated.fire();\r\n    this._indicatorChanged.fire(name, 'add');\r\n    if(this.mainSeries) {\r\n      instance.mainSeriesChanged?.(this.mainSeries);\r\n    }\r\n    return instance;\r\n  }\r\n\r\n  removeIndicator(name: string) {\r\n    const instance = this.indicators.get(name);\r\n    if (!instance) return;\r\n    instance.remove();\r\n    this.indicators.delete(name);\r\n    this._updated.fire();\r\n    this._indicatorChanged.fire(name, 'remove');\r\n  }\r\n\r\n  hasIndicator(name: string) {\r\n    return this.indicators.has(name);\r\n  }\r\n\r\n  remove() {\r\n    Array.from(this.indicators.values()).map((instance) => instance.remove());\r\n    this.indicators.clear();\r\n    this.chartApi.remove();\r\n    this._destroyed.fire();\r\n\r\n    this._chartTypeChanged.destroy();\r\n    this._indicatorChanged.destroy();\r\n    this._destroyed.destroy();\r\n    this._updated.destroy();\r\n    this._crosshairMoved.destroy();\r\n    this._chartHovered.destroy();\r\n    this.__destroyed = true\r\n  }\r\n\r\n  groupIndicatorByPane(): Array<IGroupIndicatorByPane> {\r\n    const indicators = Array.from(this.indicators.values());\r\n    const panes = Array.from(this.chartApi.panes()).map((pane) => ({\r\n      pane,\r\n      indicators: [] as ChartIndicator[],\r\n    }));\r\n    for (const indicator of indicators) {\r\n      panes[indicator.getPaneIndex()].indicators.push(indicator);\r\n    }\r\n\r\n    return panes;\r\n  }\r\n\r\n  fitRange(range: {from: Logical, to: Logical}) {\r\n    const rightOffset = this.chartApi.options().timeScale.rightOffset\r\n    const space = range.to - range.from\r\n    const barVisiable = this.maxBar - rightOffset\r\n    this.chartApi.timeScale().setVisibleLogicalRange({\r\n      from: range.from, \r\n      to: (space < barVisiable ? range.from + barVisiable : range.to) + rightOffset\r\n    });\r\n  }\r\n\r\n  fitContent() {\r\n    const range = {from: 0 as Logical, to: Math.max(this.maxBar, this.data.length) as Logical};\r\n    this.fitRange(range);\r\n\r\n    return range\r\n  }\r\n\r\n  get maxBar () {\r\n    const maxBarSpacing = this.chartApi.options().timeScale.maxBarSpacing;\r\n    const width = this.chartApi.timeScale().width();\r\n\r\n    return Math.round(width / maxBarSpacing)\r\n  }\r\n\r\n  get loading () {\r\n    return Boolean(this._loading.lastParams()?.[0])\r\n  }\r\n\r\n  set loading(status: boolean) {\r\n    this._loading.fire(status)\r\n  }\r\n  \r\n  /** --------------- public delegate --------------- */\r\n\r\n  updated() {\r\n    return this._updated as IPublicDelegate<typeof this._updated>;\r\n  }\r\n\r\n  chartTypeChanged() {\r\n    return this._chartTypeChanged as IPublicDelegate<\r\n      typeof this._chartTypeChanged\r\n    >;\r\n  }\r\n\r\n  indicatorChanged() {\r\n    return this._indicatorChanged as IPublicDelegate<\r\n      typeof this._indicatorChanged\r\n    >;\r\n  }\r\n\r\n  crosshairMoved() {\r\n    return this._crosshairMoved satisfies IPublicDelegate<Delegate<OHLCVExtraData, OHLCVExtraData>>;\r\n  }\r\n\r\n  destroyed() {\r\n    return this._destroyed satisfies IPublicDelegate<Delegate>;\r\n  }\r\n\r\n  chartHovered() {\r\n    return this._chartHovered satisfies IPublicDelegate<Delegate<boolean>>;\r\n  }\r\n\r\n  onLoading() {\r\n    return this._loading satisfies IPublicDelegate<Delegate<boolean>>;\r\n  }\r\n\r\n  optionChanged(): ISubscription {\r\n    return this._optionChanged satisfies IPublicDelegate<Delegate>;\r\n  }\r\n\r\n  mainSeriesChanged(): ISubscription {\r\n    return this._mainSeriesChanged satisfies IPublicDelegate<Delegate>;\r\n  }\r\n}\r\n"], "names": ["defaultAdvanceChartOptions", "upColor", "downColor", "PriceScaleMode", "AdvanceChart", "container", "options", "__publicField", "Period", "Delegate", "merge", "cloneDeep", "createChart", "percentageValue", "time", "timeToDate", "timePoint", "tickMarkType", "param", "index", "binarySearchIndex", "timeToUnix", "item", "data", "prev", "DisplayTimezone", "NumberFormatterFactory", "range", "from", "to", "fromIndex", "toIndex", "current", "type", "mainSeries", "LineSeries", "LastPriceAnimationMode", "CandlestickSeries", "AreaSeries", "BarSeries", "BaselineSeries", "indicator", "_a", "baseValue", "interval", "instance", "bar", "replaceLastPoint", "lastPoint", "prepareBar", "newRange", "line", "dataSet", "low", "acc", "high", "lowPriceLine", "highPriceLine", "volumePaneIndex", "indicatorNeedToMove", "paneIndex", "name", "IndicatorFactory", "indicators", "panes", "pane", "rightOffset", "space", "barVisiable", "maxBarSpacing", "width", "status"], "mappings": ";;;;;;;;;;;;;AAiCO,MAAMA,IAAmD;AAAA,EAC9D,SAAAC;AAAA,EACA,WAAAC;AAAA,EACA,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,eAAeD;AAAA,EACf,cAAcC;AAAA,EACd,gBAAgBC,EAAe;AAAA,EAC/B,kBAAkB;AAAA,EAClB,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW,KAAK,iBAAiB,gBAAkB,EAAA;AAAA,EACnD,QAAQ;AACV;AAGO,MAAMC,EAAsC;AAAA,EA+BjD,YACEC,GACAC,GACA;AAjCF,IAAAC,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA,mBAAsC;AACtC,IAAAA,EAAA,oBAA4C;AAC5C,IAAAA,EAAA,sBAAyB;AAAA,MACvB,QAAQC,EAAO;AAAA,MACf,OAAO;AAAA,IACT;AACA,IAAAD,EAAA,qBAAc;AAEN,IAAAA,EAAA,cAAyB,CAAC;AAE1B;AAAA,IAAAA,EAAA,wCAAiB,IAA4B;AAC7C,IAAAA,EAAA;AAIA;AAAA,IAAAA,EAAA,2BAAoB,IAAIE,EAAS;AACjC,IAAAF,EAAA,2BAAoB,IAAIE,EAAmC;AAC3D,IAAAF,EAAA,oBAAa,IAAIE,EAAS;AAC1B,IAAAF,EAAA,kBAAW,IAAIE,EAAS;AACxB,IAAAF,EAAA,yBAAkB,IAAIE,EAAyC;AAC/D,IAAAF,EAAA,uBAAgB,IAAIE,EAAkB;AACtC,IAAAF,EAAA,yBAAkB,IAAIE,EAA2B;AACjD,IAAAF,EAAA,kBAAW,IAAIE,EAAkB;AACjC,IAAAF,EAAA,wBAAiB,IAAIE,EAAS;AAC9B,IAAAF,EAAA,4BAAqB,IAAIE,EAAS;AAElC,IAAAF,EAAA;AAMD,SAAA,UAAU,OAAO,OAAOG,EAAMC,EAAUX,CAA0B,GAAGM,KAAW,CAAA,CAAE,CAAC,GACnF,KAAA,WAAWM,EAAYP,GAAW;AAAA,MACrC,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,OAAO;AAAA,UACL,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB;AAAA,QACA,UAAU,KAAK,QAAQ;AAAA,QACvB,YAAY,KAAK,QAAQ;AAAA,QACzB,WAAW,KAAK,QAAQ;AAAA,MAC1B;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,KAAK,QAAQ;AAAA,MACrB,cAAc;AAAA,QACZ,QAAQ,KAAK,QAAQ;AAAA,QACrB,qBAAqB,CAACQ,MAA4B,KAAK,gBAAgB,QAAQA,IAAkB,GAAG;AAAA;AAAA,QAEpG,eAAe,CAACC,MAAe,KAAK,iBAAiB,OAAOC,EAAWD,CAAI,CAAC;AAAA,MAC9E;AAAA,MACA,WAAW;AAAA,QACT,eAAe;AAAA,QACf,aAAa;AAAA,QACb,eAAe;AAAA,QACf,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,mBAAoB,CAACE,GAAWC,MAAiB,KAAK,iBAAiB,kBAAkBF,EAAWC,CAAS,GAAGC,CAAY;AAAA,MAC9H;AAAA,MACA,oBAAoB;AAAA,QAClB,cAAc;AAAA,UACZ,QAAQ;AAAA,UACR,KAAK;AAAA,QAAA;AAAA,MAET;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,MACjB;AAAA,MACA,aAAa;AAAA,QACX,sBAAsB;AAAA,MACxB;AAAA,MACA,iBAAiB;AAAA,QACf,eAAe;AAAA,QACf,MAAM,KAAK,QAAQ;AAAA,MACrB;AAAA,MAEA,MAAM;AAAA,QACJ,WAAW;AAAA,UACT,SAAS;AAAA,QACX;AAAA,QACA,WAAW;AAAA,UACT,OAAO,KAAK,QAAQ;AAAA,QAAA;AAAA,MACtB;AAAA,IACF,CACD,GAEI,KAAA,SAAS,uBAAuB,CAACC,MAAU;AAG9C,UAFIA,EAAM,SAAS,UAEf,CADe,KAAK,WACP;AAEjB,YAAMC,IAAQC;AAAA,QACZ,KAAK;AAAA,QACLC,EAAWH,EAAM,IAAI;AAAA,QACrB,CAACI,MAASD,EAAWC,EAAK,IAAI;AAAA,MAChC;AACA,UAAIH,MAAU,GAAI;AAElB,YAAM,CAACI,GAAMC,CAAI,IAAI,KAAK,kBAAkBL,CAAK;AAE5C,WAAA,gBAAgB,KAAKI,GAAMC,CAAI;AAAA,IAAA,CACrC,GAEI,KAAA,SAAS,uBAAuB,CAACN,MAAU;AAC9C,UAAIA,EAAM,YAAY,eAAkB,KAAK,cAAc,KAAK,EAAK;AAChE,WAAA,cAAc,KAAK,EAAI;AAAA,IAAA,CAC7B,GAED,KAAK,SAAS,UAAY,EAAA,gCAAgC,CAACA,MAAU;AACnE,UAAI,CAACA,EAAO,QAAO,KAAK,gBAAgB,KAAK,CAAA,CAAE;AAE1C,WAAA,gBAAgB,KAAK,KAAK,OAAO;AAAA,IAAA,CACvC,GAEI,KAAA,gBAAgB,UAAU,MAAM;AACnC,WAAK,kBAAkB,GACvB,KAAK,wBAAwB;AAAA,IAAA,CAC9B,GAEI,KAAA,mBAAmB,IAAIO,EAAgB,IAAI;AAAA,EAAA;AAAA,EAGlD,IAAI,kBAAmB;AACrB,WAAOC,EAAuB,UAAU,KAAK,QAAQ,UAAU,IAAI;AAAA,EAAA;AAAA,EAGrE,IAAI,UAAW;AACb,UAAMC,IAAQ,KAAK,SAAS,UAAA,EAAY,gBAAgB;AACrD,QAAA,CAACA,EAAO,QAAO,CAAC;AACb,UAAA,EAAE,MAAAC,GAAM,IAAAC,EAAA,IAAOF,GACfG,IAAYV,EAAkB,KAAK,MAAMC,EAAWO,CAAI,GAAG,CAACN,MAASD,EAAWC,EAAK,IAAI,CAAC,GAC1FS,IAAUX,EAAkB,KAAK,MAAMC,EAAWQ,CAAE,GAAG,CAACP,MAASD,EAAWC,EAAK,IAAI,CAAC;AAC5F,WAAO,KAAK,KAAK,MAAMQ,GAAWC,IAAU,CAAC;AAAA,EAAA;AAAA,EAG/C,UAAU;AACR,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,gBAAgB;AACd,WAAO,MAAM,KAAK,KAAK,WAAW,QAAQ;AAAA,EAAA;AAAA,EAG5C,kBAAkBZ,GAAe;AACzB,UAAAa,IAAU,KAAK,KAAKb,CAAK,GACzBK,IAAO,KAAK,KAAKL,IAAQ,IAAIA,IAAQ,IAAIA,CAAK;AAE7C,WAAA,CAACa,GAASR,CAAI;AAAA,EAAA;AAAA,EAGvB,YAAY;AACV,WAAO,KAAK,kBAAkB,KAAK,KAAK,SAAS,CAAC;AAAA,EAAA;AAAA,EAGpD,aAAaS,GAAyB;AAChC,QAAAA,MAAS,KAAK,UAAW;AACzB,QAAAC;AAEJ,YAAQD,GAAM;AAAA,MACZ,KAAK;AACH,QAAAC,IAAa,KAAK,SAAS;AAAA,UACzBC;AAAA,UACA;AAAA,YACE,OAAO,KAAK,QAAQ;AAAA,YACpB,kBAAkB,KAAK,QAAQ;AAAA,YAC/B,oBAAoBC,EAAuB;AAAA,YAC3C,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,QAAAF,IAAa,KAAK,SAAS;AAAA,UACzBG;AAAA,UACA,EAAE,SAAS,KAAK,QAAQ,SAAS,WAAW,KAAK,QAAQ,WAAW,kBAAkB,KAAK,QAAQ,iBAAkB;AAAA,UACrH;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,QAAAH,IAAa,KAAK,SAAS;AAAA,UACzBI;AAAA,UACA;AAAA,YACE,UAAU,KAAK,QAAQ;AAAA,YACvB,WAAW,KAAK,QAAQ;AAAA,YACxB,aAAa;AAAA,YACb,kBAAkB,KAAK,QAAQ;AAAA,YAC/B,oBAAoBF,EAAuB;AAAA,YAC3C,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,QAAAF,IAAa,KAAK,SAAS;AAAA,UACzBK;AAAA,UACA,EAAE,SAAS,KAAK,QAAQ,SAAS,WAAW,KAAK,QAAQ,WAAW,kBAAkB,KAAK,QAAQ,iBAAiB;AAAA,UACpH;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,QAAAL,IAAa,KAAK,SAAS;AAAA,UACzBM;AAAA,UACA;AAAA,YACE,cAAc,KAAK,QAAQ;AAAA,YAC3B,iBAAiB,KAAK,QAAQ;AAAA,YAC9B,kBAAkB;AAAA,YAClB,kBAAkB;AAAA,YAClB,eAAe;AAAA,YACf,eAAe;AAAA,YACf,kBAAkB,KAAK,QAAQ;AAAA,YAC/B,oBAAoBJ,EAAuB;AAAA,YAC3C,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,QAAAF,IAAa,KAAK,SAAS;AAAA,UACzBM;AAAA,UACA;AAAA,YACE,cAAc,KAAK,QAAQ;AAAA,YAC3B,iBAAiB,KAAK,QAAQ;AAAA,YAC9B,kBAAkB,KAAK,QAAQ;AAAA,YAC/B,oBAAoBJ,EAAuB;AAAA,YAC3C,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AACA;AAAA,MACF;AACQ,cAAA,IAAI,MAAM,oBAAoB;AAAA,IAAA;AAGxC,IAAI,KAAK,cACF,KAAA,SAAS,aAAa,KAAK,UAAU,GAE5C,KAAK,YAAYH,GACjB,KAAK,aAAaC,GAClB,KAAK,wBAAwB,GAC7B,KAAK,oBAAoB,GACzB,KAAK,kBAAkB,GACjB,MAAA,KAAK,KAAK,WAAW,OAAQ,CAAA,EAAE,QAAQ,CAAaO,MAAA;;AAAA,cAAAC,IAAAD,EAAU,sBAAV,gBAAAC,EAAA,KAAAD,GAA8BP;AAAA,KAAW,GACnG,KAAK,SAAS,KAAK,GACnB,KAAK,kBAAkB,KAAK;AAAA,EAAA;AAAA,EAG9B,0BAAyB;;AACvB,QAAI,KAAK,eAAe,KAAK,cAAc,cAAc,KAAK,cAAc,kBAAkB;AAC5F,YAAMS,KAAYD,IAAA,KAAK,QAAQ,GAAG,CAAC,MAAjB,gBAAAA,EAAoB;AACtC,MAAIC,KACD,KAAK,WAAiD,aAAa;AAAA,QAClE,WAAW,EAAE,OAAOA,EAAU;AAAA,MAAA,CAC/B;AAAA,IACH;AAAA,EACF;AAAA,EAGF,QAAQpB,GAAqBqB,GAAoB;AAC/C,SAAK,eAAeA,GACpB,KAAK,OAAOrB,EAAK,IAAI,CAACD,OAAU,EAAE,GAAGA,GAAM,OAAOA,EAAK,OAAO,cAAcA,EAAO,EAAA,GACnF,KAAK,UAAU,GACD,KAAK,SAAS,UAAA,EAAY,gBAAgB,KAEjD,KAAA,gBAAgB,KAAK,KAAK,OAAO,GAExC,KAAK,SAAS,KAAK;AAAA,EAAA;AAAA,EAGrB,sBAAsB;AAChB,IAAC,KAAK,cACL,KAAA,WAAW,QAAQ,KAAK,IAAgC;AAAA,EAAA;AAAA,EAE/D,YAAY;AACV,SAAK,oBAAoB,GACzB,MAAM,KAAK,KAAK,WAAW,OAAA,CAAQ,EAAE;AAAA,MAAI,CAACuB,MACxCA,EAAS,QAAQ,KAAK,IAAI;AAAA,IAC5B;AAAA,EAAA;AAAA,EAGF,OAAOC,GAAkBC,IAA4B,IAAO;;AAC1D,UAAM,CAACC,CAAS,IAAI,KAAK,UAAU,GAC7BC,IAAa;AAAA,MACjB,GAAGH;AAAA,MACH,OAAOA,EAAI;AAAA,MACX,cAAcA;AAAA,MACd,MAAMC,IAAmBC,EAAU,OAAOF,EAAI;AAAA,IAChD;AAEA,QAAGC;AACD,WAAK,KAAK,OAAO,KAAK,KAAK,SAAS,GAAG,GAAGE,CAAU;AAAA,SAC/C;AACA,WAAA,KAAK,KAAKA,CAAU;AAEzB,YAAMtB,IAAQ,KAAK,SAAS,UAAA,EAAY,uBAAuB;AAG5D,UAAAA,KAAU,KAAK,MAAMA,EAAM,EAAE,IAAI,KAAK,KAAK,QAAS;AACrD,cAAMuB,IAAW,EAAC,MAAMvB,EAAM,MAAM,IAAIA,EAAM,GAAE;AAChD,aAAK,SAAS,YAAY,uBAAuBuB,CAAQ;AAAA,MAAA;AAAA,IAC3D;AAGG,KAAAR,IAAA,KAAA,eAAA,QAAAA,EAAY,OAAOO,IAClB,MAAA,KAAK,KAAK,WAAW,OAAO,CAAC,EAAE,IAAI,CAACJ,MAAaA,EAAS,OAAA,CAAQ,GAExE,KAAK,kBAAkB,GACvB,KAAK,SAAS,KAAK;AAAA,EAAA;AAAA,EAGrB,oBAAoB;;AACd,QAAA,CAAC,KAAK,WAAY;AAClB,QAAA,CAAC,KAAK,QAAQ,oBAAoB;AAC/B,WAAA,WAAW,WAAa,EAAA,QAAQ,OAAQ;;AAAA,gBAAAH,IAAA,KAAK,eAAL,gBAAAA,EAAiB,gBAAgBS;AAAA,OAAK;AACnF;AAAA,IAAA;AAEF,UAAMC,IAAU,KAAK,SACfC,IAAMD,EAAQ;AAAA,MAClB,CAACE,GAAKhC,MAAS,KAAK,IAAIgC,GAAKhC,EAAK,KAAK;AAAA,MACvC,OAAO;AAAA,IACT,GACMiC,IAAOH,EAAQ;AAAA,MACnB,CAACE,GAAKhC,MAAS,KAAK,IAAIgC,GAAKhC,EAAK,KAAK;AAAA,MACvC,OAAO;AAAA,IACT,GACM,CAACkC,GAAcC,CAAa,MAAIf,IAAA,KAAK,eAAL,gBAAAA,EAAiB,iBAAgB,CAAC;AAExE,IAAIc,IACFA,EAAa,aAAa;AAAA,MACxB,OAAOH;AAAA,MACP,OAAO,KAAK,QAAQ;AAAA,IAAA,CACrB,IAED,KAAK,WAAW,gBAAgB;AAAA,MAC9B,OAAOA;AAAA,MACP,OAAO,KAAK,QAAQ;AAAA,IAAA,CACrB,GAECI,IACFA,EAAc,aAAa;AAAA,MACzB,OAAOF;AAAA,MACP,OAAO,KAAK,QAAQ;AAAA,IAAA,CACrB,IAED,KAAK,WAAW,gBAAgB;AAAA,MAC9B,OAAOA;AAAA,MACP,OAAO,KAAK,QAAQ;AAAA,IAAA,CACrB;AAAA,EACH;AAAA,EAGF,aAAajD,GAA4C;;AACvD,SAAK,UAAUI,EAAMC,EAAU,KAAK,OAAO,GAAGL,CAAO,GACjDA,EAAQ,uBAAuB,UAAW,KAAK,kBAAkB,GACjEA,EAAQ,mBAAmB,UAC7B,KAAK,SAAS,aAAa;AAAA,MACzB,iBAAiB,EAAE,MAAMA,EAAQ,eAAe;AAAA,IAAA,CACjD,GAEAA,EAAQ,qBAAqB,YAAgBoC,IAAA,KAAA,eAAA,QAAAA,EAAY,aAAa,EAAC,kBAAkBpC,EAAQ,sBAEjGA,EAAQ,UAAa,KAAA,SAAS,aAAa,EAAC,cAAc,EAAE,QAAQA,EAAQ,OAAO,EAAA,CAAE;AAAA,EAAA;AAAA,EAE1F,eAAe;AACN,WAAA,EAAQ,KAAK;AAAA,EAAW;AAAA,EAGjC,WAAW2B,IAAgC,kBAAkB3B,GAA2C;AAGlG,QAFA,CAAC,KAAK,cACN,CAAC2B,KACD,KAAK,aAAaA,CAAI,EAAG;AACvB,UAAAQ,IAAY,KAAK,aAAaR,CAAI;AACrC,IAAA3B,KAAmBmC,EAAA,aAAanC,CAAO,GACtC,KAAK,gBAAgB2B,MACnB,KAAK,eAAkB,KAAA,gBAAgB,KAAK,WAAW,GAC3D,KAAK,cAAcA;AAEf,UAAAyB,IAAkBjB,EAAU,aAAa;AAC/C,QAAIiB,MAAoB,EAAG;AAC3B,UAAMC,IAAsB,MAAM,KAAK,KAAK,WAAW,OAAQ,CAAA,EAAE;AAAA,MAC/D,CAACrC,MAAS;AACR,YAAIA,MAASmB,EAAW;AAClB,cAAAmB,IAAYtC,EAAK,aAAa;AACpC,YAAIsC,MAAc,KACd,EAAAA,KAAaF;AAEV,iBAAA;AAAA,YACL,WAAAE;AAAA,YACA,WAAWtC;AAAA,UACb;AAAA,MAAA;AAAA,IAEJ;AACA,IAAAmB,EAAU,aAAa,CAAC,GAEJkB,EAAA,IAAI,CAACrC,MAAS;AAChC,MAAKA,KACLA,EAAK,UAAU,aAAaA,EAAK,YAAY,CAAC;AAAA,IAAA,CAC/C;AAAA,EAAA;AAAA,EAGH,eAAe;AACT,IAAC,KAAK,eACL,KAAA,gBAAgB,KAAK,WAAW;AAAA,EAAA;AAAA,EAGvC,iBAAiB;AACR,WAAA,MAAM,KAAK,KAAK,WAAW,KAAA,CAAM,EAAE,OAAO,CAAAA,MAAQA,MAAS,KAAK,WAAW;AAAA,EAAA;AAAA,EAGpF,aAAauC,GAAc;;AACzB,UAAMhB,IAAWiB,EAAiB;AAAA,MAChCD;AAAA,MACA,KAAK;AAAA,MACL;AAAA,QACE,iBAAiB,MAAM,KAAK;AAAA,QAC5B,SAAS,KAAK,QAAQ;AAAA,QACtB,WAAW,KAAK,QAAQ;AAAA,MAC1B;AAAA,MACA,KAAK,SAAS,QAAQ;AAAA,IACxB;AAES,WAAAhB,EAAA,QAAQ,KAAK,IAAI,GACrB,KAAA,WAAW,IAAIgB,GAAMhB,CAAQ,GAClC,KAAK,SAAS,KAAK,GACd,KAAA,kBAAkB,KAAKgB,GAAM,KAAK,GACpC,KAAK,gBACGnB,IAAAG,EAAA,sBAAA,QAAAH,EAAA,KAAAG,GAAoB,KAAK,cAE7BA;AAAA,EAAA;AAAA,EAGT,gBAAgBgB,GAAc;AAC5B,UAAMhB,IAAW,KAAK,WAAW,IAAIgB,CAAI;AACzC,IAAKhB,MACLA,EAAS,OAAO,GACX,KAAA,WAAW,OAAOgB,CAAI,GAC3B,KAAK,SAAS,KAAK,GACd,KAAA,kBAAkB,KAAKA,GAAM,QAAQ;AAAA,EAAA;AAAA,EAG5C,aAAaA,GAAc;AAClB,WAAA,KAAK,WAAW,IAAIA,CAAI;AAAA,EAAA;AAAA,EAGjC,SAAS;AACD,UAAA,KAAK,KAAK,WAAW,OAAO,CAAC,EAAE,IAAI,CAAChB,MAAaA,EAAS,OAAA,CAAQ,GACxE,KAAK,WAAW,MAAM,GACtB,KAAK,SAAS,OAAO,GACrB,KAAK,WAAW,KAAK,GAErB,KAAK,kBAAkB,QAAQ,GAC/B,KAAK,kBAAkB,QAAQ,GAC/B,KAAK,WAAW,QAAQ,GACxB,KAAK,SAAS,QAAQ,GACtB,KAAK,gBAAgB,QAAQ,GAC7B,KAAK,cAAc,QAAQ,GAC3B,KAAK,cAAc;AAAA,EAAA;AAAA,EAGrB,uBAAqD;AACnD,UAAMkB,IAAa,MAAM,KAAK,KAAK,WAAW,QAAQ,GAChDC,IAAQ,MAAM,KAAK,KAAK,SAAS,OAAO,EAAE,IAAI,CAACC,OAAU;AAAA,MAC7D,MAAAA;AAAA,MACA,YAAY,CAAA;AAAA,IAAC,EACb;AACF,eAAWxB,KAAasB;AACtB,MAAAC,EAAMvB,EAAU,aAAa,CAAC,EAAE,WAAW,KAAKA,CAAS;AAGpD,WAAAuB;AAAA,EAAA;AAAA,EAGT,SAASrC,GAAqC;AAC5C,UAAMuC,IAAc,KAAK,SAAS,UAAU,UAAU,aAChDC,IAAQxC,EAAM,KAAKA,EAAM,MACzByC,IAAc,KAAK,SAASF;AAC7B,SAAA,SAAS,UAAU,EAAE,uBAAuB;AAAA,MAC/C,MAAMvC,EAAM;AAAA,MACZ,KAAKwC,IAAQC,IAAczC,EAAM,OAAOyC,IAAczC,EAAM,MAAMuC;AAAA,IAAA,CACnE;AAAA,EAAA;AAAA,EAGH,aAAa;AACX,UAAMvC,IAAQ,EAAC,MAAM,GAAc,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,MAAM,EAAY;AACzF,gBAAK,SAASA,CAAK,GAEZA;AAAA,EAAA;AAAA,EAGT,IAAI,SAAU;AACZ,UAAM0C,IAAgB,KAAK,SAAS,UAAU,UAAU,eAClDC,IAAQ,KAAK,SAAS,UAAA,EAAY,MAAM;AAEvC,WAAA,KAAK,MAAMA,IAAQD,CAAa;AAAA,EAAA;AAAA,EAGzC,IAAI,UAAW;;AACb,WAAO,IAAQ3B,IAAA,KAAK,SAAS,WAAW,MAAzB,QAAAA,EAA6B;AAAA,EAAE;AAAA,EAGhD,IAAI,QAAQ6B,GAAiB;AACtB,SAAA,SAAS,KAAKA,CAAM;AAAA,EAAA;AAAA;AAAA,EAK3B,UAAU;AACR,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,mBAAmB;AACjB,WAAO,KAAK;AAAA,EAAA;AAAA,EAKd,mBAAmB;AACjB,WAAO,KAAK;AAAA,EAAA;AAAA,EAKd,iBAAiB;AACf,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,YAAY;AACV,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,eAAe;AACb,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,YAAY;AACV,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,gBAA+B;AAC7B,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,oBAAmC;AACjC,WAAO,KAAK;AAAA,EAAA;AAEhB;"}