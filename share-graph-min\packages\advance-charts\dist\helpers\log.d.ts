export interface ILogger {
    debug(name: string, ...args: unknown[]): void;
    info(name: string, ...args: unknown[]): void;
    warn(name: string, ...args: unknown[]): void;
    error(name: string, ...args: unknown[]): void;
}
export declare enum Log {
    NONE = 0,
    ERROR = 1,
    WARN = 2,
    INFO = 3,
    DEBUG = 4
}
export declare const LogManager: {
    reset: () => void;
    setLevel: (value: Log) => void;
    setLogger: (value: ILogger) => void;
};
export declare class Logger {
    private name;
    private method?;
    constructor(name: string, method?: string | undefined);
    debug(...args: unknown[]): void;
    info(...args: unknown[]): void;
    warn(...args: unknown[]): void;
    error(...args: unknown[]): void;
    private format;
}
export declare const log: Logger;
