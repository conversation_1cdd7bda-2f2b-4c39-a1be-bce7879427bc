"use strict";var f=Object.defineProperty;var m=(i,t,e)=>t in i?f(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var h=(i,t,e)=>m(i,typeof t!="symbol"?t+"":t,e);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const n=require("../helpers/utils.cjs.js"),D=require("../helpers/mergeData.cjs.js"),p=require("../helpers/delegate.cjs.js"),s=require("./i-advance-chart.cjs.js"),v=require("es-toolkit");require("../helpers/dayjs-setup.cjs.js");const u=require("./time-key.cjs.js"),w=require("../helpers/log.cjs.js"),l=require("dayjs");function y(i,t){switch(t){case s.Period.minute:case s.Period.hour:case s.Period.day:return i;case s.Period.week:return l.tz(n.timeToDayjs(i),"UTC").startOf("week").unix();case s.Period.month:return l.tz(n.timeToDayjs(i),"UTC").startOf("month").unix();default:throw new Error(`Period : ${t} not support`)}}const g=i=>{const t=Math.max(...i.map(o=>o.high)),e=Math.min(...i.map(o=>o.low)),a=i.reduce((o,c)=>o+c.volume,0),r=i[0].open,d=i[i.length-1].close;return{time:i[0].time,open:r,high:t,low:e,close:d,volume:a}};class C{constructor(t,e){h(this,"_loading",!1);h(this,"_data",[]);h(this,"_dataChanged",new p.Delegate);h(this,"interval",{period:s.Period.day,times:1});h(this,"initialData",!1);h(this,"endOfData",!1);h(this,"_refeshTimer",null);h(this,"_destroyed",!1);this.advanceChart=t,this.dataFetch=e,this.onVisibleLogicalRangeChange=this.onVisibleLogicalRangeChange.bind(this),this.advanceChart.chartApi.timeScale().subscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange),this.dataFetch.refeshTime&&(this._refeshTimer=setInterval(async()=>this.updateData(),this.dataFetch.refeshTime))}get data(){return this._data}set data(t){this._data=t,this._dataChanged.fire()}isNeedPaging(t){if(!this.initialData||this.data.length===0||this.endOfData)return!1;const{from:e}=t;return!(e>30)}groupData(){const t=this.interval;if(t.period===s.Period.day)return this._data;if(t.period===s.Period.hour)return this._data;if(t.period===s.Period.minute)return this._data;const e=v.groupBy(this._data,a=>u.timeKey(a.time,t));return Object.entries(e).map(([a,r])=>[parseInt(a),g(r)]).sort((a,r)=>a[0]-r[0]).map(a=>a[1])}processNewData(t){const e=D.mergeOhlcData(t,this.data);if(e.length!==this.data.length)return this.data=e,this.advanceChart.setData(this.groupData(),this.interval),e}async updateData(){const e=this.data[this.data.length-1].time,a=await this.dataFetch.fetchUpdateData({interval:this.interval,from:n.timeToDate(e),to:new Date},{forward:this.forward.bind(this)});this._destroyed||this.processNewData(a)}async pagingData(t){const{from:e}=t,a=this.data[0].time,r=n.timeToDayjs(a),d=this.forward(a,e-200),o=await this.dataFetch.fetchPaginationData({interval:this.interval,from:d.toDate(),to:r.toDate()},{forward:this.forward.bind(this)});this._destroyed||this.processNewData(o)||(this.endOfData=!0)}async onVisibleLogicalRangeChange(t){if(!t)return;const e=(a,r)=>a.from===r.from&&a.to===r.to;if(!this.advanceChart.loading){this.advanceChart.loading=!0;try{for(;this.isNeedPaging(t);){await this.pagingData(t);const a=this.advanceChart.chartApi.timeScale().getVisibleLogicalRange();if(!a||e(a,t))break;t=a}}finally{this.advanceChart.loading=!1}}}forward(t,e){e=Math.round(e);const a=this.interval.period;switch(a){case s.Period.minute:return n.timeToDayjs(t).add(e,"minute");case s.Period.hour:return n.timeToDayjs(t).add(e,"hour");case s.Period.day:return n.timeToDayjs(t).add(e,"day");case s.Period.week:return n.timeToDayjs(t).add(e,"week");case s.Period.month:return n.timeToDayjs(t).add(e,"month");default:throw new Error(`Period : ${a} not support`)}}async setRange({from:t,to:e,interval:a}){this.resetState(),this.advanceChart.loading=!0,this.interval=a;const r=await this.dataFetch.fetchInitialData({from:t,to:e,interval:a},{forward:this.forward.bind(this)});if(this.data=r,this.advanceChart.loading=!1,this._destroyed)return;this.advanceChart.setData(this.groupData(),a),this.initialData=!0;const d=this.advanceChart.chartApi.timeScale(),o=d.timeToIndex(n.dayjsToTime(l(t)),!0),c=d.timeToIndex(n.dayjsToTime(l(e)),!0);o!==void 0&&c!==void 0&&this.advanceChart.fitRange({from:o,to:c}),await this.onVisibleLogicalRangeChange(d.getVisibleLogicalRange())}resetState(){this.advanceChart.loading=!1,this.initialData=!1,this.endOfData=!1}dataChanged(){return this._dataChanged}trade(t){const[e]=this.advanceChart.lastPoint(),a=u.timeKey(e.time,this.interval),r=u.timeKey(t.time,this.interval);if(r<a){w.log.warn(`Trade timestamp ${r} is older than current ${a}`);return}a===r?this.advanceChart.update({open:e.open,high:Math.max(e.high,t.price),low:Math.min(e.low,t.price),close:t.price,volume:e.volume+t.volume,time:t.time},!0):this.advanceChart.update({open:t.price,high:t.price,low:t.price,close:t.price,volume:t.volume,time:t.time})}destroy(){this._dataChanged.destroy(),this.advanceChart.chartApi.timeScale().unsubscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange),this._refeshTimer&&(clearInterval(this._refeshTimer),this._refeshTimer=null)}}exports.DataFeed=C;exports.aggregate=g;exports.roundTime=y;
//# sourceMappingURL=data-feed.cjs.js.map
