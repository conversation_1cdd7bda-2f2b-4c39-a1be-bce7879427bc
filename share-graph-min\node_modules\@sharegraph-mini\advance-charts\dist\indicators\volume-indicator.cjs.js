"use strict";var p=Object.defineProperty;var c=(r,o,e)=>o in r?p(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e;var a=(r,o,e)=>c(r,typeof o!="symbol"?o+"":o,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const m=require("lightweight-charts"),v=require("./abstract-indicator.cjs.js"),h=require("../helpers/color.cjs.js"),l=require("../helpers/assertions.cjs.js"),n={upColor:"#26a69a",downColor:"#ef5350",overlay:!1};class d extends v.ChartIndicator{constructor(e,t,s){super(e,t);a(this,"volumeSeries");this.chart=e;const i=this.options.numberFormatter;this.volumeSeries=this.chart.addSeries(m.HistogramSeries,{priceLineVisible:!1,priceFormat:i?{type:"custom",formatter:u=>i().volume(u)}:{type:"volume"},priceScaleId:"volume"},this.options.overlay?0:s),this.applyPriceScaleMargins()}applyPriceScaleMargins(){this.options.overlay?this.volumeSeries.priceScale().applyOptions({scaleMargins:{top:.7,bottom:0}}):this.volumeSeries.priceScale().applyOptions({scaleMargins:{top:.1,bottom:0}})}_applyOptions(e){(e.downColor||e.upColor)&&this.applyIndicatorData()}applyIndicatorData(){const e=this._executionContext.data.filter(t=>t.value);this.volumeSeries.setData(e.map(t=>({time:t.time,value:l.ensureDefined(t.value)[0],color:h.Color.applyAlpha(l.ensureDefined(t.value)[1]===1?this.options.upColor:this.options.downColor,this.options.overlay?.6:1)})))}formula(e){const t=e.new_var(e.symbol.close,2);if(!t.calculable())return;const s=t.get(0)>t.get(1)?1:0;return[e.symbol.volume,s]}getDefaultOptions(){return n}remove(){super.remove(),this.chart.removeSeries(this.volumeSeries)}setPaneIndex(e){this.volumeSeries.moveToPane(e),this.applyPriceScaleMargins()}getPaneIndex(){return this.volumeSeries.getPane().paneIndex()}}exports.default=d;exports.defaultOptions=n;
//# sourceMappingURL=volume-indicator.cjs.js.map
