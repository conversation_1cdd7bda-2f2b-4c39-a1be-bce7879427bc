{"version": 3, "file": "williams-indicator.cjs.js", "sources": ["../../src/indicators/williams-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\r\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface WilliamsIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  period: number\r\n  priceLineColor: string,\r\n  backgroundColor: string\r\n}\r\n\r\nexport const defaultOptions: WilliamsIndicatorOptions = {\r\n  color: \"rgba(108, 80, 175, 1)\",\r\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\r\n  backgroundColor: '#7e57c21a',\r\n  period: 14,\r\n  overlay: false\r\n}\r\n\r\nexport type WilliamsLine = Nominal<number, 'Williams'>\r\n\r\nexport type WilliamsData = [WilliamsLine]\r\n\r\nexport default class WilliamsIndicator extends ChartIndicator<WilliamsIndicatorOptions, WilliamsData> {\r\n  williamsSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<WilliamsIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options)\r\n    this.williamsSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'williams',\r\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 0, minValue: -100})\r\n    }, paneIndex);\r\n\r\n    this.williamsSeries.attachPrimitive(\r\n      new RegionPrimitive({\r\n        upPrice: -20,\r\n        lowPrice: -80,\r\n        lineColor: this.options.priceLineColor,\r\n        backgroundColor: this.options.backgroundColor\r\n      })\r\n    );\r\n  }\r\n\r\n  getDefaultOptions(): WilliamsIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n\r\n  formula(c: Context): WilliamsData | undefined {\r\n    const highSeries = c.new_var(c.symbol.high, this.options.period);\r\n    const lowSeries = c.new_var(c.symbol.low, this.options.period);\r\n    const closeSeries = c.new_var(c.symbol.close, this.options.period);\r\n\r\n    if(!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) return;\r\n\r\n    // Get the arrays of values for the period\r\n    const highs = highSeries.getAll();\r\n    const lows = lowSeries.getAll();\r\n    const closes = closeSeries.getAll();\r\n\r\n    // Calculate Williams %R manually\r\n    // Formula: Williams %R = ((Highest High - Close) / (Highest High - Lowest Low)) × -100\r\n\r\n    // Validate we have enough data\r\n    if (highs.length < this.options.period || lows.length < this.options.period || closes.length < this.options.period) {\r\n      return;\r\n    }\r\n\r\n    // Find the highest high and lowest low over the period\r\n    const highestHigh = Math.max(...highs);\r\n    const lowestLow = Math.min(...lows);\r\n    const currentClose = closes[closes.length - 1]; // Most recent close\r\n\r\n    // Avoid division by zero\r\n    const range = highestHigh - lowestLow;\r\n    if (range === 0) {\r\n      return [0 as WilliamsLine]; // If no price movement, return 0\r\n    }\r\n\r\n    // Calculate Williams %R\r\n    const williamsR = ((highestHigh - currentClose) / range) * -100;\r\n\r\n    // Return the result, ensuring it's within the expected range (-100 to 0)\r\n    const clampedWilliamsR = Math.max(-100, Math.min(0, williamsR));\r\n\r\n    // Debug logging (can be removed in production)\r\n    console.log(`Williams %R Calculation:`, {\r\n      period: this.options.period,\r\n      highestHigh,\r\n      lowestLow,\r\n      currentClose,\r\n      range,\r\n      williamsR,\r\n      clampedWilliamsR\r\n    });\r\n\r\n    return [clampedWilliamsR as WilliamsLine];\r\n  }\r\n\r\n\r\n  applyIndicatorData() {\r\n    const williamsData: SingleValueData[] = [];\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      if(!value) continue;\r\n      williamsData.push({time: bar.time as Time, value: value[0]})\r\n    }\r\n\r\n    this.williamsSeries.setData(williamsData)\r\n  }\r\n\r\n  remove() {\r\n    super.remove()\r\n    this.chart.removeSeries(this.williamsSeries);\r\n  }\r\n\r\n  _applyOptions() {\r\n    this.williamsSeries.applyOptions({color: this.options.color})\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n\r\n  setPaneIndex(paneIndex: number) {\r\n    this.williamsSeries.moveToPane(paneIndex)\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.williamsSeries.getPane().paneIndex()\r\n  }\r\n}"], "names": ["defaultOptions", "WilliamsIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "highSeries", "lowSeries", "closeSeries", "highs", "lows", "closes", "highestHigh", "lowestLow", "currentClose", "range", "<PERSON><PERSON>sR", "clampedWilliamsR", "williamsData", "bar", "value"], "mappings": "6bAaaA,EAA2C,CACtD,MAAO,wBACP,eAAgB,4BAChB,gBAAiB,YACjB,OAAQ,GACR,QAAS,EACX,EAMA,MAAqBC,UAA0BC,EAAAA,cAAuD,CAGpG,YAAYC,EAAkBC,EAA6CC,EAAoB,CAC7F,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,uBAIO,KAAA,eAAiBH,EAAM,UAAUI,EAAAA,WAAY,CAChD,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,WACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,EAAG,SAAU,IAAK,CAAA,GAChFH,CAAS,EAEZ,KAAK,eAAe,gBAClB,IAAII,kBAAgB,CAClB,QAAS,IACT,SAAU,IACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAA8C,CACrC,OAAAT,CAAA,CAGT,QAAQU,EAAsC,CACtC,MAAAC,EAAaD,EAAE,QAAQA,EAAE,OAAO,KAAM,KAAK,QAAQ,MAAM,EACzDE,EAAYF,EAAE,QAAQA,EAAE,OAAO,IAAK,KAAK,QAAQ,MAAM,EACvDG,EAAcH,EAAE,QAAQA,EAAE,OAAO,MAAO,KAAK,QAAQ,MAAM,EAE9D,GAAA,CAACC,EAAW,WAAA,GAAgB,CAACC,EAAU,cAAgB,CAACC,EAAY,aAAc,OAG/E,MAAAC,EAAQH,EAAW,OAAO,EAC1BI,EAAOH,EAAU,OAAO,EACxBI,EAASH,EAAY,OAAO,EAMlC,GAAIC,EAAM,OAAS,KAAK,QAAQ,QAAUC,EAAK,OAAS,KAAK,QAAQ,QAAUC,EAAO,OAAS,KAAK,QAAQ,OAC1G,OAIF,MAAMC,EAAc,KAAK,IAAI,GAAGH,CAAK,EAC/BI,EAAY,KAAK,IAAI,GAAGH,CAAI,EAC5BI,EAAeH,EAAOA,EAAO,OAAS,CAAC,EAGvCI,EAAQH,EAAcC,EAC5B,GAAIE,IAAU,EACZ,MAAO,CAAC,CAAiB,EAIrB,MAAAC,GAAcJ,EAAcE,GAAgBC,EAAS,KAGrDE,EAAmB,KAAK,IAAI,KAAM,KAAK,IAAI,EAAGD,CAAS,CAAC,EAG9D,eAAQ,IAAI,2BAA4B,CACtC,OAAQ,KAAK,QAAQ,OACrB,YAAAJ,EACA,UAAAC,EACA,aAAAC,EACA,MAAAC,EACA,UAAAC,EACA,iBAAAC,CAAA,CACD,EAEM,CAACA,CAAgC,CAAA,CAI1C,oBAAqB,CACnB,MAAMC,EAAkC,CAAC,EAC/B,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MACdC,GACSF,EAAA,KAAK,CAAC,KAAMC,EAAI,KAAc,MAAOC,EAAM,CAAC,EAAE,CAAA,CAGxD,KAAA,eAAe,QAAQF,CAAY,CAAA,CAG1C,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,cAAc,CAAA,CAG7C,eAAgB,CACd,KAAK,eAAe,aAAa,CAAC,MAAO,KAAK,QAAQ,MAAM,EAC5D,KAAK,mBAAmB,CAAA,CAI1B,aAAalB,EAAmB,CACzB,KAAA,eAAe,WAAWA,CAAS,CAAA,CAG1C,cAAuB,CACrB,OAAO,KAAK,eAAe,QAAQ,EAAE,UAAU,CAAA,CAEnD"}