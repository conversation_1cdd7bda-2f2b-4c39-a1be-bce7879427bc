{"version": 3, "file": "market.es.js", "sources": ["../../src/advance-chart/market.ts"], "sourcesContent": ["import { Nominal } from \"lightweight-charts\"\r\nimport type { Dayjs } from \"dayjs\"\r\nimport dayjs from \"../helpers/dayjs-setup\"\r\n\r\ntype IMarketDate = Nominal<Dayjs, 'marketDate'>\r\n\r\nexport interface IMarket {\r\n  isOpen(): boolean\r\n  isOpen(date: Date): boolean\r\n  toMarketDate(date: Dayjs): IMarketDate\r\n  marketZoneNow(): IMarketDate\r\n  getOpen(date: Date): IMarketDate\r\n  getClose(date: Date): IMarketDate\r\n  getNextOpen(date: Date): IMarketDate\r\n  getNextClose(date: Date): IMarketDate\r\n  getPrevOpen(date: Date): IMarketDate\r\n  getPrevClose(date: Date): IMarketDate\r\n}\r\n\r\ninterface IMarketOptions {\r\n  name: string\r\n  timeZone: string\r\n  open: string\r\n  close: string\r\n}\r\n\r\ninterface TimeBlock {\r\n  start: string  // HH:mm format\r\n  end: string    // HH:mm format\r\n  isOpen: boolean\r\n}\r\n\r\ntype MarketDateInput = Dayjs | Date | string\r\n\r\nexport class Market implements IMarket {\r\n  private parsedOpenTime: { hours: number; minutes: number }\r\n  private parsedCloseTime: { hours: number; minutes: number }\r\n\r\n  constructor(protected options: IMarketOptions) {\r\n    this.parsedOpenTime = this.parseTime(this.options.open)\r\n    this.parsedCloseTime = this.parseTime(this.options.close)\r\n  }\r\n\r\n  public toMarketDate(date: MarketDateInput): IMarketDate {\r\n    return dayjs(date).tz(this.options.timeZone) as IMarketDate\r\n  }\r\n\r\n  private parseTime(timeStr: string): { hours: number; minutes: number } {\r\n    const [hours, minutes] = timeStr.split(':').map(Number)\r\n    return { hours, minutes }\r\n  }\r\n\r\n  private getTimeBlocksForDay(): TimeBlock[] {\r\n\r\n    const blocks: TimeBlock[] = [{\r\n      start: '00:00',\r\n      end: this.options.open,\r\n      isOpen: false\r\n    }, {\r\n      start: this.options.open,\r\n      end: this.options.close,\r\n      isOpen: true\r\n    }, {\r\n      start: this.options.close,\r\n      end: '23:59',\r\n      isOpen: false\r\n    }]\r\n\r\n    return blocks\r\n  }\r\n\r\n  private isTimeInRange(time: string, block: TimeBlock): boolean {\r\n    return time >= block.start && time < block.end\r\n  }\r\n\r\n  marketZoneNow(): IMarketDate {\r\n    return this.toMarketDate(dayjs())\r\n  }\r\n\r\n  private _isOpen(dateInput: IMarketDate): boolean {\r\n    const timeToCheck = dateInput.format('HH:mm')\r\n    const blocks = this.getTimeBlocksForDay()\r\n    return blocks.some(block => this.isTimeInRange(timeToCheck, block) && block.isOpen)\r\n  }\r\n\r\n  isOpen(dateInput?: MarketDateInput): boolean {\r\n    return dateInput ? this._isOpen(this.toMarketDate(dateInput)) : this._isOpen(this.marketZoneNow())\r\n  }\r\n\r\n  getOpen(dateInput: MarketDateInput): IMarketDate {\r\n    return this.toMarketDate(dateInput)\r\n      .hour(this.parsedOpenTime.hours)\r\n      .minute(this.parsedOpenTime.minutes)\r\n      .second(0) as IMarketDate;\r\n  }\r\n\r\n  getClose(dateInput: MarketDateInput): IMarketDate {\r\n    return this.toMarketDate(dateInput)\r\n      .hour(this.parsedCloseTime.hours)\r\n      .minute(this.parsedCloseTime.minutes)\r\n      .second(0) as IMarketDate\r\n  }\r\n\r\n  public getNextOpen(dateInput: MarketDateInput): IMarketDate {\r\n    const time = this.getOpen(dateInput);\r\n    const inputDate = this.toMarketDate(dateInput);\r\n    return inputDate.isAfter(time) ? time.add(1, 'day') as IMarketDate : time;\r\n  }\r\n\r\n  public getNextClose(dateInput: MarketDateInput): IMarketDate {\r\n    const time = this.getClose(dateInput);\r\n    const inputDate = this.toMarketDate(dateInput);\r\n    return inputDate.isAfter(time) ? time.add(1, 'day') as IMarketDate : time;\r\n  }\r\n\r\n  public getPrevOpen(dateInput: MarketDateInput): IMarketDate {\r\n    const time = this.getOpen(dateInput);\r\n    const inputDate = this.toMarketDate(dateInput);\r\n    return inputDate.isBefore(time) ? time.subtract(1, 'day') as IMarketDate : time;\r\n  }\r\n\r\n  public getPrevClose(dateInput: MarketDateInput): IMarketDate {\r\n    const time = this.getClose(dateInput);\r\n    const inputDate = this.toMarketDate(dateInput);\r\n    return inputDate.isBefore(time) ? time.subtract(1, 'day') as IMarketDate : time;\r\n  }\r\n}"], "names": ["Market", "options", "__publicField", "date", "dayjs", "timeStr", "hours", "minutes", "time", "block", "dateInput", "timeToCheck"], "mappings": ";;;;;AAkCO,MAAMA,EAA0B;AAAA,EAIrC,YAAsBC,GAAyB;AAHvC,IAAAC,EAAA;AACA,IAAAA,EAAA;AAEc,SAAA,UAAAD,GACpB,KAAK,iBAAiB,KAAK,UAAU,KAAK,QAAQ,IAAI,GACtD,KAAK,kBAAkB,KAAK,UAAU,KAAK,QAAQ,KAAK;AAAA,EAAA;AAAA,EAGnD,aAAaE,GAAoC;AACtD,WAAOC,EAAMD,CAAI,EAAE,GAAG,KAAK,QAAQ,QAAQ;AAAA,EAAA;AAAA,EAGrC,UAAUE,GAAqD;AAC/D,UAAA,CAACC,GAAOC,CAAO,IAAIF,EAAQ,MAAM,GAAG,EAAE,IAAI,MAAM;AAC/C,WAAA,EAAE,OAAAC,GAAO,SAAAC,EAAQ;AAAA,EAAA;AAAA,EAGlB,sBAAmC;AAgBlC,WAdqB,CAAC;AAAA,MAC3B,OAAO;AAAA,MACP,KAAK,KAAK,QAAQ;AAAA,MAClB,QAAQ;AAAA,IAAA,GACP;AAAA,MACD,OAAO,KAAK,QAAQ;AAAA,MACpB,KAAK,KAAK,QAAQ;AAAA,MAClB,QAAQ;AAAA,IAAA,GACP;AAAA,MACD,OAAO,KAAK,QAAQ;AAAA,MACpB,KAAK;AAAA,MACL,QAAQ;AAAA,IAAA,CACT;AAAA,EAEM;AAAA,EAGD,cAAcC,GAAcC,GAA2B;AAC7D,WAAOD,KAAQC,EAAM,SAASD,IAAOC,EAAM;AAAA,EAAA;AAAA,EAG7C,gBAA6B;AACpB,WAAA,KAAK,aAAaL,GAAO;AAAA,EAAA;AAAA,EAG1B,QAAQM,GAAiC;AACzC,UAAAC,IAAcD,EAAU,OAAO,OAAO;AAErC,WADQ,KAAK,oBAAoB,EAC1B,KAAK,CAASD,MAAA,KAAK,cAAcE,GAAaF,CAAK,KAAKA,EAAM,MAAM;AAAA,EAAA;AAAA,EAGpF,OAAOC,GAAsC;AAC3C,WAAOA,IAAY,KAAK,QAAQ,KAAK,aAAaA,CAAS,CAAC,IAAI,KAAK,QAAQ,KAAK,cAAA,CAAe;AAAA,EAAA;AAAA,EAGnG,QAAQA,GAAyC;AAC/C,WAAO,KAAK,aAAaA,CAAS,EAC/B,KAAK,KAAK,eAAe,KAAK,EAC9B,OAAO,KAAK,eAAe,OAAO,EAClC,OAAO,CAAC;AAAA,EAAA;AAAA,EAGb,SAASA,GAAyC;AAChD,WAAO,KAAK,aAAaA,CAAS,EAC/B,KAAK,KAAK,gBAAgB,KAAK,EAC/B,OAAO,KAAK,gBAAgB,OAAO,EACnC,OAAO,CAAC;AAAA,EAAA;AAAA,EAGN,YAAYA,GAAyC;AACpD,UAAAF,IAAO,KAAK,QAAQE,CAAS;AAE5B,WADW,KAAK,aAAaA,CAAS,EAC5B,QAAQF,CAAI,IAAIA,EAAK,IAAI,GAAG,KAAK,IAAmBA;AAAA,EAAA;AAAA,EAGhE,aAAaE,GAAyC;AACrD,UAAAF,IAAO,KAAK,SAASE,CAAS;AAE7B,WADW,KAAK,aAAaA,CAAS,EAC5B,QAAQF,CAAI,IAAIA,EAAK,IAAI,GAAG,KAAK,IAAmBA;AAAA,EAAA;AAAA,EAGhE,YAAYE,GAAyC;AACpD,UAAAF,IAAO,KAAK,QAAQE,CAAS;AAE5B,WADW,KAAK,aAAaA,CAAS,EAC5B,SAASF,CAAI,IAAIA,EAAK,SAAS,GAAG,KAAK,IAAmBA;AAAA,EAAA;AAAA,EAGtE,aAAaE,GAAyC;AACrD,UAAAF,IAAO,KAAK,SAASE,CAAS;AAE7B,WADW,KAAK,aAAaA,CAAS,EAC5B,SAASF,CAAI,IAAIA,EAAK,SAAS,GAAG,KAAK,IAAmBA;AAAA,EAAA;AAE/E;"}