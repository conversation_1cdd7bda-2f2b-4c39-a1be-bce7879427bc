{"version": 3, "file": "wma-indicator.es.js", "sources": ["../../src/indicators/wma-indicator.ts"], "sourcesContent": ["import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from \"lightweight-charts\";\r\nimport { SMAIndicatorOptions } from \"./sma-indicator\";\r\nimport { Context, IIndicatorBar } from \"../helpers/execution-indicator\";\r\nimport { SeriesPrimitiveBase } from \"../custom-primitive/primitive-base\";\r\nimport { LineData, LinePrimitivePaneView } from \"../custom-primitive/pane-view/line\";\r\nimport { ChartIndicator } from \"./abstract-indicator\";\r\n\r\nexport interface WMAIndicatorOptions extends SMAIndicatorOptions {}\r\n\r\nexport const defaultOptions: WMAIndicatorOptions = {\r\n    color: \"#03fc03\",\r\n    period: 9,\r\n    overlay: true\r\n}\r\n\r\nexport class WMAPrimitive extends SeriesPrimitiveBase<\r\nSingleValueData | WhitespaceData\r\n> {\r\n    linePrimitive: LinePrimitivePaneView;\r\n    constructor(protected source: WMAIndicator) {\r\n        super();\r\n        this.linePrimitive = new LinePrimitivePaneView({\r\n            lineColor: this.source.options.color,\r\n        });\r\n        this._paneViews = [this.linePrimitive];\r\n    }\r\n\r\n    update(indicatorBars: IIndicatorBar<WMAData>[]) {\r\n        const lineData: LineData[] = []\r\n        for(const bar of indicatorBars) {\r\n            const value = bar.value\r\n            if(value) lineData.push({time: bar.time as Time, price: value[0]})\r\n        }\r\n\r\n        this.linePrimitive.update(lineData);\r\n    }\r\n}\r\n\r\nexport type WMAData = readonly [Nominal<number, 'WMA'>]\r\n\r\nexport default class WMAIndicator extends ChartIndicator<WMAIndicatorOptions, WMAData> {\r\n    wmaPrimitive = new WMAPrimitive(this)\r\n\r\n    getDefaultOptions(): WMAIndicatorOptions {\r\n        return defaultOptions\r\n    }\r\n\r\n    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {\r\n        series.attachPrimitive(this.wmaPrimitive)\r\n    }\r\n\r\n    remove(): void {\r\n        super.remove();\r\n        this.mainSeries?.detachPrimitive(this.wmaPrimitive)\r\n    }\r\n\r\n    applyIndicatorData(): void {\r\n        this.wmaPrimitive.update(\r\n            this._executionContext.data\r\n        )\r\n    }\r\n\r\n    formula(c: Context) {\r\n        const prices = c.new_var(c.symbol.close, this.options.period);\r\n        if (!prices.calculable()) return;\r\n      \r\n        const values = prices.getAll();\r\n        const period = this.options.period;\r\n      \r\n        if (values.length < period) return;\r\n      \r\n        const weights = Array.from({ length: period }, (_, i) => period - i);\r\n        const weightSum = weights.reduce((a, b) => a + b, 0);\r\n      \r\n        const recent = values.slice(-period);\r\n        const weightedSum = recent.reduce((sum, val, i) => sum + val * weights[i], 0);\r\n      \r\n        const wma = weightedSum / weightSum;\r\n      \r\n        return [wma as Nominal<number, 'WMA'>] as WMAData;\r\n    }\r\n\r\n}"], "names": ["defaultOptions", "WMAPrimitive", "SeriesPrimitiveBase", "source", "__publicField", "LinePrimitivePaneView", "indicatorBars", "lineData", "bar", "value", "WMAIndicator", "ChartIndicator", "series", "_a", "c", "prices", "values", "period", "weights", "_", "i", "weightSum", "b", "sum", "val"], "mappings": ";;;;;;AASO,MAAMA,IAAsC;AAAA,EAC/C,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AACb;AAEO,MAAMC,UAAqBC,EAEhC;AAAA,EAEE,YAAsBC,GAAsB;AAClC,UAAA;AAFV,IAAAC,EAAA;AACsB,SAAA,SAAAD,GAEb,KAAA,gBAAgB,IAAIE,EAAsB;AAAA,MAC3C,WAAW,KAAK,OAAO,QAAQ;AAAA,IAAA,CAClC,GACI,KAAA,aAAa,CAAC,KAAK,aAAa;AAAA,EAAA;AAAA,EAGzC,OAAOC,GAAyC;AAC5C,UAAMC,IAAuB,CAAC;AAC9B,eAAUC,KAAOF,GAAe;AAC5B,YAAMG,IAAQD,EAAI;AACf,MAAAC,KAAgBF,EAAA,KAAK,EAAC,MAAMC,EAAI,MAAc,OAAOC,EAAM,CAAC,EAAA,CAAE;AAAA,IAAA;AAGhE,SAAA,cAAc,OAAOF,CAAQ;AAAA,EAAA;AAE1C;AAIA,MAAqBG,UAAqBC,EAA6C;AAAA,EAAvF;AAAA;AACI,IAAAP,EAAA,sBAAe,IAAIH,EAAa,IAAI;AAAA;AAAA,EAEpC,oBAAyC;AAC9B,WAAAD;AAAA,EAAA;AAAA,EAGX,mBAAmBY,GAAsC;AAC9C,IAAAA,EAAA,gBAAgB,KAAK,YAAY;AAAA,EAAA;AAAA,EAG5C,SAAe;;AACX,UAAM,OAAO,IACRC,IAAA,KAAA,eAAA,QAAAA,EAAY,gBAAgB,KAAK;AAAA,EAAY;AAAA,EAGtD,qBAA2B;AACvB,SAAK,aAAa;AAAA,MACd,KAAK,kBAAkB;AAAA,IAC3B;AAAA,EAAA;AAAA,EAGJ,QAAQC,GAAY;AACV,UAAAC,IAASD,EAAE,QAAQA,EAAE,OAAO,OAAO,KAAK,QAAQ,MAAM;AACxD,QAAA,CAACC,EAAO,aAAc;AAEpB,UAAAC,IAASD,EAAO,OAAO,GACvBE,IAAS,KAAK,QAAQ;AAExB,QAAAD,EAAO,SAASC,EAAQ;AAEtB,UAAAC,IAAU,MAAM,KAAK,EAAE,QAAQD,EAAO,GAAG,CAACE,GAAGC,MAAMH,IAASG,CAAC,GAC7DC,IAAYH,EAAQ,OAAO,CAAC,GAAGI,MAAM,IAAIA,GAAG,CAAC;AAOnD,WAAO,CALQN,EAAO,MAAM,CAACC,CAAM,EACR,OAAO,CAACM,GAAKC,GAAKJ,MAAMG,IAAMC,IAAMN,EAAQE,CAAC,GAAG,CAAC,IAElDC,CAEW;AAAA,EAAA;AAG7C;"}