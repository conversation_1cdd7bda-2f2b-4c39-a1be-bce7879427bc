import { Time } from 'lightweight-charts';
import { default as dayjs } from './dayjs-setup';
import { Dayjs } from 'dayjs';

export declare function timeToDate(time: Time): Date;
export declare function dateToTime(date: Date): Time;
export declare function timeToDayjs(time: Time): dayjs.Dayjs;
export declare function timeToUnix(time: Time): number;
export declare function dayjsToTime(djs: Dayjs): Time;
export declare const defaultCompare: (a: number | string, b: number | string) => 1 | -1 | 0;
export declare function binarySearchIndex<T, V extends number | string>(arr: T[], target: V, keyFn: (item: T) => V, compare?: (a: V, b: V) => number): number;
export declare function binarySearch<T, V extends number | string>(arr: T[], target: V, keyFn: (item: T) => V, compare?: (a: V, b: V) => number): T | undefined;
export declare function autoScaleInfoProviderCreator(defaultPriceRange: {
    maxValue: number;
    minValue: number;
}): (baseImplementation: () => import('lightweight-charts').AutoscaleInfo | null) => {
    priceRange: {
        maxValue: number;
        minValue: number;
    };
};
