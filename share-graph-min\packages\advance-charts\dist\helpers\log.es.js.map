{"version": 3, "file": "log.es.js", "sources": ["../../src/helpers/log.ts"], "sourcesContent": ["export interface ILogger {\r\n  debug(name: string, ...args: unknown[]): void;\r\n  info(name: string, ...args: unknown[]): void;\r\n  warn(name: string, ...args: unknown[]): void;\r\n  error(name: string, ...args: unknown[]): void;\r\n}\r\n\r\nexport enum Log {\r\n  NONE = 0,\r\n  ERROR = 1,\r\n  WARN = 2,\r\n  INFO = 3,\r\n  DEBUG = 4\r\n}\r\n\r\n// Module state\r\nlet currentLevel = Log.NONE;\r\nlet currentLogger: ILogger = {\r\n  debug: (name, ...args) => console.debug(`[${name}]`, ...args),\r\n  info: (name, ...args) => console.info(`[${name}]`, ...args),\r\n  warn: (name, ...args) => console.warn(`[${name}]`, ...args),\r\n  error: (name, ...args) => console.error(`[${name}]`, ...args)\r\n};\r\n\r\nexport const LogManager = {\r\n  reset: () => { currentLevel = Log.INFO; },\r\n  setLevel: (value: Log) => { currentLevel = value; },\r\n  setLogger: (value: ILogger) => { currentLogger = value; }\r\n};\r\n\r\nexport class Logger {\r\n  constructor(private name: string, private method?: string) {}\r\n\r\n  debug(...args: unknown[]): void {\r\n    if (currentLevel >= Log.DEBUG) currentLogger.debug(this.format(), ...args);\r\n  }\r\n\r\n  info(...args: unknown[]): void {\r\n    if (currentLevel >= Log.INFO) currentLogger.info(this.format(), ...args);\r\n  }\r\n\r\n  warn(...args: unknown[]): void {\r\n    if (currentLevel >= Log.WARN) currentLogger.warn(this.format(), ...args);\r\n  }\r\n\r\n  error(...args: unknown[]): void {\r\n    if (currentLevel >= Log.ERROR) currentLogger.error(this.format(), ...args);\r\n  }\r\n\r\n  private format(): string {\r\n    return this.method ? `${this.name}.${this.method}` : this.name;\r\n  }\r\n}\r\n\r\nexport const log = new Logger('Chart');\r\n"], "names": ["Log", "currentLevel", "<PERSON><PERSON><PERSON><PERSON>", "name", "args", "LogManager", "value", "<PERSON><PERSON>", "method", "log"], "mappings": "AAOY,IAAAA,sBAAAA,OACVA,EAAAA,EAAA,OAAO,CAAP,IAAA,QACAA,EAAAA,EAAA,QAAQ,CAAR,IAAA,SACAA,EAAAA,EAAA,OAAO,CAAP,IAAA,QACAA,EAAAA,EAAA,OAAO,CAAP,IAAA,QACAA,EAAAA,EAAA,QAAQ,CAAR,IAAA,SALUA,IAAAA,KAAA,CAAA,CAAA;AASZ,IAAIC,IAAe,GACfC,IAAyB;AAAA,EAC3B,OAAO,CAACC,MAASC,MAAS,QAAQ,MAAM,IAAID,CAAI,KAAK,GAAGC,CAAI;AAAA,EAC5D,MAAM,CAACD,MAASC,MAAS,QAAQ,KAAK,IAAID,CAAI,KAAK,GAAGC,CAAI;AAAA,EAC1D,MAAM,CAACD,MAASC,MAAS,QAAQ,KAAK,IAAID,CAAI,KAAK,GAAGC,CAAI;AAAA,EAC1D,OAAO,CAACD,MAASC,MAAS,QAAQ,MAAM,IAAID,CAAI,KAAK,GAAGC,CAAI;AAC9D;AAEO,MAAMC,IAAa;AAAA,EACxB,OAAO,MAAM;AAAiB,IAAAJ,IAAA;AAAA,EAAU;AAAA,EACxC,UAAU,CAACK,MAAe;AAAiB,IAAAL,IAAAK;AAAA,EAAO;AAAA,EAClD,WAAW,CAACA,MAAmB;AAAkB,IAAAJ,IAAAI;AAAA,EAAA;AACnD;AAEO,MAAMC,EAAO;AAAA,EAClB,YAAoBJ,GAAsBK,GAAiB;AAAvC,SAAA,OAAAL,GAAsB,KAAA,SAAAK;AAAA,EAAA;AAAA,EAE1C,SAASJ,GAAuB;AAC1B,IAAAH,KAAgB,KAAyBC,EAAA,MAAM,KAAK,OAAO,GAAG,GAAGE,CAAI;AAAA,EAAA;AAAA,EAG3E,QAAQA,GAAuB;AACzB,IAAAH,KAAgB,KAAwBC,EAAA,KAAK,KAAK,OAAO,GAAG,GAAGE,CAAI;AAAA,EAAA;AAAA,EAGzE,QAAQA,GAAuB;AACzB,IAAAH,KAAgB,KAAwBC,EAAA,KAAK,KAAK,OAAO,GAAG,GAAGE,CAAI;AAAA,EAAA;AAAA,EAGzE,SAASA,GAAuB;AAC1B,IAAAH,KAAgB,KAAyBC,EAAA,MAAM,KAAK,OAAO,GAAG,GAAGE,CAAI;AAAA,EAAA;AAAA,EAGnE,SAAiB;AAChB,WAAA,KAAK,SAAS,GAAG,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK;AAAA,EAAA;AAE9D;AAEa,MAAAK,IAAM,IAAIF,EAAO,OAAO;"}