import { LineStyle as i } from "lightweight-charts";
function s(e, t) {
  const n = {
    [i.Solid]: [],
    [i.Dotted]: [e.lineWidth, e.lineWidth],
    [i.Dashed]: [2 * e.lineWidth, 2 * e.lineWidth],
    [i.LargeDashed]: [6 * e.lineWidth, 6 * e.lineWidth],
    [i.SparseDotted]: [e.lineWidth, 4 * e.lineWidth]
  }[t];
  e.setLineDash(n);
}
export {
  s as setLineStyle
};
//# sourceMappingURL=line-style.es.js.map
