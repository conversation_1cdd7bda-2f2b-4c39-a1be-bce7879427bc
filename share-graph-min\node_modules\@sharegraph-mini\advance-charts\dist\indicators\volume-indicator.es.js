var n = Object.defineProperty;
var m = (r, t, e) => t in r ? n(r, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : r[t] = e;
var a = (r, t, e) => m(r, typeof t != "symbol" ? t + "" : t, e);
import { HistogramSeries as u } from "lightweight-charts";
import { ChartIndicator as c } from "./abstract-indicator.es.js";
import { Color as v } from "../helpers/color.es.js";
import { ensureDefined as l } from "../helpers/assertions.es.js";
const h = {
  upColor: "#26a69a",
  downColor: "#ef5350",
  overlay: !1
};
class C extends c {
  constructor(e, o, i) {
    super(e, o);
    a(this, "volumeSeries");
    this.chart = e;
    const s = this.options.numberFormatter;
    this.volumeSeries = this.chart.addSeries(
      u,
      {
        priceLineVisible: !1,
        priceFormat: s ? {
          type: "custom",
          formatter: (p) => s().volume(p)
        } : { type: "volume" },
        priceScaleId: "volume"
      },
      this.options.overlay ? 0 : i
    ), this.applyPriceScaleMargins();
  }
  applyPriceScaleMargins() {
    this.options.overlay ? this.volumeSeries.priceScale().applyOptions({
      scaleMargins: {
        top: 0.7,
        bottom: 0
      }
    }) : this.volumeSeries.priceScale().applyOptions({
      scaleMargins: {
        top: 0.1,
        bottom: 0
      }
    });
  }
  _applyOptions(e) {
    (e.downColor || e.upColor) && this.applyIndicatorData();
  }
  applyIndicatorData() {
    const e = this._executionContext.data.filter((o) => o.value);
    this.volumeSeries.setData(
      e.map((o) => ({
        time: o.time,
        value: l(o.value)[0],
        color: v.applyAlpha(
          l(o.value)[1] === 1 ? this.options.upColor : this.options.downColor,
          this.options.overlay ? 0.6 : 1
        )
      }))
    );
  }
  formula(e) {
    const o = e.new_var(e.symbol.close, 2);
    if (!o.calculable()) return;
    const i = o.get(0) > o.get(1) ? 1 : 0;
    return [e.symbol.volume, i];
  }
  getDefaultOptions() {
    return h;
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.volumeSeries);
  }
  setPaneIndex(e) {
    this.volumeSeries.moveToPane(e), this.applyPriceScaleMargins();
  }
  getPaneIndex() {
    return this.volumeSeries.getPane().paneIndex();
  }
}
export {
  C as default,
  h as defaultOptions
};
//# sourceMappingURL=volume-indicator.es.js.map
