import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface WilliamsIndicatorOptions extends ChartIndicatorOptions {
    color: string;
    period: number;
    priceLineColor: string;
    backgroundColor: string;
}
export declare const defaultOptions: WilliamsIndicatorOptions;
export type WilliamsLine = Nominal<number, 'Williams'>;
export type WilliamsData = [WilliamsLine];
export default class WilliamsIndicator extends ChartIndicator<WilliamsIndicatorOptions, WilliamsData> {
    williamsSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<WilliamsIndicatorOptions>, paneIndex?: number);
    getDefaultOptions(): WilliamsIndicatorOptions;
    formula(c: Context): WilliamsData | undefined;
    applyIndicatorData(): void;
    remove(): void;
    _applyOptions(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
