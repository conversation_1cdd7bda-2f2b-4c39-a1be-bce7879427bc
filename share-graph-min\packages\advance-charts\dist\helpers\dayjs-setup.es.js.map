{"version": 3, "file": "dayjs-setup.es.js", "sources": ["../../src/helpers/dayjs-setup.ts"], "sourcesContent": ["import dayjs from \"dayjs\"\r\nimport utc from 'dayjs/plugin/utc'\r\nimport timezone from 'dayjs/plugin/timezone'\r\nimport weekYear from 'dayjs/plugin/weekYear'\r\nimport weekOfYear from 'dayjs/plugin/weekOfYear'\r\n\r\ndayjs.extend(utc)\r\ndayjs.extend(timezone)\r\ndayjs.extend(weekYear)\r\ndayjs.extend(weekOfYear)\r\n\r\nexport { timezone, weekYear, weekOfYear }\r\n\r\nexport default dayjs"], "names": ["dayjs", "utc", "timezone", "weekYear", "weekOfYear"], "mappings": ";;;;;;;;;AAMAA,EAAM,OAAOC,CAAG;AAChBD,EAAM,OAAOE,CAAQ;AACrBF,EAAM,OAAOG,CAAQ;AACrBH,EAAM,OAAOI,CAAU;"}