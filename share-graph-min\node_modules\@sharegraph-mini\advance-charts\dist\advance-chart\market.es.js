var a = Object.defineProperty;
var p = (s, t, e) => t in s ? a(s, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : s[t] = e;
var o = (s, t, e) => p(s, typeof t != "symbol" ? t + "" : t, e);
import "../helpers/dayjs-setup.es.js";
import n from "dayjs";
class c {
  constructor(t) {
    o(this, "parsedOpenTime");
    o(this, "parsedCloseTime");
    this.options = t, this.parsedOpenTime = this.parseTime(this.options.open), this.parsedCloseTime = this.parseTime(this.options.close);
  }
  toMarketDate(t) {
    return n(t).tz(this.options.timeZone);
  }
  parseTime(t) {
    const [e, i] = t.split(":").map(Number);
    return { hours: e, minutes: i };
  }
  getTimeBlocksForDay() {
    return [{
      start: "00:00",
      end: this.options.open,
      isOpen: !1
    }, {
      start: this.options.open,
      end: this.options.close,
      isOpen: !0
    }, {
      start: this.options.close,
      end: "23:59",
      isOpen: !1
    }];
  }
  isTimeInRange(t, e) {
    return t >= e.start && t < e.end;
  }
  marketZoneNow() {
    return this.toMarketDate(n());
  }
  _isOpen(t) {
    const e = t.format("HH:mm");
    return this.getTimeBlocksForDay().some((r) => this.isTimeInRange(e, r) && r.isOpen);
  }
  isOpen(t) {
    return t ? this._isOpen(this.toMarketDate(t)) : this._isOpen(this.marketZoneNow());
  }
  getOpen(t) {
    return this.toMarketDate(t).hour(this.parsedOpenTime.hours).minute(this.parsedOpenTime.minutes).second(0);
  }
  getClose(t) {
    return this.toMarketDate(t).hour(this.parsedCloseTime.hours).minute(this.parsedCloseTime.minutes).second(0);
  }
  getNextOpen(t) {
    const e = this.getOpen(t);
    return this.toMarketDate(t).isAfter(e) ? e.add(1, "day") : e;
  }
  getNextClose(t) {
    const e = this.getClose(t);
    return this.toMarketDate(t).isAfter(e) ? e.add(1, "day") : e;
  }
  getPrevOpen(t) {
    const e = this.getOpen(t);
    return this.toMarketDate(t).isBefore(e) ? e.subtract(1, "day") : e;
  }
  getPrevClose(t) {
    const e = this.getClose(t);
    return this.toMarketDate(t).isBefore(e) ? e.subtract(1, "day") : e;
  }
}
export {
  c as Market
};
//# sourceMappingURL=market.es.js.map
