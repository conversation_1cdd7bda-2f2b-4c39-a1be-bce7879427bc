"use strict";var m=Object.defineProperty;var u=(r,t,e)=>t in r?m(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var c=(r,t,e)=>u(r,typeof t!="symbol"?t+"":t,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const d=require("lightweight-charts"),p=require("./abstract-indicator.cjs.js"),l={period:14,usePercentage:!1,color:"#2b97f1",zeroLineColor:"#808080",overlay:!1};class h extends p.ChartIndicator{constructor(e,s,o){super(e,s);c(this,"momentumSeries");this.momentumSeries=e.addSeries(d.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"momentum"},o)}applyIndicatorData(){const e=[];for(const s of this._executionContext.data){const o=s.value,n=s.time;if(!o)continue;const[i]=o;isNaN(i)||e.push({time:n,value:i})}this.momentumSeries.setData(e)}formula(e){const s=this.options.period,o=e.symbol.close,n=e.new_var(o,s+1);if(!n.calculable())return;const i=n.get(s);let a;return this.options.usePercentage?a=(o-i)/i*100:a=o-i,[a]}_applyOptions(e){(e.period||e.usePercentage)&&this.calcIndicatorData(),e.color&&this.momentumSeries.applyOptions({color:e.color}),this.applyIndicatorData()}getDefaultOptions(){return l}remove(){super.remove(),this.chart.removeSeries(this.momentumSeries)}setPaneIndex(e){this.momentumSeries.moveToPane(e)}getPaneIndex(){return this.momentumSeries.getPane().paneIndex()}}exports.default=h;exports.defaultOptions=l;
//# sourceMappingURL=momentum-indicator.cjs.js.map
