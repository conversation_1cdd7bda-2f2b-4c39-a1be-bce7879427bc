"use strict";var g=Object.defineProperty;var l=(e,t,r)=>t in e?g(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var o=(e,t,r)=>l(e,typeof t!="symbol"?t+"":t,r);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const u=require("es-toolkit");class c{static registerIndicator(t,r,i){this.registry.set(t,[r,i])}static indicatorRegistered(t){return this.registry.has(t)}static createIndicator(t,r,i,n){const s=this.registry.get(t);if(!s)throw new Error(`Indicator "${t}" not registered. Available: ${Array.from(this.registry.keys()).join(", ")}`);const[d,a]=s;return new d(r,i||a?u.merge(structuredClone(a)??{},i??{}):void 0,n)}}o(c,"registry",new Map);exports.IndicatorFactory=c;
//# sourceMappingURL=indicator-factory.cjs.js.map
