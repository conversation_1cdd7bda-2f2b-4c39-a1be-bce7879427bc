import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface RSIIndicatorOptions extends ChartIndicatorOptions {
    color: string;
    period: number;
    priceLineColor: string;
    backgroundColor: string;
}
export declare const defaultOptions: RSIIndicatorOptions;
export type RsiLine = Nominal<number, 'RSI'>;
export type RsiData = [RsiLine];
export default class RSIIndicator extends ChartIndicator<RSIIndicatorOptions, RsiData> {
    rsiSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<RSIIndicatorOptions>, paneIndex?: number);
    getDefaultOptions(): RSIIndicatorOptions;
    formula(c: Context): RsiData | undefined;
    applyIndicatorData(): void;
    remove(): void;
    _applyOptions(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
