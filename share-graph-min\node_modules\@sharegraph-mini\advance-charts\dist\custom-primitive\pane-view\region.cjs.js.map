{"version": 3, "file": "region.cjs.js", "sources": ["../../../src/custom-primitive/pane-view/region.ts"], "sourcesContent": ["import {CanvasRenderingTarget2D} from \"fancy-canvas\";\r\nimport {PrimitivePaneViewBase} from \"../primitive-base\";\r\nimport {Coordinate} from \"lightweight-charts\";\r\nimport {ensureDefined} from \"../../helpers/assertions\";\r\n\r\nexport interface RegionPrimitivePaneViewOptions {\r\n  backgroundColor: string,\r\n  lineWidth: number, \r\n  lineColor: string;\r\n  lineStyle: number[]\r\n}\r\n\r\nexport const RegionPrimitiveOptionsDefault: RegionPrimitivePaneViewOptions = {\r\n  backgroundColor: '#2196f31a',\r\n  lineColor: '#787b86',\r\n  lineWidth: 1,\r\n  lineStyle: []\r\n}\r\n\r\ninterface RegionPrimitiveData {\r\n  x: Coordinate, \r\n  points: Coordinate[]\r\n}\r\n\r\nexport class RegionPrimitivePaneView extends PrimitivePaneViewBase<RegionPrimitivePaneViewOptions, RegionPrimitiveData> {\r\n  drawBackground(target: CanvasRenderingTarget2D): void {\r\n    const data = this.data\r\n    const visibleLogicalRange = this.getVisibleLogicalRange()\r\n    if(data.length < 2) return;\r\n    if(!visibleLogicalRange) return;\r\n    target.useBitmapCoordinateSpace(scope => {\r\n      const ctx = scope.context;\r\n      ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);\r\n\r\n      ctx.beginPath();\r\n      const region = new Path2D();\r\n      const from = 0;\r\n      const to = data.length\r\n      const firstBar = data[from];\r\n      region.moveTo(\r\n        firstBar.x, \r\n        ensureDefined(firstBar.points.at(0))\r\n      );\r\n\r\n      for (let i = from + 1; i < to; i++) {\r\n        const point = data[i];\r\n        region.lineTo(\r\n          point.x,\r\n          point.points.at(0) as number\r\n        );\r\n      }\r\n\r\n      for (let i = to - 1; i >= from; i--) {\r\n        const point = data[i];\r\n        region.lineTo(point.x, point.points.at(-1) as number);\r\n      }\r\n\r\n      region.lineTo(\r\n        firstBar.x, \r\n        ensureDefined(firstBar.points.at(0))\r\n      );\r\n      region.closePath();\r\n      ctx.fillStyle = this.options.backgroundColor\r\n      ctx.fill(region)\r\n\r\n      const paths = firstBar.points.map(() => new Path2D)\r\n\r\n      firstBar.points.map((point, index) => paths[index].moveTo(firstBar.x, point))\r\n\r\n      for (let i = from + 1; i < to; i++) {\r\n        const bar = data[i]\r\n        bar.points.map((point, index) => paths[index].lineTo(bar.x, point))\r\n      }\r\n\r\n      ctx.setLineDash(this.options.lineStyle);\r\n      ctx.lineWidth = this.options.lineWidth\r\n      ctx.strokeStyle = this.options.lineColor\r\n      paths.map(path => ctx.stroke(path))\r\n      ctx.setLineDash([]);\r\n    })\r\n    // const points: Coordinate[] = this.points\r\n    // if(points.length < 2) return;\r\n    // target.useBitmapCoordinateSpace(scope => {\r\n    //   const ctx = scope.context;\r\n    //   ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);\r\n\r\n    //   ctx.lineWidth = this.options.lineWidth;\r\n    //   ctx.strokeStyle = this.options.lineColor;\r\n      \r\n    //   ctx.beginPath();\r\n\r\n    //   const topLine = points[0]\r\n    //   const bottomLine = points[points.length - 1];\r\n\r\n    //   const width = ctx.canvas.width\r\n\r\n    //   const region = new Path2D();\r\n    //   const lines = new Path2D();\r\n\r\n      \r\n    //   for(const point of points) {\r\n    //     lines.moveTo(0, point)\r\n    //     lines.lineTo(width, point)\r\n    //   }\r\n\r\n    //   region.moveTo(0, topLine)\r\n    //   region.lineTo(width, topLine)\r\n    //   region.lineTo(width, bottomLine)\r\n    //   region.lineTo(0, bottomLine)\r\n    //   region.lineTo(0, topLine)\r\n    //   region.closePath();\r\n\r\n    //   ctx.setLineDash(LINE_DASH)\r\n    //   ctx.stroke(lines)\r\n    //   ctx.setLineDash([])\r\n    //   ctx.fillStyle = this.options.backgroundColor;\r\n    //   ctx.fill(region)\r\n    // })\r\n  }\r\n  defaultOptions(): RegionPrimitivePaneViewOptions {\r\n    return RegionPrimitiveOptionsDefault\r\n  }\r\n}"], "names": ["RegionPrimitiveOptionsDefault", "RegionPrimitivePaneView", "PrimitivePaneViewBase", "target", "data", "visibleLogicalRange", "scope", "ctx", "region", "from", "to", "firstBar", "ensureDefined", "point", "paths", "index", "bar", "path"], "mappings": "yKAYaA,EAAgE,CAC3E,gBAAiB,YACjB,UAAW,UACX,UAAW,EACX,UAAW,CAAA,CACb,EAOO,MAAMC,UAAgCC,EAAAA,qBAA2E,CACtH,eAAeC,EAAuC,CACpD,MAAMC,EAAO,KAAK,KACZC,EAAsB,KAAK,uBAAuB,EACrDD,EAAK,OAAS,GACbC,GACJF,EAAO,yBAAkCG,GAAA,CACvC,MAAMC,EAAMD,EAAM,QAClBC,EAAI,MAAMD,EAAM,qBAAsBA,EAAM,kBAAkB,EAE9DC,EAAI,UAAU,EACR,MAAAC,EAAS,IAAI,OACbC,EAAO,EACPC,EAAKN,EAAK,OACVO,EAAWP,EAAKK,CAAI,EACnBD,EAAA,OACLG,EAAS,EACTC,EAAAA,cAAcD,EAAS,OAAO,GAAG,CAAC,CAAC,CACrC,EAEA,QAAS,EAAIF,EAAO,EAAG,EAAIC,EAAI,IAAK,CAC5B,MAAAG,EAAQT,EAAK,CAAC,EACbI,EAAA,OACLK,EAAM,EACNA,EAAM,OAAO,GAAG,CAAC,CACnB,CAAA,CAGF,QAAS,EAAIH,EAAK,EAAG,GAAKD,EAAM,IAAK,CAC7B,MAAAI,EAAQT,EAAK,CAAC,EACpBI,EAAO,OAAOK,EAAM,EAAGA,EAAM,OAAO,GAAG,EAAE,CAAW,CAAA,CAG/CL,EAAA,OACLG,EAAS,EACTC,EAAAA,cAAcD,EAAS,OAAO,GAAG,CAAC,CAAC,CACrC,EACAH,EAAO,UAAU,EACbD,EAAA,UAAY,KAAK,QAAQ,gBAC7BA,EAAI,KAAKC,CAAM,EAEf,MAAMM,EAAQH,EAAS,OAAO,IAAI,IAAM,IAAI,MAAM,EAElDA,EAAS,OAAO,IAAI,CAACE,EAAOE,IAAUD,EAAMC,CAAK,EAAE,OAAOJ,EAAS,EAAGE,CAAK,CAAC,EAE5E,QAAS,EAAIJ,EAAO,EAAG,EAAIC,EAAI,IAAK,CAC5B,MAAAM,EAAMZ,EAAK,CAAC,EAClBY,EAAI,OAAO,IAAI,CAACH,EAAOE,IAAUD,EAAMC,CAAK,EAAE,OAAOC,EAAI,EAAGH,CAAK,CAAC,CAAA,CAGhEN,EAAA,YAAY,KAAK,QAAQ,SAAS,EAClCA,EAAA,UAAY,KAAK,QAAQ,UACzBA,EAAA,YAAc,KAAK,QAAQ,UAC/BO,EAAM,IAAIG,GAAQV,EAAI,OAAOU,CAAI,CAAC,EAC9BV,EAAA,YAAY,EAAE,CAAA,CACnB,CAAA,CAwCH,gBAAiD,CACxC,OAAAP,CAAA,CAEX"}