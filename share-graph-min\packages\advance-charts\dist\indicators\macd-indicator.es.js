var u = Object.defineProperty;
var P = (r, s, e) => s in r ? u(r, s, { enumerable: !0, configurable: !0, writable: !0, value: e }) : r[s] = e;
var m = (r, s, e) => P(r, typeof s != "symbol" ? s + "" : s, e);
import { HistogramSeries as v, LineSeries as p } from "lightweight-charts";
import { ChartIndicator as A, downColor as C, upColor as M } from "./abstract-indicator.es.js";
import { SMA as S, EMA as g } from "technicalindicators";
const w = {
  fastPeriod: 12,
  slowPeriod: 26,
  signalPeriod: 9,
  SimpleMAOscillator: !1,
  SimpleMASignal: !1,
  upColor: M,
  downColor: C,
  macdLineColor: "#2b97f1",
  signalLineColor: "#fd6c1c",
  overlay: !1
};
class N extends A {
  constructor(e, o, i) {
    super(e, o);
    m(this, "histogramSeries");
    m(this, "macdSeries");
    m(this, "signalSeries");
    this.histogramSeries = e.addSeries(v, {
      priceLineVisible: !1,
      priceScaleId: "macd"
    }, i), this.macdSeries = e.addSeries(p, {
      color: this.options.macdLineColor,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "macd"
    }, i), this.signalSeries = e.addSeries(p, {
      color: this.options.signalLineColor,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "macd"
    }, i);
  }
  applyIndicatorData() {
    const e = [], o = [], i = [];
    for (const t of this._executionContext.data) {
      const l = t.value, n = t.time;
      if (!l) continue;
      const [c, d, a] = l;
      isNaN(c) || o.push({ time: n, value: c }), isNaN(d) || i.push({ time: n, value: d }), isNaN(a) || e.push({ time: n, value: a, color: (a ?? 0) >= 0 ? this.options.upColor : this.options.downColor });
    }
    this.histogramSeries.setData(e), this.macdSeries.setData(o), this.signalSeries.setData(i);
  }
  formula(e) {
    const o = e.new_var(e.symbol.close, this.options.fastPeriod), i = e.new_var(e.symbol.close, this.options.slowPeriod), t = e.new_var(NaN, this.options.signalPeriod);
    if (!o.calculable() || !i.calculable()) return;
    const l = this.options.SimpleMAOscillator ? S : g, n = this.options.SimpleMASignal ? S : g, [c] = new l({ period: this.options.fastPeriod, values: o.getAll() }).result, [d] = new l({ period: this.options.slowPeriod, values: i.getAll() }).result, a = c - d;
    if (t.set(a), !t.calculable()) return;
    const [h] = new n({ period: this.options.signalPeriod, values: t.getAll() }).result, f = a - h;
    return [a, h, f];
  }
  _applyOptions(e) {
    (e.SimpleMAOscillator || e.SimpleMASignal || e.fastPeriod || e.signalPeriod || e.slowPeriod) && this.calcIndicatorData(), e.macdLineColor && this.macdSeries.applyOptions({ color: e.macdLineColor }), e.signalLineColor && this.signalSeries.applyOptions({ color: e.signalLineColor }), (e.downColor || e.upColor) && this.applyIndicatorData();
  }
  getDefaultOptions() {
    return w;
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.histogramSeries), this.chart.removeSeries(this.macdSeries), this.chart.removeSeries(this.signalSeries);
  }
  setPaneIndex(e) {
    this.histogramSeries.moveToPane(e), this.macdSeries.moveToPane(e), this.signalSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.histogramSeries.getPane().paneIndex();
  }
}
export {
  N as default,
  w as defaultOptions
};
//# sourceMappingURL=macd-indicator.es.js.map
