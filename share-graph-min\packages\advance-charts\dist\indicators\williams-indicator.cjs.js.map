{"version": 3, "file": "williams-indicator.cjs.js", "sources": ["../../src/indicators/williams-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {RSI} from \"technicalindicators\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\r\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface WilliamsIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  period: number\r\n  priceLineColor: string,\r\n  backgroundColor: string\r\n}\r\n\r\nexport const defaultOptions: WilliamsIndicatorOptions = {\r\n  color: \"rgba(108, 80, 175, 1)\",\r\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\r\n  backgroundColor: '#7e57c21a',\r\n  period: 14,\r\n  overlay: false\r\n}\r\n\r\nexport type RsiLine = Nominal<number, 'Williams'>\r\n\r\nexport type RsiData = [RsiLine]\r\n\r\nexport default class WilliamsIndicator extends ChartIndicator<WilliamsIndicatorOptions, RsiData> {\r\n  rsiSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<WilliamsIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options)\r\n    this.rsiSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'williams',\r\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 80, minValue: 20})\r\n    }, paneIndex);\r\n    \r\n    this.rsiSeries.attachPrimitive(\r\n      new RegionPrimitive({\r\n        upPrice: 70,\r\n        lowPrice: 30,\r\n        lineColor: this.options.priceLineColor,\r\n        backgroundColor: this.options.backgroundColor\r\n      })\r\n    );\r\n  }\r\n\r\n  getDefaultOptions(): WilliamsIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n\r\n  formula(c: Context): RsiData | undefined {\r\n    const closeSeries = c.new_var(c.symbol.close, this.options.period + 1);\r\n\r\n    if(!closeSeries.calculable()) return;\r\n\r\n    const [rsi] = new RSI({\r\n      period: this.options.period,\r\n      values: closeSeries.getAll()\r\n    }).result;\r\n\r\n    return [rsi]\r\n  }\r\n\r\n\r\n  applyIndicatorData() {\r\n    const rsi: SingleValueData[] = [];\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      if(!value) continue;\r\n      rsi.push({time: bar.time as Time, value: value[0]})\r\n    }\r\n\r\n    this.rsiSeries.setData(rsi)\r\n  }\r\n\r\n  remove() {\r\n    super.remove()\r\n    this.chart.removeSeries(this.rsiSeries);\r\n  }\r\n\r\n  _applyOptions() {\r\n    this.rsiSeries.applyOptions({color: this.options.color})\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n\r\n  setPaneIndex(paneIndex: number) {\r\n    this.rsiSeries.moveToPane(paneIndex)\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.rsiSeries.getPane().paneIndex()\r\n  }\r\n}"], "names": ["defaultOptions", "WilliamsIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "closeSeries", "rsi", "RSI", "bar", "value"], "mappings": "8dAcaA,EAA2C,CACtD,MAAO,wBACP,eAAgB,4BAChB,gBAAiB,YACjB,OAAQ,GACR,QAAS,EACX,EAMA,MAAqBC,UAA0BC,EAAAA,cAAkD,CAG/F,YAAYC,EAAkBC,EAA6CC,EAAoB,CAC7F,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,kBAIO,KAAA,UAAYH,EAAM,UAAUI,EAAAA,WAAY,CAC3C,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,WACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,GAAI,SAAU,EAAG,CAAA,GAC/EH,CAAS,EAEZ,KAAK,UAAU,gBACb,IAAII,kBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAA8C,CACrC,OAAAT,CAAA,CAGT,QAAQU,EAAiC,CACjC,MAAAC,EAAcD,EAAE,QAAQA,EAAE,OAAO,MAAO,KAAK,QAAQ,OAAS,CAAC,EAElE,GAAA,CAACC,EAAY,aAAc,OAE9B,KAAM,CAACC,CAAG,EAAI,IAAIC,MAAI,CACpB,OAAQ,KAAK,QAAQ,OACrB,OAAQF,EAAY,OAAO,CAC5B,CAAA,EAAE,OAEH,MAAO,CAACC,CAAG,CAAA,CAIb,oBAAqB,CACnB,MAAMA,EAAyB,CAAC,EACtB,UAAAE,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MACdC,GACAH,EAAA,KAAK,CAAC,KAAME,EAAI,KAAc,MAAOC,EAAM,CAAC,EAAE,CAAA,CAG/C,KAAA,UAAU,QAAQH,CAAG,CAAA,CAG5B,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,SAAS,CAAA,CAGxC,eAAgB,CACd,KAAK,UAAU,aAAa,CAAC,MAAO,KAAK,QAAQ,MAAM,EACvD,KAAK,mBAAmB,CAAA,CAI1B,aAAaP,EAAmB,CACzB,KAAA,UAAU,WAAWA,CAAS,CAAA,CAGrC,cAAuB,CACrB,OAAO,KAAK,UAAU,QAAQ,EAAE,UAAU,CAAA,CAE9C"}