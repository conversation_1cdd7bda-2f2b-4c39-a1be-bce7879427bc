{"version": 3, "file": "importer.js", "sourceRoot": "", "sources": ["../../../../lib/src/legacy/importer.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;;AAEvC,mCAAwC;AACxC,yBAAyB;AACzB,0BAA0B;AAC1B,6BAA6B;AAE7B,iDAA2C;AAC3C,oCAMkB;AAWlB,mCAKiB;AAEjB;;;;;GAKG;AACU,QAAA,iBAAiB,GAAG,iCAAiC,CAAC;AAEnE;;GAEG;AACU,QAAA,0BAA0B,GAAG,uBAAuB,CAAC;AAElE;;;;GAIG;AACU,QAAA,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;AAE7E,4EAA4E;AAC5E,6EAA6E;AAC7E,IAAI,cAAc,GAAG,CAAC,CAAC;AAYvB;;;GAGG;AACH,MAAa,qBAAqB;IAWb;IACA;IACA;IAEA;IAZnB,uDAAuD;IACtC,IAAI,GAAkB,EAAE,CAAC;IAE1C,qEAAqE;IACrE,wCAAwC;IAChC,YAAY,CAAqB;IAEzC,YACmB,IAAsB,EACtB,SAAsC,EACtC,SAAmB,EACpC,WAAmB,EACF,IAAuB;QAJvB,SAAI,GAAJ,IAAI,CAAkB;QACtB,cAAS,GAAT,SAAS,CAA6B;QACtC,cAAS,GAAT,SAAS,CAAU;QAEnB,SAAI,GAAJ,IAAI,CAAmB;QAExC,MAAM,IAAI,GAAG,WAAW,KAAK,OAAO,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;IACvE,CAAC;IAED,YAAY,CACV,GAAW,EACX,OAAyD;QAEzD,IAAI,GAAG,CAAC,UAAU,CAAC,yBAAiB,CAAC;YAAE,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3D,iEAAiE;QACjE,qEAAqE;QACrE,2BAA2B;QAC3B,IAAI,OAAO,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;oBAC9C,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAC;gBACH,IAAI,QAAQ,KAAK,IAAI;oBAAE,OAAO,QAAQ,CAAC;YACzC,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,IACE,KAAK,YAAY,SAAS;oBAC1B,IAAA,wBAAgB,EAAC,KAAK,CAAC;oBACvB,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAChC,CAAC;oBACD,SAAS;gBACX,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,IACE,GAAG,CAAC,UAAU,CAAC,oCAA4B,CAAC;YAC5C,GAAG,CAAC,UAAU,CAAC,8BAAsB,CAAC,EACtC,CAAC;YACD,0EAA0E;YAC1E,uEAAuE;YACvE,mEAAmE;YACnE,mEAAmE;YACnE,YAAY;YACZ,MAAM,gBAAgB,GAAG,GAAG,CAAC,SAAS,CACpC,oCAA4B,CAAC,MAAM,CACpC,CAAC;YACF,IAAI,gBAAgB,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBACzC,IAAI,QAAQ,GAAkB,IAAI,CAAC;gBAEnC,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,IAAA,kCAA0B,EAAC,gBAAgB,CAAC,CAAC;oBAC1D,QAAQ,GAAG,IAAA,0BAAW,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;gBACnD,CAAC;gBAAC,OAAO,KAAc,EAAE,CAAC;oBACxB,IACE,KAAK,YAAY,SAAS;wBAC1B,IAAA,wBAAgB,EAAC,KAAK,CAAC;wBACvB,CAAC,KAAK,CAAC,IAAI,KAAK,iBAAiB;4BAC/B,KAAK,CAAC,IAAI,KAAK,2BAA2B,CAAC,EAC7C,CAAC;wBACD,+DAA+D;wBAC/D,+DAA+D;wBAC/D,8DAA8D;wBAC9D,gEAAgE;wBAChE,gEAAgE;wBAChE,OAAO;oBACT,CAAC;yBAAM,CAAC;wBACN,MAAM,KAAK,CAAC;oBACd,CAAC;gBACH,CAAC;gBAED,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;oBAC5C,OAAO,IAAA,2BAAmB,EAAC,QAAQ,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAA,cAAM,EACX,IAAA,cAAM,EAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,EAAE;YAC5D,IAAI,MAAM,YAAY,KAAK;gBAAE,MAAM,MAAM,CAAC;YAC1C,IAAI,MAAM,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAC;YAEjC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC/B,MAAM,CACJ,6CAA6C;oBAC7C,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAC3B,CAAC;YACJ,CAAC;YAED,IAAI,UAAU,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,CAAC;gBAChD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;gBAE1C,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;oBACrB,OAAO,IAAI,GAAG,CACZ,8BAAsB;wBACpB,SAAS,CAAE,MAAyB,CAAC,IAAI,CAAC,CAC7C,CAAC;gBACJ,CAAC;qBAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACtC,OAAO,IAAI,GAAG,CAAC,GAAG,oCAA4B,GAAG,GAAG,EAAE,CAAC,CAAC;gBAC1D,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,GAAG,CAAC,8BAAsB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC9B,MAAM,QAAQ,GAAG,IAAA,0BAAW,EAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC9D,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAA,2BAAmB,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACzD,CAAC;gBAED,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC1C,IAAI,IAAI,CAAC,IAAI;oBAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;gBAErD,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;oBAC9B,MAAM,QAAQ,GAAG,IAAA,0BAAW,EAC1B,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAC3B,OAAO,CAAC,UAAU,CACnB,CAAC;oBACF,IAAI,QAAQ,KAAK,IAAI;wBAAE,OAAO,IAAA,2BAAmB,EAAC,QAAQ,CAAC,CAAC;gBAC9D,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC,CAAC,EACF,MAAM,CAAC,EAAE;YACP,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,KAAK,kCAA0B,CAAC;gBAC5D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;oBACb,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,IAAA,2BAAmB,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG;oBAC7C,IAAI;iBACL,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACtC,MAAM,QAAQ,GAAG,IAAA,0BAAW,EAC1B,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EACrB,OAAO,CAAC,UAAU,CACnB,CAAC;oBACF,IAAI,QAAQ,KAAK,IAAI;wBAAE,OAAO,IAAA,2BAAmB,EAAC,QAAQ,CAAC,CAAC;gBAC9D,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,YAAiB;QACpB,IAAI,YAAY,CAAC,QAAQ,KAAK,yBAAiB,EAAE,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YAChB,OAAO;gBACL,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,MAAM;gBACd,YAAY,EAAE,IAAI,GAAG,CAAC,yBAAiB,CAAC;aACzC,CAAC;QACJ,CAAC;QAED,IAAI,YAAY,CAAC,QAAQ,KAAK,kCAA0B,EAAE,CAAC;YACzD,MAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACpD,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACtC,CAAC,CAAC,KAAK;oBACP,CAAC,CAAC,MAAM,CAAC;YAEb,IAAI,QAAQ,GACV,IAAI,CAAC,YAAY;gBACjB,EAAE,CAAC,YAAY,CAAC,IAAA,2BAAmB,EAAC,YAAY,CAAC,EAAE,OAAO,CAAC,CAAC;YAC9D,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAC9B,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YAClB,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACjD,CAAC;YAED,OAAO,EAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAC,CAAC;QACxD,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,YAAa,CAAC;QACxC,eAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;YACjD,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,YAAY;SAC3B,CAAC;IACJ,CAAC;IAED,yEAAyE;IACzE,0EAA0E;IAC1E,2BAA2B;IACnB,eAAe,CACrB,GAAW,EACX,IAAY,EACZ,EAAC,UAAU,EAAwB;QAEnC,IAAA,eAAM,EAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAElC,MAAM,IAAI,GAAuB,EAAC,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU,EAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,GAAG,EAAC,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;QAEhD,MAAM,iBAAiB,GAAG,CACxB,CAAS,EAC8B,EAAE,CACzC,IAAA,cAAM,EACJ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,EACvD,MAAM,CAAC,EAAE;YACP,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;oBAAE,OAAO,IAAI,CAAC;gBACjD,OAAO,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClC,CAAC;YACD,IACE,UAAU,IAAI,MAAM;gBACpB,MAAM,CAAC,QAAQ;gBACf,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,EACnC,CAAC;gBACD,MAAM,IAAI,KAAK,CACb,0DACG,MAAM,CAAC,QAAe,CAAC,WAAW,CAAC,IACtC,EAAE,CACH,CAAC;YACJ,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CACF,CAAC;QAEJ,OAAO,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED,uEAAuE;IAC/D,cAAc,CACpB,QAA8B,EAC9B,IAAwB,EACxB,GAAW,EACX,IAAY;QAEZ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,OAAQ,QAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3B,uEAAuE;YACvE,MAAM,UAAU,GAAI,QAAgC,CAAC,IAAI,CACvD,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,OAAO,CACR,CAAC;YAEF,IAAI,UAAU,KAAK,SAAS;gBAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QACpD,CAAC,CAA0C,CAAC;IAC9C,CAAC;IAED,6EAA6E;IAC7E,2DAA2D;IACnD,YAAY,CAAC,QAAgB,EAAE,MAA2B;QAChE,MAAM,GAAG,GAAG,IAAI,yBAAiB,GAAG,cAAc,EAAE,GAAG,CAAC;QACxD,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,OAAO,CACL,uBAAuB,qBAAa,GAAG;gBACvC,QAAQ;gBACR,eAAe,qBAAa,aAAa,GAAG,IAAI,CACjD,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CACL,uBAAuB,qBAAa,IAAI;gBACxC,QAAQ;gBACR,cAAc,qBAAa,aAAa,GAAG,GAAG,CAC/C,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AA9RD,sDA8RC"}