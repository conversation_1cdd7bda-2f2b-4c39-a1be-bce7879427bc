"use strict";var d=Object.defineProperty;var V=(n,i,e)=>i in n?d(n,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[i]=e;var o=(n,i,e)=>V(n,typeof i!="symbol"?i+"":i,e);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const c=require("lightweight-charts"),w=require("../primitive-base.cjs.js"),a=require("../pane-view/line.cjs.js"),P=require("es-toolkit"),u=require("../../helpers/assertions.cjs.js"),h={backgroundColor:"#2196f31a",...a.LinePrimitiveOptionsDefault,upPrice:0,lowPrice:0};class p extends w.PrimitivePaneViewBase{_drawBackgroundImpl(i){const e=i.context;e.scale(i.horizontalPixelRatio,i.verticalPixelRatio);const s=e.canvas.width,t=new Path2D,r=u.ensureNotNull(this.priceToCoordinate(this.options.upPrice)),l=u.ensureNotNull(this.priceToCoordinate(this.options.lowPrice));t.moveTo(0,r),t.lineTo(s,r),t.lineTo(s,l),t.lineTo(0,l),t.lineTo(0,r),t.closePath(),e.beginPath(),e.fillStyle=this.options.backgroundColor,e.fill(t)}defaultOptions(){return{backgroundColor:"#2196f31a",upPrice:0,lowPrice:0}}}class g extends w.SeriesPrimitiveBase{constructor(e){super();o(this,"bandPaneView");o(this,"upLinePaneView");o(this,"lowLinePaneView");o(this,"_options");o(this,"upPriceLine",null);o(this,"lowPriceLine",null);this._options=P.merge(P.cloneDeep(h),e),this.bandPaneView=new p(this._options),this.upLinePaneView=new a.LinePrimitivePaneView({...this._options,lineDash:c.LineStyle.LargeDashed}),this.lowLinePaneView=new a.LinePrimitivePaneView({...this._options,lineDash:c.LineStyle.LargeDashed}),this._paneViews=[this.upLinePaneView,this.bandPaneView,this.lowLinePaneView]}_updateAllViews(){const e=this.chart.timeScale().width(),{upPrice:s,lowPrice:t}=this._options;this.upLinePaneView.update([{x:0,price:s},{x:e,price:s}]),this.lowLinePaneView.update([{x:0,price:t},{x:e,price:t}])}}exports.RegionPaneView=p;exports.RegionPrimitive=g;exports.RegionPrimitiveOptionsDefault=h;
//# sourceMappingURL=region.cjs.js.map
