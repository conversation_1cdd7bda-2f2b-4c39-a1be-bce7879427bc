var l = Object.defineProperty;
var p = (r, t, e) => t in r ? l(r, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : r[t] = e;
var a = (r, t, e) => p(r, typeof t != "symbol" ? t + "" : t, e);
import { ChartIndicator as d } from "./abstract-indicator.es.js";
import { BollingerBands as u } from "technicalindicators";
import { SeriesPrimitiveBase as m } from "../custom-primitive/primitive-base.es.js";
import { BandPrimitivePaneView as h } from "../custom-primitive/pane-view/band.es.js";
import { LinePrimitivePaneView as s } from "../custom-primitive/pane-view/line.es.js";
const w = {
  backgroundColor: "#2196f312",
  upperLineColor: "#2196f3",
  middleLineColor: "#ff6d00",
  lowerLineColor: "#2196f3",
  period: 20,
  stdDev: 2,
  overlay: !0
};
class c extends m {
  constructor(e) {
    super();
    a(this, "bandPaneView");
    a(this, "upperPaneView");
    a(this, "middlePaneView");
    a(this, "lowerPaneView");
    this.source = e, this.bandPaneView = new h({
      backgroundColor: this.source.options.backgroundColor
    }), this.upperPaneView = new s({
      lineWidth: 1,
      lineColor: this.source.options.upperLineColor
    }), this.middlePaneView = new s({
      lineWidth: 1,
      lineColor: this.source.options.middleLineColor
    }), this.lowerPaneView = new s({
      lineWidth: 1,
      lineColor: this.source.options.lowerLineColor
    }), this._paneViews = [
      this.bandPaneView,
      this.upperPaneView,
      this.middlePaneView,
      this.lowerPaneView
    ];
  }
  update(e) {
    const o = [];
    for (const i of e) {
      const n = i.value;
      n && o.push({ time: i.time, value: n });
    }
    this.bandPaneView.update(
      o.map((i) => ({
        time: i.time,
        upper: i.value[0],
        lower: i.value[2]
      }))
    ), this.upperPaneView.update(
      o.map((i) => ({
        time: i.time,
        price: i.value[0]
      }))
    ), this.middlePaneView.update(
      o.map((i) => ({
        time: i.time,
        price: i.value[1]
      }))
    ), this.lowerPaneView.update(
      o.map((i) => ({
        time: i.time,
        price: i.value[2]
      }))
    );
  }
}
class g extends d {
  constructor() {
    super(...arguments);
    a(this, "bbPrimitive", new c(this));
  }
  _mainSeriesChanged(e) {
    e.attachPrimitive(this.bbPrimitive);
  }
  _applyOptions(e) {
    (e.period || e.stdDev) && this.applyIndicatorData();
  }
  applyIndicatorData() {
    this.bbPrimitive.update(this._executionContext.data);
  }
  formula(e) {
    const o = e.new_var(e.symbol.close, this.options.period);
    if (!o.calculable()) return;
    const n = new u({
      values: o.getAll(),
      period: this.options.period,
      stdDev: this.options.stdDev
    }).getResult().at(0);
    return [n.upper, n.middle, n.lower];
  }
  remove() {
    var e;
    super.remove(), (e = this.mainSeries) == null || e.detachPrimitive(this.bbPrimitive);
  }
  getDefaultOptions() {
    return w;
  }
}
export {
  c as BBPrimitive,
  g as default,
  w as defaultOptions
};
//# sourceMappingURL=bb-indicator.es.js.map
