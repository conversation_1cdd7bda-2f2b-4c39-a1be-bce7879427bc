"use strict";var v=Object.defineProperty;var P=(t,s,e)=>s in t?v(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e;var h=(t,s,e)=>P(t,typeof s!="symbol"?s+"":s,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const S=require("lightweight-charts"),u=require("./abstract-indicator.cjs.js"),m=require("technicalindicators"),p={fastPeriod:12,slowPeriod:26,signalPeriod:9,SimpleMAOscillator:!1,SimpleMASignal:!1,upColor:u.upColor,downColor:u.downColor,macdLineColor:"#2b97f1",signalLineColor:"#fd6c1c",overlay:!1};class C extends u.ChartIndicator{constructor(e,o,i){super(e,o);h(this,"histogramSeries");h(this,"macdSeries");h(this,"signalSeries");this.histogramSeries=e.addSeries(S.HistogramSeries,{priceLineVisible:!1,priceScaleId:"macd"},i),this.macdSeries=e.addSeries(S.LineSeries,{color:this.options.macdLineColor,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"macd"},i),this.signalSeries=e.addSeries(S.LineSeries,{color:this.options.signalLineColor,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"macd"},i)}applyIndicatorData(){const e=[],o=[],i=[];for(const r of this._executionContext.data){const l=r.value,n=r.time;if(!l)continue;const[c,d,a]=l;isNaN(c)||o.push({time:n,value:c}),isNaN(d)||i.push({time:n,value:d}),isNaN(a)||e.push({time:n,value:a,color:(a??0)>=0?this.options.upColor:this.options.downColor})}this.histogramSeries.setData(e),this.macdSeries.setData(o),this.signalSeries.setData(i)}formula(e){const o=e.new_var(e.symbol.close,this.options.fastPeriod),i=e.new_var(e.symbol.close,this.options.slowPeriod),r=e.new_var(NaN,this.options.signalPeriod);if(!o.calculable()||!i.calculable())return;const l=this.options.SimpleMAOscillator?m.SMA:m.EMA,n=this.options.SimpleMASignal?m.SMA:m.EMA,[c]=new l({period:this.options.fastPeriod,values:o.getAll()}).result,[d]=new l({period:this.options.slowPeriod,values:i.getAll()}).result,a=c-d;if(r.set(a),!r.calculable())return;const[g]=new n({period:this.options.signalPeriod,values:r.getAll()}).result,f=a-g;return[a,g,f]}_applyOptions(e){(e.SimpleMAOscillator||e.SimpleMASignal||e.fastPeriod||e.signalPeriod||e.slowPeriod)&&this.calcIndicatorData(),e.macdLineColor&&this.macdSeries.applyOptions({color:e.macdLineColor}),e.signalLineColor&&this.signalSeries.applyOptions({color:e.signalLineColor}),(e.downColor||e.upColor)&&this.applyIndicatorData()}getDefaultOptions(){return p}remove(){super.remove(),this.chart.removeSeries(this.histogramSeries),this.chart.removeSeries(this.macdSeries),this.chart.removeSeries(this.signalSeries)}setPaneIndex(e){this.histogramSeries.moveToPane(e),this.macdSeries.moveToPane(e),this.signalSeries.moveToPane(e)}getPaneIndex(){return this.histogramSeries.getPane().paneIndex()}}exports.default=C;exports.defaultOptions=p;
//# sourceMappingURL=macd-indicator.cjs.js.map
