import { ISeriesApi, Nominal, SeriesType, SingleValueData, WhitespaceData } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { SeriesPrimitiveBase } from '../custom-primitive/primitive-base';
import { LinePrimitivePaneView } from '../custom-primitive/pane-view/line';
import { Context, IIndicatorBar } from '../helpers/execution-indicator';

export interface SMAIndicatorOptions extends ChartIndicatorOptions {
    color: string;
    period: number;
}
export declare const defaultOptions: SMAIndicatorOptions;
export declare class SMAPrimitive extends SeriesPrimitiveBase<SingleValueData | WhitespaceData> {
    protected source: SMAIndicator;
    linePrimitive: LinePrimitivePaneView;
    constructor(source: SMAIndicator);
    update(indicatorBars: IIndicatorBar<SMAData>[]): void;
}
export type SMAData = readonly [Nominal<number, 'SMA'>];
export default class SMAIndicator extends ChartIndicator<SMAIndicatorOptions, SMAData> {
    smaPrimitive: SMAPrimitive;
    getDefaultOptions(): SMAIndicatorOptions;
    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void;
    remove(): void;
    applyIndicatorData(): void;
    formula(c: Context): any;
}
