{"version": 3, "file": "DisplayTimezone.es.js", "sources": ["../../src/advance-chart/DisplayTimezone.ts"], "sourcesContent": ["import { Delegate } from '../helpers/delegate';\r\nimport { IAdvanceChart, Period } from './i-advance-chart';\r\nimport {TickMarkType} from 'lightweight-charts';\r\nimport dayjs from '../helpers/dayjs-setup'\r\nexport class DisplayTimezone {\r\n  _timezoneChanged = new Delegate<string>();\r\n  _timezone: string\r\n  _browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone\r\n  constructor(\r\n    private _source: IAdvanceChart\r\n  ) {\r\n    this._timezone = this._source.options.tzDisplay\r\n\r\n    // const now = dayjs(new Date).tz(this._timezone).format('YYYY-MM-DD HH:mm:ss Z');\r\n    // console.log('timezone display now: ' + now)\r\n  }\r\n\r\n  formatDateTime(date: Date) {\r\n    date = this.convertDateToTimezoneDate(date);\r\n    return new Intl.DateTimeFormat(this._source.options.locale, {\r\n      dateStyle: 'medium',\r\n      timeStyle: 'medium',\r\n      hourCycle: 'h23', \r\n    }).format(date);\r\n  }\r\n  formatDate(date: Date) {\r\n    date = this.convertDateToTimezoneDate(date);\r\n    return new Intl.DateTimeFormat(this._source.options.locale, {\r\n      dateStyle: 'medium',\r\n    }).format(date);\r\n  }\r\n\r\n  convertDateToTimezoneDate(date: Date) {\r\n    if(this._timezone === this._browserTimezone) return date;\r\n    return dayjs.tz(date, this._timezone).tz(this._browserTimezone, true).toDate()\r\n  }\r\n\r\n  format(date: Date) {\r\n    switch(this._source.dataInterval.period) {\r\n      case Period.day:\r\n      case Period.month:\r\n      case Period.week:\r\n        return this.formatDate(date);\r\n      default:\r\n        return this.formatDateTime(date)\r\n    }\r\n  }\r\n\r\n  tickMarkFormatter(date: Date, tickMarkType: TickMarkType) {\r\n    date = this.convertDateToTimezoneDate(date);\r\n    const formatOptions: Intl.DateTimeFormatOptions = {};\r\n\r\n    switch (tickMarkType) {\r\n      case TickMarkType.Year:\r\n        formatOptions.year = 'numeric';\r\n        break;\r\n\r\n      case TickMarkType.Month:\r\n        formatOptions.month = 'short';\r\n        break;\r\n\r\n      case TickMarkType.DayOfMonth:\r\n        formatOptions.day = 'numeric';\r\n        break;\r\n\r\n      case TickMarkType.Time:\r\n        formatOptions.hour12 = false;\r\n        formatOptions.hour = '2-digit';\r\n        formatOptions.minute = '2-digit';\r\n        break;\r\n\r\n      case TickMarkType.TimeWithSeconds:\r\n        formatOptions.hour12 = false;\r\n        formatOptions.hour = '2-digit';\r\n        formatOptions.minute = '2-digit';\r\n        formatOptions.second = '2-digit';\r\n        break;\r\n    }\r\n\r\n    return date.toLocaleString(this._source.options.locale, formatOptions)\r\n  }\r\n}\r\n"], "names": ["DisplayTimezone", "_source", "__publicField", "Delegate", "date", "dayjs", "Period", "tickMarkType", "formatOptions", "TickMarkType"], "mappings": ";;;;;;;;AAIO,MAAMA,EAAgB;AAAA,EAI3B,YACUC,GACR;AALF,IAAAC,EAAA,0BAAmB,IAAIC,EAAiB;AACxC,IAAAD,EAAA;AACA,IAAAA,EAAA,0BAAmB,KAAK,iBAAiB,gBAAkB,EAAA;AAEjD,SAAA,UAAAD,GAEH,KAAA,YAAY,KAAK,QAAQ,QAAQ;AAAA,EAAA;AAAA,EAMxC,eAAeG,GAAY;AAClB,WAAAA,IAAA,KAAK,0BAA0BA,CAAI,GACnC,IAAI,KAAK,eAAe,KAAK,QAAQ,QAAQ,QAAQ;AAAA,MAC1D,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,IAAA,CACZ,EAAE,OAAOA,CAAI;AAAA,EAAA;AAAA,EAEhB,WAAWA,GAAY;AACd,WAAAA,IAAA,KAAK,0BAA0BA,CAAI,GACnC,IAAI,KAAK,eAAe,KAAK,QAAQ,QAAQ,QAAQ;AAAA,MAC1D,WAAW;AAAA,IAAA,CACZ,EAAE,OAAOA,CAAI;AAAA,EAAA;AAAA,EAGhB,0BAA0BA,GAAY;AACpC,WAAG,KAAK,cAAc,KAAK,mBAAyBA,IAC7CC,EAAM,GAAGD,GAAM,KAAK,SAAS,EAAE,GAAG,KAAK,kBAAkB,EAAI,EAAE,OAAO;AAAA,EAAA;AAAA,EAG/E,OAAOA,GAAY;AACV,YAAA,KAAK,QAAQ,aAAa,QAAQ;AAAA,MACvC,KAAKE,EAAO;AAAA,MACZ,KAAKA,EAAO;AAAA,MACZ,KAAKA,EAAO;AACH,eAAA,KAAK,WAAWF,CAAI;AAAA,MAC7B;AACS,eAAA,KAAK,eAAeA,CAAI;AAAA,IAAA;AAAA,EACnC;AAAA,EAGF,kBAAkBA,GAAYG,GAA4B;AACjD,IAAAH,IAAA,KAAK,0BAA0BA,CAAI;AAC1C,UAAMI,IAA4C,CAAC;AAEnD,YAAQD,GAAc;AAAA,MACpB,KAAKE,EAAa;AAChB,QAAAD,EAAc,OAAO;AACrB;AAAA,MAEF,KAAKC,EAAa;AAChB,QAAAD,EAAc,QAAQ;AACtB;AAAA,MAEF,KAAKC,EAAa;AAChB,QAAAD,EAAc,MAAM;AACpB;AAAA,MAEF,KAAKC,EAAa;AAChB,QAAAD,EAAc,SAAS,IACvBA,EAAc,OAAO,WACrBA,EAAc,SAAS;AACvB;AAAA,MAEF,KAAKC,EAAa;AAChB,QAAAD,EAAc,SAAS,IACvBA,EAAc,OAAO,WACrBA,EAAc,SAAS,WACvBA,EAAc,SAAS;AACvB;AAAA,IAAA;AAGJ,WAAOJ,EAAK,eAAe,KAAK,QAAQ,QAAQ,QAAQI,CAAa;AAAA,EAAA;AAEzE;"}