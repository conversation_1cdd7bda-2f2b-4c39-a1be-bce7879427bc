{"version": 3, "file": "volume-indicator.es.js", "sources": ["../../src/indicators/volume-indicator.ts"], "sourcesContent": ["import {\r\n  ISeriesApi,\r\n  SeriesType,\r\n  HistogramSeries,\r\n  IChartApi,\r\n  Nominal,\r\n  Time,\r\n} from 'lightweight-charts';\r\nimport { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';\r\nimport { Color } from '../helpers/color';\r\nimport {Context} from '../helpers/execution-indicator';\r\nimport {ensureDefined} from '../helpers/assertions';\r\n\r\nexport interface VolumeIndicatorOptions extends ChartIndicatorOptions {\r\n  upColor: string;\r\n  downColor: string;\r\n}\r\n\r\nexport const defaultOptions: VolumeIndicatorOptions = {\r\n  upColor: '#26a69a',\r\n  downColor: '#ef5350',\r\n  overlay: false,\r\n};\r\n\r\nexport type VolumePoint = Nominal<number, 'VolumePoint'>\r\nexport type VolumePositive = Nominal<0 | 1, 'Positive'>\r\n\r\nexport type VolumeIndicatorData = [VolumePoint, VolumePositive]\r\n\r\nexport default class VolumeIndicator extends ChartIndicator<\r\n  VolumeIndicatorOptions,\r\n  VolumeIndicatorData\r\n> {\r\n  volumeSeries: ISeriesApi<SeriesType>;\r\n\r\n  constructor(\r\n    protected chart: IChartApi,\r\n    options?: Partial<VolumeIndicatorOptions>,\r\n    paneIndex?: number\r\n  ) {\r\n    super(chart, options);\r\n    const numberFormatter = this.options.numberFormatter;\r\n\r\n    this.volumeSeries = this.chart.addSeries(\r\n      HistogramSeries,\r\n      {\r\n        priceLineVisible: false,\r\n        priceFormat: numberFormatter\r\n          ? {\r\n              type: 'custom',\r\n              formatter: (volume: number) => numberFormatter().volume(volume),\r\n            }\r\n          : { type: 'volume' },\r\n        priceScaleId: 'volume',\r\n      },\r\n      this.options.overlay ? 0 : paneIndex\r\n    );\r\n\r\n    this.applyPriceScaleMargins()\r\n  }\r\n\r\n  applyPriceScaleMargins() {\r\n    if (this.options.overlay) {\r\n      this.volumeSeries.priceScale().applyOptions({\r\n        scaleMargins: {\r\n          top: 0.7,\r\n          bottom: 0,\r\n        },\r\n      });\r\n    } else {\r\n      this.volumeSeries.priceScale().applyOptions({\r\n        scaleMargins: {\r\n          top: 0.1,\r\n          bottom: 0,\r\n        },\r\n      });\r\n    }\r\n  }\r\n  _applyOptions(options: Partial<VolumeIndicatorOptions>): void {\r\n    if (options.downColor || options.upColor) {\r\n      this.applyIndicatorData();\r\n    }\r\n  }\r\n\r\n  applyIndicatorData(): void {\r\n    const volumeData = this._executionContext.data.filter(item => item.value)\r\n    this.volumeSeries.setData(\r\n      volumeData.map((item) => ({\r\n        time: item.time as Time,\r\n        value: ensureDefined(item.value)[0],\r\n        color: Color.applyAlpha(\r\n          ensureDefined(item.value)[1] === 1\r\n            ? this.options.upColor\r\n            : this.options.downColor,\r\n          this.options.overlay ? 0.6 : 1\r\n        ),\r\n      }))\r\n    );\r\n  }\r\n\r\n  formula(c: Context): VolumeIndicatorData | undefined {\r\n    const closeSeries = c.new_var(c.symbol.close, 2);\r\n    if(!closeSeries.calculable()) return;\r\n    const positive = closeSeries.get(0) > closeSeries.get(1) ? 1 : 0\r\n    return [c.symbol.volume as VolumePoint, positive as VolumePositive]\r\n  }\r\n\r\n  getDefaultOptions(): VolumeIndicatorOptions {\r\n    return defaultOptions;\r\n  }\r\n\r\n  remove(): void {\r\n    super.remove();\r\n    this.chart.removeSeries(this.volumeSeries);\r\n  }\r\n\r\n  setPaneIndex(paneIndex: number): void {\r\n    this.volumeSeries.moveToPane(paneIndex);\r\n    this.applyPriceScaleMargins()\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.volumeSeries.getPane().paneIndex();\r\n  }\r\n}\r\n"], "names": ["defaultOptions", "VolumeIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "numberF<PERSON>atter", "HistogramSeries", "volume", "volumeData", "item", "ensureDefined", "Color", "c", "closeSeries", "positive"], "mappings": ";;;;;;;AAkBO,MAAMA,IAAyC;AAAA,EACpD,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AACX;AAOA,MAAqBC,UAAwBC,EAG3C;AAAA,EAGA,YACYC,GACVC,GACAC,GACA;AACA,UAAMF,GAAOC,CAAO;AAPtB,IAAAE,EAAA;AAGY,SAAA,QAAAH;AAKJ,UAAAI,IAAkB,KAAK,QAAQ;AAEhC,SAAA,eAAe,KAAK,MAAM;AAAA,MAC7BC;AAAA,MACA;AAAA,QACE,kBAAkB;AAAA,QAClB,aAAaD,IACT;AAAA,UACE,MAAM;AAAA,UACN,WAAW,CAACE,MAAmBF,EAAgB,EAAE,OAAOE,CAAM;AAAA,QAAA,IAEhE,EAAE,MAAM,SAAS;AAAA,QACrB,cAAc;AAAA,MAChB;AAAA,MACA,KAAK,QAAQ,UAAU,IAAIJ;AAAA,IAC7B,GAEA,KAAK,uBAAuB;AAAA,EAAA;AAAA,EAG9B,yBAAyB;AACnB,IAAA,KAAK,QAAQ,UACV,KAAA,aAAa,WAAW,EAAE,aAAa;AAAA,MAC1C,cAAc;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,MAAA;AAAA,IACV,CACD,IAEI,KAAA,aAAa,WAAW,EAAE,aAAa;AAAA,MAC1C,cAAc;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,MAAA;AAAA,IACV,CACD;AAAA,EACH;AAAA,EAEF,cAAcD,GAAgD;AACxD,KAAAA,EAAQ,aAAaA,EAAQ,YAC/B,KAAK,mBAAmB;AAAA,EAC1B;AAAA,EAGF,qBAA2B;AACzB,UAAMM,IAAa,KAAK,kBAAkB,KAAK,OAAO,CAAAC,MAAQA,EAAK,KAAK;AACxE,SAAK,aAAa;AAAA,MAChBD,EAAW,IAAI,CAACC,OAAU;AAAA,QACxB,MAAMA,EAAK;AAAA,QACX,OAAOC,EAAcD,EAAK,KAAK,EAAE,CAAC;AAAA,QAClC,OAAOE,EAAM;AAAA,UACXD,EAAcD,EAAK,KAAK,EAAE,CAAC,MAAM,IAC7B,KAAK,QAAQ,UACb,KAAK,QAAQ;AAAA,UACjB,KAAK,QAAQ,UAAU,MAAM;AAAA,QAAA;AAAA,MAC/B,EACA;AAAA,IACJ;AAAA,EAAA;AAAA,EAGF,QAAQG,GAA6C;AACnD,UAAMC,IAAcD,EAAE,QAAQA,EAAE,OAAO,OAAO,CAAC;AAC5C,QAAA,CAACC,EAAY,aAAc;AACxB,UAAAC,IAAWD,EAAY,IAAI,CAAC,IAAIA,EAAY,IAAI,CAAC,IAAI,IAAI;AAC/D,WAAO,CAACD,EAAE,OAAO,QAAuBE,CAA0B;AAAA,EAAA;AAAA,EAGpE,oBAA4C;AACnC,WAAAhB;AAAA,EAAA;AAAA,EAGT,SAAe;AACb,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,YAAY;AAAA,EAAA;AAAA,EAG3C,aAAaK,GAAyB;AAC/B,SAAA,aAAa,WAAWA,CAAS,GACtC,KAAK,uBAAuB;AAAA,EAAA;AAAA,EAG9B,eAAuB;AACrB,WAAO,KAAK,aAAa,QAAQ,EAAE,UAAU;AAAA,EAAA;AAEjD;"}