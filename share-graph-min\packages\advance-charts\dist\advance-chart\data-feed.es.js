var m = Object.defineProperty;
var g = (i, t, a) => t in i ? m(i, t, { enumerable: !0, configurable: !0, writable: !0, value: a }) : i[t] = a;
var h = (i, t, a) => g(i, typeof t != "symbol" ? t + "" : t, a);
import { timeToDayjs as o, timeToDate as p, dayjsToTime as u } from "../helpers/utils.es.js";
import { mergeOhlcData as v } from "../helpers/mergeData.es.js";
import { Delegate as w } from "../helpers/delegate.es.js";
import { Period as s } from "./i-advance-chart.es.js";
import { groupBy as D } from "es-toolkit";
import "../helpers/dayjs-setup.es.js";
import { timeKey as f } from "./time-key.es.js";
import { log as C } from "../helpers/log.es.js";
import l from "dayjs";
function S(i, t) {
  switch (t) {
    case s.minute:
    case s.hour:
    case s.day:
      return i;
    case s.week:
      return l.tz(o(i), "UTC").startOf("week").unix();
    case s.month:
      return l.tz(o(i), "UTC").startOf("month").unix();
    default:
      throw new Error(`Period : ${t} not support`);
  }
}
const y = (i) => {
  const t = Math.max(...i.map((n) => n.high)), a = Math.min(...i.map((n) => n.low)), e = i.reduce((n, c) => n + c.volume, 0), r = i[0].open, d = i[i.length - 1].close;
  return {
    time: i[0].time,
    open: r,
    high: t,
    low: a,
    close: d,
    volume: e
  };
};
class F {
  constructor(t, a) {
    h(this, "_loading", !1);
    h(this, "_data", []);
    h(this, "_dataChanged", new w());
    h(this, "interval", { period: s.day, times: 1 });
    h(this, "initialData", !1);
    h(this, "endOfData", !1);
    h(this, "_refeshTimer", null);
    h(this, "_destroyed", !1);
    this.advanceChart = t, this.dataFetch = a, this.onVisibleLogicalRangeChange = this.onVisibleLogicalRangeChange.bind(this), this.advanceChart.chartApi.timeScale().subscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange), this.dataFetch.refeshTime && (this._refeshTimer = setInterval(async () => this.updateData(), this.dataFetch.refeshTime));
  }
  get data() {
    return this._data;
  }
  set data(t) {
    this._data = t, this._dataChanged.fire();
  }
  isNeedPaging(t) {
    if (!this.initialData || this.data.length === 0 || this.endOfData) return !1;
    const { from: a } = t;
    return !(a > 30);
  }
  groupData() {
    const t = this.interval;
    if (t.period === s.day) return this._data;
    if (t.period === s.hour) return this._data;
    if (t.period === s.minute) return this._data;
    const a = D(this._data, (e) => f(e.time, t));
    return Object.entries(a).map(([e, r]) => [parseInt(e), y(r)]).sort((e, r) => e[0] - r[0]).map((e) => e[1]);
  }
  processNewData(t) {
    const a = v(t, this.data);
    if (a.length !== this.data.length)
      return this.data = a, this.advanceChart.setData(this.groupData(), this.interval), a;
  }
  async updateData() {
    const a = this.data[this.data.length - 1].time, e = await this.dataFetch.fetchUpdateData({
      interval: this.interval,
      from: p(a),
      to: /* @__PURE__ */ new Date()
    }, { forward: this.forward.bind(this) });
    this._destroyed || this.processNewData(e);
  }
  async pagingData(t) {
    const { from: a } = t, e = this.data[0].time, r = o(e), d = this.forward(e, a - 200), n = await this.dataFetch.fetchPaginationData({
      interval: this.interval,
      from: d.toDate(),
      to: r.toDate()
    }, { forward: this.forward.bind(this) });
    this._destroyed || this.processNewData(n) || (this.endOfData = !0);
  }
  async onVisibleLogicalRangeChange(t) {
    if (!t) return;
    const a = (e, r) => e.from === r.from && e.to === r.to;
    if (!this.advanceChart.loading) {
      this.advanceChart.loading = !0;
      try {
        for (; this.isNeedPaging(t); ) {
          await this.pagingData(t);
          const e = this.advanceChart.chartApi.timeScale().getVisibleLogicalRange();
          if (!e || a(e, t)) break;
          t = e;
        }
      } finally {
        this.advanceChart.loading = !1;
      }
    }
  }
  forward(t, a) {
    a = Math.round(a);
    const e = this.interval.period;
    switch (e) {
      case s.minute:
        return o(t).add(a, "minute");
      case s.hour:
        return o(t).add(a, "hour");
      case s.day:
        return o(t).add(a, "day");
      case s.week:
        return o(t).add(a, "week");
      case s.month:
        return o(t).add(a, "month");
      default:
        throw new Error(`Period : ${e} not support`);
    }
  }
  async setRange({ from: t, to: a, interval: e }) {
    this.resetState(), this.advanceChart.loading = !0, this.interval = e;
    const r = await this.dataFetch.fetchInitialData({ from: t, to: a, interval: e }, { forward: this.forward.bind(this) });
    if (this.data = r, this.advanceChart.loading = !1, this._destroyed) return;
    this.advanceChart.setData(this.groupData(), e), this.initialData = !0;
    const d = this.advanceChart.chartApi.timeScale(), n = d.timeToIndex(u(l(t)), !0), c = d.timeToIndex(u(l(a)), !0);
    n !== void 0 && c !== void 0 && this.advanceChart.fitRange({ from: n, to: c }), await this.onVisibleLogicalRangeChange(
      d.getVisibleLogicalRange()
    );
  }
  resetState() {
    this.advanceChart.loading = !1, this.initialData = !1, this.endOfData = !1;
  }
  dataChanged() {
    return this._dataChanged;
  }
  trade(t) {
    const [a] = this.advanceChart.lastPoint(), e = f(a.time, this.interval), r = f(t.time, this.interval);
    if (r < e) {
      C.warn(`Trade timestamp ${r} is older than current ${e}`);
      return;
    }
    e === r ? this.advanceChart.update({
      open: a.open,
      high: Math.max(a.high, t.price),
      low: Math.min(a.low, t.price),
      close: t.price,
      volume: a.volume + t.volume,
      time: t.time
    }, !0) : this.advanceChart.update({
      open: t.price,
      high: t.price,
      low: t.price,
      close: t.price,
      volume: t.volume,
      time: t.time
    });
  }
  destroy() {
    this._dataChanged.destroy(), this.advanceChart.chartApi.timeScale().unsubscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange), this._refeshTimer && (clearInterval(this._refeshTimer), this._refeshTimer = null);
  }
}
export {
  F as DataFeed,
  y as aggregate,
  S as roundTime
};
//# sourceMappingURL=data-feed.es.js.map
