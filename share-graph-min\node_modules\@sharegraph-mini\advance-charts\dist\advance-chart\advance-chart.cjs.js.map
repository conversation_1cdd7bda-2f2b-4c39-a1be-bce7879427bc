{"version": 3, "file": "advance-chart.cjs.js", "sources": ["../../src/advance-chart/advance-chart.ts"], "sourcesContent": ["import {\r\n  AreaSeries,\r\n  BarSeries,\r\n  BaselineSeries,\r\n  CandlestickSeries,\r\n  createChart,\r\n  DeepPartial,\r\n  IChartApi,\r\n  ISeriesApi,\r\n  LastPriceAnimationMode,\r\n  LineSeries,\r\n  Logical,\r\n  PriceScaleMode,\r\n  SeriesType,\r\n  TickMarkFormatter,\r\n  Time,\r\n} from 'lightweight-charts';\r\nimport {\r\n  ChartIndicator,\r\n  downColor,\r\n  upColor,\r\n} from '../indicators/abstract-indicator';\r\nimport { OHLCVExtraData, OHLCVSimple } from '../interface';\r\nimport { Delegate, IPublicDelegate, ISubscription } from '../helpers/delegate';\r\nimport { IndicatorFactory, VolumeIndicator } from '../indicators';\r\nimport { binarySearchIndex, timeToDate, timeToUnix } from '../helpers/utils';\r\nimport { cloneDeep, merge } from 'es-toolkit';\r\nimport {NumberFormatterFactory} from '../helpers/number-formatter';\r\nimport {VolumeIndicatorOptions} from '../indicators/volume-indicator';\r\nimport {IAdvanceChart, IAdvanceChartOptions, IAdvanceChartType, IGroupIndicatorByPane, Interval, Period} from './i-advance-chart';\r\nimport {DisplayTimezone} from './DisplayTimezone';\r\n\r\n\r\nexport const defaultAdvanceChartOptions: IAdvanceChartOptions = {\r\n  upColor,\r\n  downColor,\r\n  mainColor: '#3594e7',\r\n  highLowLineVisible: false,\r\n  highLineColor: upColor,\r\n  lowLineColor: downColor,\r\n  priceScaleMode: PriceScaleMode.Normal,\r\n  priceLineVisible: false,\r\n  locale: 'en',\r\n  gridColor: '#f2f2f2',\r\n  axesColor: '#333',\r\n  tzDisplay: Intl.DateTimeFormat().resolvedOptions().timeZone,\r\n  height: 500\r\n};\r\n\r\n\r\nexport class AdvanceChart implements IAdvanceChart {\r\n  options: IAdvanceChartOptions;\r\n  chartApi: IChartApi;\r\n  chartType: IAdvanceChartType | null = null;\r\n  mainSeries: ISeriesApi<SeriesType> | null = null;\r\n  dataInterval: Interval = {\r\n    period: Period.day,\r\n    times: 1\r\n  };\r\n  __destroyed = false\r\n  \r\n  private data: OHLCVExtraData[] = [];\r\n  // private dataSet: OHLCVExtraData[] = [];\r\n  private indicators = new Map<string, ChartIndicator>();\r\n  private _volumeType: 'volume' | 'volume_overlay' | undefined = undefined;\r\n  // private _displayTimezone = new DisplayTimezoneUtils()\r\n  \r\n\r\n  private _chartTypeChanged = new Delegate();\r\n  private _indicatorChanged = new Delegate<string, 'add' | 'remove'>();\r\n  private _destroyed = new Delegate();\r\n  private _updated = new Delegate();\r\n  private _crosshairMoved = new Delegate<OHLCVExtraData, OHLCVExtraData>();\r\n  private _chartHovered = new Delegate<boolean>();\r\n  private _dataSetChanged = new Delegate<OHLCVExtraData[]>();\r\n  private _loading = new Delegate<boolean>();\r\n  private _optionChanged = new Delegate();\r\n  private _mainSeriesChanged = new Delegate();\r\n\r\n  private _displayTimezone: DisplayTimezone\r\n\r\n  constructor(\r\n    container: HTMLElement,\r\n    options?: DeepPartial<IAdvanceChartOptions>\r\n  ) {\r\n    this.options = Object.freeze(merge(cloneDeep(defaultAdvanceChartOptions), options ?? {}));\r\n    this.chartApi = createChart(container, {\r\n      layout: {\r\n        attributionLogo: false,\r\n        panes: {\r\n          separatorColor: '#e0e3eb',\r\n          enableResize: false,\r\n        },\r\n        fontSize: this.options.fontSize,\r\n        fontFamily: this.options.fontFamily,\r\n        textColor: this.options.axesColor,\r\n      },\r\n      autoSize: true,\r\n      height: this.options.height,\r\n      localization: {\r\n        locale: this.options.locale,\r\n        percentageFormatter: (percentageValue: number) => this.numberFormatter.percent(percentageValue / 100),\r\n        // priceFormatter: (price) => this.numberFormatter.decimal(price),\r\n        timeFormatter: (time: Time) => this._displayTimezone.format(timeToDate(time))\r\n      },\r\n      timeScale: {\r\n        borderVisible: false,\r\n        rightOffset: 10,\r\n        maxBarSpacing: 40,\r\n        minBarSpacing: 4,\r\n        secondsVisible: true,\r\n        timeVisible: true,\r\n        tickMarkFormatter: ((timePoint, tickMarkType) => this._displayTimezone.tickMarkFormatter(timeToDate(timePoint), tickMarkType)) as TickMarkFormatter\r\n      },\r\n      overlayPriceScales: {\r\n        scaleMargins: {\r\n          bottom: 0.05,\r\n          top: 0.05\r\n        }\r\n      },\r\n      leftPriceScale: {\r\n        borderVisible: false,\r\n      },\r\n      handleScale: {\r\n        axisPressedMouseMove: false,\r\n      },\r\n      rightPriceScale: {\r\n        borderVisible: false,\r\n        mode: this.options.priceScaleMode,\r\n      },\r\n\r\n      grid: {\r\n        horzLines: {\r\n          visible: false,\r\n        },\r\n        vertLines: {\r\n          color: this.options.gridColor,\r\n        },\r\n      },\r\n    });\r\n\r\n    this.chartApi.subscribeCrosshairMove((param) => {\r\n      if (param.time === undefined) return;\r\n      const mainSeries = this.mainSeries;\r\n      if (!mainSeries) return;\r\n\r\n      const index = binarySearchIndex(\r\n        this.data,\r\n        timeToUnix(param.time),\r\n        (item) => timeToUnix(item.time)\r\n      );\r\n      if (index === -1) return;\r\n\r\n      const [data, prev] = this.getPointFromIndex(index);\r\n\r\n      this._crosshairMoved.fire(data, prev);\r\n    });\r\n\r\n    this.chartApi.subscribeCrosshairMove((param) => {\r\n      if (param.logical === undefined) return this._chartHovered.fire(false);\r\n      this._chartHovered.fire(true);\r\n    });\r\n\r\n    this.chartApi.timeScale().subscribeVisibleTimeRangeChange((param) => {\r\n      if (!param) return this._dataSetChanged.fire([]);\r\n      // this.dataSet = this.getDataSet(param);\r\n      this._dataSetChanged.fire(this.dataSet);\r\n    });\r\n\r\n    this._dataSetChanged.subscribe(() => {\r\n      this.tryDrawUpDownLine();\r\n      this.updateBaselineChartType()\r\n    });\r\n\r\n    this._displayTimezone = new DisplayTimezone(this)\r\n  }\r\n\r\n  get numberFormatter () {\r\n    return NumberFormatterFactory.formatter(this.options.locale ?? 'en')\r\n  }\r\n\r\n  get dataSet () {\r\n    const range = this.chartApi.timeScale().getVisibleRange();\r\n    if(!range) return [];\r\n    const { from, to } = range;\r\n    const fromIndex = binarySearchIndex(this.data, timeToUnix(from), (item) => timeToUnix(item.time));\r\n    const toIndex = binarySearchIndex(this.data, timeToUnix(to), (item) => timeToUnix(item.time));\r\n    return this.data.slice(fromIndex, toIndex + 1);\r\n  }\r\n\r\n  getData() {\r\n    return this.data;\r\n  }\r\n\r\n  getIndicators() {\r\n    return Array.from(this.indicators.values());\r\n  }\r\n\r\n  getPointFromIndex(index: number) {\r\n    const current = this.data[index];\r\n    const prev = this.data[index > 0 ? index - 1 : index];\r\n\r\n    return [current, prev] as const;\r\n  }\r\n\r\n  lastPoint() {\r\n    return this.getPointFromIndex(this.data.length - 1);\r\n  }\r\n\r\n  setChartType(type: IAdvanceChartType) {\r\n    if (type === this.chartType) return;\r\n    let mainSeries: ISeriesApi<SeriesType>;\r\n\r\n    switch (type) {\r\n      case 'line':\r\n        mainSeries = this.chartApi.addSeries(\r\n          LineSeries,\r\n          { \r\n            color: this.options.mainColor, \r\n            priceLineVisible: this.options.priceLineVisible,\r\n            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,\r\n            lineWidth: 2\r\n          },\r\n          0\r\n        );\r\n        break;\r\n      case 'candle':\r\n        mainSeries = this.chartApi.addSeries(\r\n          CandlestickSeries,\r\n          { upColor: this.options.upColor, downColor: this.options.downColor, priceLineVisible: this.options.priceLineVisible, },\r\n          0\r\n        );\r\n        break;\r\n      case 'mountain':\r\n        mainSeries = this.chartApi.addSeries(\r\n          AreaSeries,\r\n          {\r\n            topColor: this.options.mainColor,\r\n            lineColor: this.options.mainColor,\r\n            bottomColor: '#ffffff00',\r\n            priceLineVisible: this.options.priceLineVisible,\r\n            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,\r\n            lineWidth: 2\r\n          },\r\n          0\r\n        );\r\n        break;\r\n      case 'bar':\r\n        mainSeries = this.chartApi.addSeries(\r\n          BarSeries,\r\n          { upColor: this.options.upColor, downColor: this.options.downColor, priceLineVisible: this.options.priceLineVisible },\r\n          0\r\n        );\r\n        break;\r\n      case 'baseline':\r\n        mainSeries = this.chartApi.addSeries(\r\n          BaselineSeries,\r\n          {\r\n            topLineColor: this.options.upColor,\r\n            bottomLineColor: this.options.downColor,\r\n            bottomFillColor1: 'transparent',\r\n            bottomFillColor2: 'transparent',\r\n            topFillColor1: 'transparent',\r\n            topFillColor2: 'transparent',\r\n            priceLineVisible: this.options.priceLineVisible,\r\n            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,\r\n            lineWidth: 2\r\n          },\r\n          0\r\n        );\r\n        break;\r\n      case 'base-mountain':\r\n        mainSeries = this.chartApi.addSeries(\r\n          BaselineSeries,\r\n          {\r\n            topLineColor: this.options.upColor,\r\n            bottomLineColor: this.options.downColor,\r\n            priceLineVisible: this.options.priceLineVisible,\r\n            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,\r\n            lineWidth: 2\r\n          },\r\n          0\r\n        );\r\n        break;\r\n      default:\r\n        throw new Error('Invalid chart type');\r\n    }\r\n\r\n    if (this.mainSeries) {\r\n      this.chartApi.removeSeries(this.mainSeries);\r\n    }\r\n    this.chartType = type;\r\n    this.mainSeries = mainSeries;\r\n    this.updateBaselineChartType()\r\n    this.applyMainSeriesData();\r\n    this.tryDrawUpDownLine()\r\n    Array.from(this.indicators.values()).forEach(indicator => indicator.mainSeriesChanged?.(mainSeries))\r\n    this._updated.fire();\r\n    this._chartTypeChanged.fire();\r\n  }\r\n\r\n  updateBaselineChartType(){\r\n    if (this.mainSeries && (this.chartType === 'baseline' || this.chartType === 'base-mountain')) {\r\n      const baseValue = this.dataSet.at(0)?.close;\r\n      if (baseValue) {\r\n        (this.mainSeries as unknown as ISeriesApi<'Baseline'>).applyOptions({\r\n          baseValue: { price: baseValue },\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  setData(data: OHLCVSimple[], interval: Interval) {\r\n    this.dataInterval = interval\r\n    this.data = data.map((item) => ({ ...item, value: item.close, customValues: item }));\r\n    this.applyData();\r\n    const range = this.chartApi.timeScale().getVisibleRange();\r\n    if (range) {\r\n      this._dataSetChanged.fire(this.dataSet);\r\n    }\r\n    this._updated.fire();\r\n  }\r\n\r\n  applyMainSeriesData() {\r\n    if (!this.mainSeries) return;\r\n    this.mainSeries.setData(this.data as unknown as OHLCVSimple[]);\r\n  }\r\n  applyData() {\r\n    this.applyMainSeriesData();\r\n    Array.from(this.indicators.values()).map((instance) =>\r\n      instance.setData(this.data)\r\n    );\r\n  }\r\n\r\n  update(bar: OHLCVSimple, replaceLastPoint: boolean = false) {\r\n    const [lastPoint] = this.lastPoint()\r\n    const prepareBar = {\r\n      ...bar,\r\n      value: bar.close,\r\n      customValues: bar,\r\n      time: replaceLastPoint ? lastPoint.time : bar.time\r\n    }\r\n\r\n    if(replaceLastPoint) {\r\n      this.data.splice(this.data.length - 1, 1, prepareBar)\r\n    } else {\r\n      this.data.push(prepareBar)\r\n\r\n      const range = this.chartApi.timeScale().getVisibleLogicalRange();\r\n\r\n      // keep current range when add new tick.\r\n      if(range && (Math.floor(range.to) > this.data.length)) {\r\n        const newRange = {from: range.from, to: range.to}\r\n        this.chartApi.timeScale().setVisibleLogicalRange(newRange)\r\n      }\r\n    }\r\n\r\n    this.mainSeries?.update(prepareBar as unknown as OHLCVSimple)\r\n    Array.from(this.indicators.values()).map((instance) => instance.update());\r\n\r\n    this.tryDrawUpDownLine()\r\n    this._updated.fire();\r\n  }\r\n\r\n  tryDrawUpDownLine() {\r\n    if (!this.mainSeries) return;\r\n    if (!this.options.highLowLineVisible) {\r\n      this.mainSeries.priceLines().forEach(line => this.mainSeries?.removePriceLine(line))\r\n      return;\r\n    }\r\n    const dataSet = this.dataSet;\r\n    const low = dataSet.reduce(\r\n      (acc, item) => Math.min(acc, item.close),\r\n      Number.MAX_SAFE_INTEGER\r\n    );\r\n    const high = dataSet.reduce(\r\n      (acc, item) => Math.max(acc, item.close),\r\n      Number.MIN_SAFE_INTEGER\r\n    );\r\n    const [lowPriceLine, highPriceLine] = this.mainSeries?.priceLines() ?? [];\r\n\r\n    if (lowPriceLine) {\r\n      lowPriceLine.applyOptions({\r\n        price: low,\r\n        color: this.options.downColor,\r\n      });\r\n    } else {\r\n      this.mainSeries.createPriceLine({\r\n        price: low,\r\n        color: this.options.downColor,\r\n      });\r\n    }\r\n    if (highPriceLine) {\r\n      highPriceLine.applyOptions({\r\n        price: high,\r\n        color: this.options.upColor,\r\n      });\r\n    } else {\r\n      this.mainSeries.createPriceLine({\r\n        price: high,\r\n        color: this.options.upColor,\r\n      });\r\n    }\r\n  }\r\n\r\n  applyOptions(options: DeepPartial<IAdvanceChartOptions>) {\r\n    this.options = merge(cloneDeep(this.options), options);\r\n    if (options.highLowLineVisible !== undefined) this.tryDrawUpDownLine();\r\n    if (options.priceScaleMode !== undefined)\r\n      this.chartApi.applyOptions({\r\n        rightPriceScale: { mode: options.priceScaleMode },\r\n      });\r\n\r\n    if(options.priceLineVisible !== undefined) this.mainSeries?.applyOptions({priceLineVisible: options.priceLineVisible})\r\n\r\n    if(options.locale) this.chartApi.applyOptions({localization: { locale: options.locale }})\r\n  }\r\n  isShowVolume() {\r\n    return Boolean(this._volumeType);\r\n  }\r\n\r\n  showVolume(type: typeof this._volumeType = 'volume_overlay', options?: Partial<VolumeIndicatorOptions>) {\r\n    if (!this.mainSeries) return;\r\n    if (!type) return;\r\n    if (this.hasIndicator(type)) return;\r\n    const indicator = this.addIndicator(type) as VolumeIndicator;\r\n    if(options) indicator.applyOptions(options)\r\n    if (this._volumeType !== type) {\r\n      if (this._volumeType) this.removeIndicator(this._volumeType);\r\n      this._volumeType = type;\r\n    }\r\n    const volumePaneIndex = indicator.getPaneIndex();\r\n    if (volumePaneIndex === 0) return;\r\n    const indicatorNeedToMove = Array.from(this.indicators.values()).map(\r\n      (item) => {\r\n        if (item === indicator) return;\r\n        const paneIndex = item.getPaneIndex();\r\n        if (paneIndex === 0) return;\r\n        if (paneIndex >= volumePaneIndex) return;\r\n\r\n        return {\r\n          paneIndex,\r\n          indicator: item,\r\n        };\r\n      }\r\n    );\r\n    indicator.setPaneIndex(1);\r\n    \r\n    indicatorNeedToMove.map((item) => {\r\n      if (!item) return;\r\n      item.indicator.setPaneIndex(item.paneIndex + 1);\r\n    });\r\n  }\r\n\r\n  hiddenVolume() {\r\n    if (!this._volumeType) return;\r\n    this.removeIndicator(this._volumeType);\r\n  }\r\n\r\n  listIndicators() {\r\n    return Array.from(this.indicators.keys()).filter(item => item !== this._volumeType);\r\n  }\r\n\r\n  addIndicator(name: string) {\r\n    const instance = IndicatorFactory.createIndicator(\r\n      name,\r\n      this.chartApi,\r\n      {\r\n        numberFormatter: () => this.numberFormatter,\r\n        upColor: this.options.upColor,\r\n        downColor: this.options.downColor,\r\n      },\r\n      this.chartApi.panes().length\r\n    );\r\n\r\n    instance.setData(this.data);\r\n    this.indicators.set(name, instance);\r\n    this._updated.fire();\r\n    this._indicatorChanged.fire(name, 'add');\r\n    if(this.mainSeries) {\r\n      instance.mainSeriesChanged?.(this.mainSeries);\r\n    }\r\n    return instance;\r\n  }\r\n\r\n  removeIndicator(name: string) {\r\n    const instance = this.indicators.get(name);\r\n    if (!instance) return;\r\n    instance.remove();\r\n    this.indicators.delete(name);\r\n    this._updated.fire();\r\n    this._indicatorChanged.fire(name, 'remove');\r\n  }\r\n\r\n  hasIndicator(name: string) {\r\n    return this.indicators.has(name);\r\n  }\r\n\r\n  remove() {\r\n    Array.from(this.indicators.values()).map((instance) => instance.remove());\r\n    this.indicators.clear();\r\n    this.chartApi.remove();\r\n    this._destroyed.fire();\r\n\r\n    this._chartTypeChanged.destroy();\r\n    this._indicatorChanged.destroy();\r\n    this._destroyed.destroy();\r\n    this._updated.destroy();\r\n    this._crosshairMoved.destroy();\r\n    this._chartHovered.destroy();\r\n    this.__destroyed = true\r\n  }\r\n\r\n  groupIndicatorByPane(): Array<IGroupIndicatorByPane> {\r\n    const indicators = Array.from(this.indicators.values());\r\n    const panes = Array.from(this.chartApi.panes()).map((pane) => ({\r\n      pane,\r\n      indicators: [] as ChartIndicator[],\r\n    }));\r\n    for (const indicator of indicators) {\r\n      panes[indicator.getPaneIndex()].indicators.push(indicator);\r\n    }\r\n\r\n    return panes;\r\n  }\r\n\r\n  fitRange(range: {from: Logical, to: Logical}) {\r\n    const rightOffset = this.chartApi.options().timeScale.rightOffset\r\n    const space = range.to - range.from\r\n    const barVisiable = this.maxBar - rightOffset\r\n    this.chartApi.timeScale().setVisibleLogicalRange({\r\n      from: range.from, \r\n      to: (space < barVisiable ? range.from + barVisiable : range.to) + rightOffset\r\n    });\r\n  }\r\n\r\n  fitContent() {\r\n    const range = {from: 0 as Logical, to: Math.max(this.maxBar, this.data.length) as Logical};\r\n    this.fitRange(range);\r\n\r\n    return range\r\n  }\r\n\r\n  get maxBar () {\r\n    const maxBarSpacing = this.chartApi.options().timeScale.maxBarSpacing;\r\n    const width = this.chartApi.timeScale().width();\r\n\r\n    return Math.round(width / maxBarSpacing)\r\n  }\r\n\r\n  get loading () {\r\n    return Boolean(this._loading.lastParams()?.[0])\r\n  }\r\n\r\n  set loading(status: boolean) {\r\n    this._loading.fire(status)\r\n  }\r\n  \r\n  /** --------------- public delegate --------------- */\r\n\r\n  updated() {\r\n    return this._updated as IPublicDelegate<typeof this._updated>;\r\n  }\r\n\r\n  chartTypeChanged() {\r\n    return this._chartTypeChanged as IPublicDelegate<\r\n      typeof this._chartTypeChanged\r\n    >;\r\n  }\r\n\r\n  indicatorChanged() {\r\n    return this._indicatorChanged as IPublicDelegate<\r\n      typeof this._indicatorChanged\r\n    >;\r\n  }\r\n\r\n  crosshairMoved() {\r\n    return this._crosshairMoved satisfies IPublicDelegate<Delegate<OHLCVExtraData, OHLCVExtraData>>;\r\n  }\r\n\r\n  destroyed() {\r\n    return this._destroyed satisfies IPublicDelegate<Delegate>;\r\n  }\r\n\r\n  chartHovered() {\r\n    return this._chartHovered satisfies IPublicDelegate<Delegate<boolean>>;\r\n  }\r\n\r\n  onLoading() {\r\n    return this._loading satisfies IPublicDelegate<Delegate<boolean>>;\r\n  }\r\n\r\n  optionChanged(): ISubscription {\r\n    return this._optionChanged satisfies IPublicDelegate<Delegate>;\r\n  }\r\n\r\n  mainSeriesChanged(): ISubscription {\r\n    return this._mainSeriesChanged satisfies IPublicDelegate<Delegate>;\r\n  }\r\n}\r\n"], "names": ["defaultAdvanceChartOptions", "upColor", "downColor", "PriceScaleMode", "AdvanceChart", "container", "options", "__publicField", "Period", "Delegate", "merge", "cloneDeep", "createChart", "percentageValue", "time", "timeToDate", "timePoint", "tickMarkType", "param", "index", "binarySearchIndex", "timeToUnix", "item", "data", "prev", "DisplayTimezone", "NumberFormatterFactory", "range", "from", "to", "fromIndex", "toIndex", "current", "type", "mainSeries", "LineSeries", "LastPriceAnimationMode", "CandlestickSeries", "AreaSeries", "BarSeries", "BaselineSeries", "indicator", "_a", "baseValue", "interval", "instance", "bar", "replaceLastPoint", "lastPoint", "prepareBar", "newRange", "line", "dataSet", "low", "acc", "high", "lowPriceLine", "highPriceLine", "volumePaneIndex", "indicatorNeedToMove", "paneIndex", "name", "IndicatorFactory", "indicators", "panes", "pane", "rightOffset", "space", "barVisiable", "maxBarSpacing", "width", "status"], "mappings": "gpBAiCaA,EAAmD,CAAA,QAC9DC,EAAA,QAAA,UACAC,EAAA,UACA,UAAW,UACX,mBAAoB,GACpB,cAAeD,EAAA,QACf,aAAcC,EAAA,UACd,eAAgBC,EAAe,eAAA,OAC/B,iBAAkB,GAClB,OAAQ,KACR,UAAW,UACX,UAAW,OACX,UAAW,KAAK,iBAAiB,gBAAkB,EAAA,SACnD,OAAQ,GACV,EAGO,MAAMC,CAAsC,CA+BjD,YACEC,EACAC,EACA,CAjCFC,EAAA,gBACAA,EAAA,iBACAA,EAAA,iBAAsC,MACtCA,EAAA,kBAA4C,MAC5CA,EAAA,oBAAyB,CACvB,OAAQC,EAAO,OAAA,IACf,MAAO,CACT,GACAD,EAAA,mBAAc,IAENA,EAAA,YAAyB,CAAC,GAE1BA,EAAA,sBAAiB,KACjBA,EAAA,oBAIAA,EAAA,yBAAoB,IAAIE,EAAAA,UACxBF,EAAA,yBAAoB,IAAIE,EAAAA,UACxBF,EAAA,kBAAa,IAAIE,EAAAA,UACjBF,EAAA,gBAAW,IAAIE,EAAAA,UACfF,EAAA,uBAAkB,IAAIE,EAAAA,UACtBF,EAAA,qBAAgB,IAAIE,EAAAA,UACpBF,EAAA,uBAAkB,IAAIE,EAAAA,UACtBF,EAAA,gBAAW,IAAIE,EAAAA,UACfF,EAAA,sBAAiB,IAAIE,EAAAA,UACrBF,EAAA,0BAAqB,IAAIE,EAAAA,UAEzBF,EAAA,yBAMD,KAAA,QAAU,OAAO,OAAOG,EAAAA,MAAMC,EAAA,UAAUX,CAA0B,EAAGM,GAAW,CAAA,CAAE,CAAC,EACnF,KAAA,SAAWM,cAAYP,EAAW,CACrC,OAAQ,CACN,gBAAiB,GACjB,MAAO,CACL,eAAgB,UAChB,aAAc,EAChB,EACA,SAAU,KAAK,QAAQ,SACvB,WAAY,KAAK,QAAQ,WACzB,UAAW,KAAK,QAAQ,SAC1B,EACA,SAAU,GACV,OAAQ,KAAK,QAAQ,OACrB,aAAc,CACZ,OAAQ,KAAK,QAAQ,OACrB,oBAAsBQ,GAA4B,KAAK,gBAAgB,QAAQA,EAAkB,GAAG,EAEpG,cAAgBC,GAAe,KAAK,iBAAiB,OAAOC,EAAA,WAAWD,CAAI,CAAC,CAC9E,EACA,UAAW,CACT,cAAe,GACf,YAAa,GACb,cAAe,GACf,cAAe,EACf,eAAgB,GAChB,YAAa,GACb,kBAAoB,CAACE,EAAWC,IAAiB,KAAK,iBAAiB,kBAAkBF,EAAAA,WAAWC,CAAS,EAAGC,CAAY,CAC9H,EACA,mBAAoB,CAClB,aAAc,CACZ,OAAQ,IACR,IAAK,GAAA,CAET,EACA,eAAgB,CACd,cAAe,EACjB,EACA,YAAa,CACX,qBAAsB,EACxB,EACA,gBAAiB,CACf,cAAe,GACf,KAAM,KAAK,QAAQ,cACrB,EAEA,KAAM,CACJ,UAAW,CACT,QAAS,EACX,EACA,UAAW,CACT,MAAO,KAAK,QAAQ,SAAA,CACtB,CACF,CACD,EAEI,KAAA,SAAS,uBAAwBC,GAAU,CAG9C,GAFIA,EAAM,OAAS,QAEf,CADe,KAAK,WACP,OAEjB,MAAMC,EAAQC,EAAA,kBACZ,KAAK,KACLC,EAAA,WAAWH,EAAM,IAAI,EACpBI,GAASD,aAAWC,EAAK,IAAI,CAChC,EACA,GAAIH,IAAU,GAAI,OAElB,KAAM,CAACI,EAAMC,CAAI,EAAI,KAAK,kBAAkBL,CAAK,EAE5C,KAAA,gBAAgB,KAAKI,EAAMC,CAAI,CAAA,CACrC,EAEI,KAAA,SAAS,uBAAwBN,GAAU,CAC9C,GAAIA,EAAM,UAAY,cAAkB,KAAK,cAAc,KAAK,EAAK,EAChE,KAAA,cAAc,KAAK,EAAI,CAAA,CAC7B,EAED,KAAK,SAAS,UAAY,EAAA,gCAAiCA,GAAU,CACnE,GAAI,CAACA,EAAO,OAAO,KAAK,gBAAgB,KAAK,CAAA,CAAE,EAE1C,KAAA,gBAAgB,KAAK,KAAK,OAAO,CAAA,CACvC,EAEI,KAAA,gBAAgB,UAAU,IAAM,CACnC,KAAK,kBAAkB,EACvB,KAAK,wBAAwB,CAAA,CAC9B,EAEI,KAAA,iBAAmB,IAAIO,EAAA,gBAAgB,IAAI,CAAA,CAGlD,IAAI,iBAAmB,CACrB,OAAOC,EAAAA,uBAAuB,UAAU,KAAK,QAAQ,QAAU,IAAI,CAAA,CAGrE,IAAI,SAAW,CACb,MAAMC,EAAQ,KAAK,SAAS,UAAA,EAAY,gBAAgB,EACrD,GAAA,CAACA,EAAO,MAAO,CAAC,EACb,KAAA,CAAE,KAAAC,EAAM,GAAAC,CAAA,EAAOF,EACfG,EAAYV,EAAAA,kBAAkB,KAAK,KAAMC,EAAW,WAAAO,CAAI,EAAIN,GAASD,EAAAA,WAAWC,EAAK,IAAI,CAAC,EAC1FS,EAAUX,EAAAA,kBAAkB,KAAK,KAAMC,EAAW,WAAAQ,CAAE,EAAIP,GAASD,EAAAA,WAAWC,EAAK,IAAI,CAAC,EAC5F,OAAO,KAAK,KAAK,MAAMQ,EAAWC,EAAU,CAAC,CAAA,CAG/C,SAAU,CACR,OAAO,KAAK,IAAA,CAGd,eAAgB,CACd,OAAO,MAAM,KAAK,KAAK,WAAW,QAAQ,CAAA,CAG5C,kBAAkBZ,EAAe,CACzB,MAAAa,EAAU,KAAK,KAAKb,CAAK,EACzBK,EAAO,KAAK,KAAKL,EAAQ,EAAIA,EAAQ,EAAIA,CAAK,EAE7C,MAAA,CAACa,EAASR,CAAI,CAAA,CAGvB,WAAY,CACV,OAAO,KAAK,kBAAkB,KAAK,KAAK,OAAS,CAAC,CAAA,CAGpD,aAAaS,EAAyB,CAChC,GAAAA,IAAS,KAAK,UAAW,OACzB,IAAAC,EAEJ,OAAQD,EAAM,CACZ,IAAK,OACHC,EAAa,KAAK,SAAS,UACzBC,EAAA,WACA,CACE,MAAO,KAAK,QAAQ,UACpB,iBAAkB,KAAK,QAAQ,iBAC/B,mBAAoBC,EAAuB,uBAAA,aAC3C,UAAW,CACb,EACA,CACF,EACA,MACF,IAAK,SACHF,EAAa,KAAK,SAAS,UACzBG,EAAA,kBACA,CAAE,QAAS,KAAK,QAAQ,QAAS,UAAW,KAAK,QAAQ,UAAW,iBAAkB,KAAK,QAAQ,gBAAkB,EACrH,CACF,EACA,MACF,IAAK,WACHH,EAAa,KAAK,SAAS,UACzBI,EAAA,WACA,CACE,SAAU,KAAK,QAAQ,UACvB,UAAW,KAAK,QAAQ,UACxB,YAAa,YACb,iBAAkB,KAAK,QAAQ,iBAC/B,mBAAoBF,EAAuB,uBAAA,aAC3C,UAAW,CACb,EACA,CACF,EACA,MACF,IAAK,MACHF,EAAa,KAAK,SAAS,UACzBK,EAAA,UACA,CAAE,QAAS,KAAK,QAAQ,QAAS,UAAW,KAAK,QAAQ,UAAW,iBAAkB,KAAK,QAAQ,gBAAiB,EACpH,CACF,EACA,MACF,IAAK,WACHL,EAAa,KAAK,SAAS,UACzBM,EAAA,eACA,CACE,aAAc,KAAK,QAAQ,QAC3B,gBAAiB,KAAK,QAAQ,UAC9B,iBAAkB,cAClB,iBAAkB,cAClB,cAAe,cACf,cAAe,cACf,iBAAkB,KAAK,QAAQ,iBAC/B,mBAAoBJ,EAAuB,uBAAA,aAC3C,UAAW,CACb,EACA,CACF,EACA,MACF,IAAK,gBACHF,EAAa,KAAK,SAAS,UACzBM,EAAA,eACA,CACE,aAAc,KAAK,QAAQ,QAC3B,gBAAiB,KAAK,QAAQ,UAC9B,iBAAkB,KAAK,QAAQ,iBAC/B,mBAAoBJ,EAAuB,uBAAA,aAC3C,UAAW,CACb,EACA,CACF,EACA,MACF,QACQ,MAAA,IAAI,MAAM,oBAAoB,CAAA,CAGpC,KAAK,YACF,KAAA,SAAS,aAAa,KAAK,UAAU,EAE5C,KAAK,UAAYH,EACjB,KAAK,WAAaC,EAClB,KAAK,wBAAwB,EAC7B,KAAK,oBAAoB,EACzB,KAAK,kBAAkB,EACjB,MAAA,KAAK,KAAK,WAAW,OAAQ,CAAA,EAAE,QAAqBO,GAAA,OAAA,OAAAC,EAAAD,EAAU,oBAAV,YAAAC,EAAA,KAAAD,EAA8BP,GAAW,EACnG,KAAK,SAAS,KAAK,EACnB,KAAK,kBAAkB,KAAK,CAAA,CAG9B,yBAAyB,OACvB,GAAI,KAAK,aAAe,KAAK,YAAc,YAAc,KAAK,YAAc,iBAAkB,CAC5F,MAAMS,GAAYD,EAAA,KAAK,QAAQ,GAAG,CAAC,IAAjB,YAAAA,EAAoB,MAClCC,GACD,KAAK,WAAiD,aAAa,CAClE,UAAW,CAAE,MAAOA,CAAU,CAAA,CAC/B,CACH,CACF,CAGF,QAAQpB,EAAqBqB,EAAoB,CAC/C,KAAK,aAAeA,EACpB,KAAK,KAAOrB,EAAK,IAAKD,IAAU,CAAE,GAAGA,EAAM,MAAOA,EAAK,MAAO,aAAcA,CAAO,EAAA,EACnF,KAAK,UAAU,EACD,KAAK,SAAS,UAAA,EAAY,gBAAgB,GAEjD,KAAA,gBAAgB,KAAK,KAAK,OAAO,EAExC,KAAK,SAAS,KAAK,CAAA,CAGrB,qBAAsB,CACf,KAAK,YACL,KAAA,WAAW,QAAQ,KAAK,IAAgC,CAAA,CAE/D,WAAY,CACV,KAAK,oBAAoB,EACzB,MAAM,KAAK,KAAK,WAAW,OAAA,CAAQ,EAAE,IAAKuB,GACxCA,EAAS,QAAQ,KAAK,IAAI,CAC5B,CAAA,CAGF,OAAOC,EAAkBC,EAA4B,GAAO,OAC1D,KAAM,CAACC,CAAS,EAAI,KAAK,UAAU,EAC7BC,EAAa,CACjB,GAAGH,EACH,MAAOA,EAAI,MACX,aAAcA,EACd,KAAMC,EAAmBC,EAAU,KAAOF,EAAI,IAChD,EAEA,GAAGC,EACD,KAAK,KAAK,OAAO,KAAK,KAAK,OAAS,EAAG,EAAGE,CAAU,MAC/C,CACA,KAAA,KAAK,KAAKA,CAAU,EAEzB,MAAMtB,EAAQ,KAAK,SAAS,UAAA,EAAY,uBAAuB,EAG5D,GAAAA,GAAU,KAAK,MAAMA,EAAM,EAAE,EAAI,KAAK,KAAK,OAAS,CACrD,MAAMuB,EAAW,CAAC,KAAMvB,EAAM,KAAM,GAAIA,EAAM,EAAE,EAChD,KAAK,SAAS,YAAY,uBAAuBuB,CAAQ,CAAA,CAC3D,EAGGR,EAAA,KAAA,aAAA,MAAAA,EAAY,OAAOO,GAClB,MAAA,KAAK,KAAK,WAAW,OAAO,CAAC,EAAE,IAAKJ,GAAaA,EAAS,OAAA,CAAQ,EAExE,KAAK,kBAAkB,EACvB,KAAK,SAAS,KAAK,CAAA,CAGrB,mBAAoB,OACd,GAAA,CAAC,KAAK,WAAY,OAClB,GAAA,CAAC,KAAK,QAAQ,mBAAoB,CAC/B,KAAA,WAAW,WAAa,EAAA,WAAgB,OAAA,OAAAH,EAAA,KAAK,aAAL,YAAAA,EAAiB,gBAAgBS,GAAK,EACnF,MAAA,CAEF,MAAMC,EAAU,KAAK,QACfC,EAAMD,EAAQ,OAClB,CAACE,EAAKhC,IAAS,KAAK,IAAIgC,EAAKhC,EAAK,KAAK,EACvC,OAAO,gBACT,EACMiC,EAAOH,EAAQ,OACnB,CAACE,EAAKhC,IAAS,KAAK,IAAIgC,EAAKhC,EAAK,KAAK,EACvC,OAAO,gBACT,EACM,CAACkC,EAAcC,CAAa,IAAIf,EAAA,KAAK,aAAL,YAAAA,EAAiB,eAAgB,CAAC,EAEpEc,EACFA,EAAa,aAAa,CACxB,MAAOH,EACP,MAAO,KAAK,QAAQ,SAAA,CACrB,EAED,KAAK,WAAW,gBAAgB,CAC9B,MAAOA,EACP,MAAO,KAAK,QAAQ,SAAA,CACrB,EAECI,EACFA,EAAc,aAAa,CACzB,MAAOF,EACP,MAAO,KAAK,QAAQ,OAAA,CACrB,EAED,KAAK,WAAW,gBAAgB,CAC9B,MAAOA,EACP,MAAO,KAAK,QAAQ,OAAA,CACrB,CACH,CAGF,aAAajD,EAA4C,OACvD,KAAK,QAAUI,QAAMC,EAAAA,UAAU,KAAK,OAAO,EAAGL,CAAO,EACjDA,EAAQ,qBAAuB,QAAW,KAAK,kBAAkB,EACjEA,EAAQ,iBAAmB,QAC7B,KAAK,SAAS,aAAa,CACzB,gBAAiB,CAAE,KAAMA,EAAQ,cAAe,CAAA,CACjD,EAEAA,EAAQ,mBAAqB,UAAgBoC,EAAA,KAAA,aAAA,MAAAA,EAAY,aAAa,CAAC,iBAAkBpC,EAAQ,oBAEjGA,EAAQ,QAAa,KAAA,SAAS,aAAa,CAAC,aAAc,CAAE,OAAQA,EAAQ,MAAO,CAAA,CAAE,CAAA,CAE1F,cAAe,CACN,MAAA,EAAQ,KAAK,WAAW,CAGjC,WAAW2B,EAAgC,iBAAkB3B,EAA2C,CAGlG,GAFA,CAAC,KAAK,YACN,CAAC2B,GACD,KAAK,aAAaA,CAAI,EAAG,OACvB,MAAAQ,EAAY,KAAK,aAAaR,CAAI,EACrC3B,GAAmBmC,EAAA,aAAanC,CAAO,EACtC,KAAK,cAAgB2B,IACnB,KAAK,aAAkB,KAAA,gBAAgB,KAAK,WAAW,EAC3D,KAAK,YAAcA,GAEf,MAAAyB,EAAkBjB,EAAU,aAAa,EAC/C,GAAIiB,IAAoB,EAAG,OAC3B,MAAMC,EAAsB,MAAM,KAAK,KAAK,WAAW,OAAQ,CAAA,EAAE,IAC9DrC,GAAS,CACR,GAAIA,IAASmB,EAAW,OAClB,MAAAmB,EAAYtC,EAAK,aAAa,EACpC,GAAIsC,IAAc,GACd,EAAAA,GAAaF,GAEV,MAAA,CACL,UAAAE,EACA,UAAWtC,CACb,CAAA,CAEJ,EACAmB,EAAU,aAAa,CAAC,EAEJkB,EAAA,IAAKrC,GAAS,CAC3BA,GACLA,EAAK,UAAU,aAAaA,EAAK,UAAY,CAAC,CAAA,CAC/C,CAAA,CAGH,cAAe,CACR,KAAK,aACL,KAAA,gBAAgB,KAAK,WAAW,CAAA,CAGvC,gBAAiB,CACR,OAAA,MAAM,KAAK,KAAK,WAAW,KAAA,CAAM,EAAE,OAAOA,GAAQA,IAAS,KAAK,WAAW,CAAA,CAGpF,aAAauC,EAAc,OACzB,MAAMhB,EAAWiB,EAAAA,iBAAiB,gBAChCD,EACA,KAAK,SACL,CACE,gBAAiB,IAAM,KAAK,gBAC5B,QAAS,KAAK,QAAQ,QACtB,UAAW,KAAK,QAAQ,SAC1B,EACA,KAAK,SAAS,QAAQ,MACxB,EAES,OAAAhB,EAAA,QAAQ,KAAK,IAAI,EACrB,KAAA,WAAW,IAAIgB,EAAMhB,CAAQ,EAClC,KAAK,SAAS,KAAK,EACd,KAAA,kBAAkB,KAAKgB,EAAM,KAAK,EACpC,KAAK,cACGnB,EAAAG,EAAA,oBAAA,MAAAH,EAAA,KAAAG,EAAoB,KAAK,aAE7BA,CAAA,CAGT,gBAAgBgB,EAAc,CAC5B,MAAMhB,EAAW,KAAK,WAAW,IAAIgB,CAAI,EACpChB,IACLA,EAAS,OAAO,EACX,KAAA,WAAW,OAAOgB,CAAI,EAC3B,KAAK,SAAS,KAAK,EACd,KAAA,kBAAkB,KAAKA,EAAM,QAAQ,EAAA,CAG5C,aAAaA,EAAc,CAClB,OAAA,KAAK,WAAW,IAAIA,CAAI,CAAA,CAGjC,QAAS,CACD,MAAA,KAAK,KAAK,WAAW,OAAO,CAAC,EAAE,IAAKhB,GAAaA,EAAS,OAAA,CAAQ,EACxE,KAAK,WAAW,MAAM,EACtB,KAAK,SAAS,OAAO,EACrB,KAAK,WAAW,KAAK,EAErB,KAAK,kBAAkB,QAAQ,EAC/B,KAAK,kBAAkB,QAAQ,EAC/B,KAAK,WAAW,QAAQ,EACxB,KAAK,SAAS,QAAQ,EACtB,KAAK,gBAAgB,QAAQ,EAC7B,KAAK,cAAc,QAAQ,EAC3B,KAAK,YAAc,EAAA,CAGrB,sBAAqD,CACnD,MAAMkB,EAAa,MAAM,KAAK,KAAK,WAAW,QAAQ,EAChDC,EAAQ,MAAM,KAAK,KAAK,SAAS,OAAO,EAAE,IAAKC,IAAU,CAC7D,KAAAA,EACA,WAAY,CAAA,CAAC,EACb,EACF,UAAWxB,KAAasB,EACtBC,EAAMvB,EAAU,aAAa,CAAC,EAAE,WAAW,KAAKA,CAAS,EAGpD,OAAAuB,CAAA,CAGT,SAASrC,EAAqC,CAC5C,MAAMuC,EAAc,KAAK,SAAS,UAAU,UAAU,YAChDC,EAAQxC,EAAM,GAAKA,EAAM,KACzByC,EAAc,KAAK,OAASF,EAC7B,KAAA,SAAS,UAAU,EAAE,uBAAuB,CAC/C,KAAMvC,EAAM,KACZ,IAAKwC,EAAQC,EAAczC,EAAM,KAAOyC,EAAczC,EAAM,IAAMuC,CAAA,CACnE,CAAA,CAGH,YAAa,CACX,MAAMvC,EAAQ,CAAC,KAAM,EAAc,GAAI,KAAK,IAAI,KAAK,OAAQ,KAAK,KAAK,MAAM,CAAY,EACzF,YAAK,SAASA,CAAK,EAEZA,CAAA,CAGT,IAAI,QAAU,CACZ,MAAM0C,EAAgB,KAAK,SAAS,UAAU,UAAU,cAClDC,EAAQ,KAAK,SAAS,UAAA,EAAY,MAAM,EAEvC,OAAA,KAAK,MAAMA,EAAQD,CAAa,CAAA,CAGzC,IAAI,SAAW,OACb,MAAO,IAAQ3B,EAAA,KAAK,SAAS,WAAW,IAAzB,MAAAA,EAA6B,GAAE,CAGhD,IAAI,QAAQ6B,EAAiB,CACtB,KAAA,SAAS,KAAKA,CAAM,CAAA,CAK3B,SAAU,CACR,OAAO,KAAK,QAAA,CAGd,kBAAmB,CACjB,OAAO,KAAK,iBAAA,CAKd,kBAAmB,CACjB,OAAO,KAAK,iBAAA,CAKd,gBAAiB,CACf,OAAO,KAAK,eAAA,CAGd,WAAY,CACV,OAAO,KAAK,UAAA,CAGd,cAAe,CACb,OAAO,KAAK,aAAA,CAGd,WAAY,CACV,OAAO,KAAK,QAAA,CAGd,eAA+B,CAC7B,OAAO,KAAK,cAAA,CAGd,mBAAmC,CACjC,OAAO,KAAK,kBAAA,CAEhB"}