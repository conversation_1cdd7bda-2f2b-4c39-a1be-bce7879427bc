var c = Object.defineProperty;
var u = (a, e, t) => e in a ? c(a, e, { enumerable: !0, configurable: !0, writable: !0, value: t }) : a[e] = t;
var i = (a, e, t) => u(a, typeof e != "symbol" ? e + "" : e, t);
import { ensureDefined as r, ensureNotNull as h } from "../helpers/assertions.es.js";
import { merge as _, cloneDeep as p } from "es-toolkit";
import { binarySearch as l, timeToUnix as n } from "../helpers/utils.es.js";
class w {
  constructor() {
    i(this, "_chart");
    i(this, "_series");
    i(this, "_paneViews", []);
    i(this, "indicatorData", []);
    i(this, "_isDetached", !1);
    i(this, "_requestUpdate");
    // This method is a class property to maintain the
    // lexical 'this' scope (due to the use of the arrow function)
    // and to ensure its reference stays the same, so we can unsubscribe later.
    i(this, "_fireDataUpdated", (e) => {
      this.dataUpdated && this.dataUpdated(e);
    });
  }
  updateAllViews() {
    var e;
    this._isDetached || (e = this._updateAllViews) == null || e.call(this);
  }
  _updateAllViews() {
  }
  paneViews() {
    return this._paneViews;
  }
  requestUpdate() {
    this._requestUpdate && this._requestUpdate();
  }
  attached({
    chart: e,
    series: t,
    requestUpdate: s
  }) {
    var d;
    this._chart = e, this._series = t, this._series.subscribeDataChanged(this._fireDataUpdated), this._requestUpdate = s, this.requestUpdate(), this._paneViews.map((o) => o.attached(t, e)), (d = this._attached) == null || d.call(this);
  }
  get data() {
    const e = this.series.data();
    if (e.length > 0)
      r(e[0].customValues);
    else
      return [];
    return e.map((t) => t.customValues);
  }
  detached() {
    var e;
    (e = this._series) == null || e.unsubscribeDataChanged(this._fireDataUpdated), this._chart = void 0, this._series = void 0, this._paneViews = [], this._requestUpdate = void 0, this._isDetached = !0;
  }
  get chart() {
    return r(this._chart);
  }
  get series() {
    return r(this._series);
  }
  dataByTime(e) {
    return l(
      this.indicatorData,
      n(e),
      (t) => n(t.time)
    );
  }
  lastPoint() {
    return this.indicatorData.at(-1);
  }
}
class D {
  constructor(e) {
    i(this, "options");
    i(this, "_series", null);
    i(this, "_chartApi", null);
    i(this, "_data", null);
    i(this, "_timeScale");
    this.options = _(p(this.defaultOptions()), e ?? {});
  }
  renderer() {
    return this;
  }
  draw(e) {
    this._chartApi && this._series && e.useBitmapCoordinateSpace((t) => {
      var s;
      return (s = this._drawImpl) == null ? void 0 : s.call(this, t);
    });
  }
  drawBackground(e) {
    this._chartApi && this._series && e.useBitmapCoordinateSpace(
      (t) => {
        var s;
        return (s = this._drawBackgroundImpl) == null ? void 0 : s.call(this, t);
      }
    );
  }
  get data() {
    return h(this._data);
  }
  get series() {
    return h(this._series);
  }
  get chartApi() {
    return h(this._chartApi);
  }
  get timeScale() {
    return this._timeScale || (this._timeScale = this.chartApi.timeScale()), this._timeScale;
  }
  getVisibleLogicalRange() {
    const e = this.timeScale.getVisibleLogicalRange();
    if (e)
      return {
        from: Math.floor(e.from),
        to: Math.ceil(e.to)
      };
  }
  getVisibleRange() {
    return this.timeScale.getVisibleRange();
  }
  coordinateToPrice(e) {
    return this.series.coordinateToPrice(e);
  }
  priceToCoordinate(e) {
    return this.series.priceToCoordinate(e);
  }
  timeToCoordinate(e) {
    return this.timeScale.timeToCoordinate(e);
  }
  attached(e, t) {
    this._series = e, this._chartApi = t;
  }
  update(e) {
    var t;
    this._data = e, (t = this._update) == null || t.call(this);
  }
}
export {
  D as PrimitivePaneViewBase,
  w as SeriesPrimitiveBase
};
//# sourceMappingURL=primitive-base.es.js.map
