"use strict";var c=Object.defineProperty;var u=(t,i,e)=>i in t?c(t,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[i]=e;var a=(t,i,e)=>u(t,typeof i!="symbol"?i+"":i,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const m=require("technicalindicators"),v=require("./abstract-indicator.cjs.js"),d=require("../custom-primitive/primitive-base.cjs.js"),p=require("../custom-primitive/pane-view/line.cjs.js"),n={color:"#2962ff",period:9,overlay:!0};class l extends d.SeriesPrimitiveBase{constructor(e){super();a(this,"linePrimitive");this.source=e,this.linePrimitive=new p.LinePrimitivePaneView({lineColor:this.source.options.color}),this._paneViews=[this.linePrimitive]}update(e){const r=[];for(const s of e){const o=s.value;o&&r.push({time:s.time,price:o[0]})}this.linePrimitive.update(r)}}class h extends v.ChartIndicator{constructor(){super(...arguments);a(this,"smaPrimitive",new l(this))}getDefaultOptions(){return n}_mainSeriesChanged(e){e.attachPrimitive(this.smaPrimitive)}remove(){var e;super.remove(),(e=this.mainSeries)==null||e.detachPrimitive(this.smaPrimitive)}applyIndicatorData(){this.smaPrimitive.update(this._executionContext.data)}formula(e){const r=e.new_var(e.symbol.close,this.options.period);return r.calculable()?new m.SMA({values:r.getAll(),period:this.options.period}).getResult():void 0}}exports.SMAPrimitive=l;exports.default=h;exports.defaultOptions=n;
//# sourceMappingURL=sma-indicator.cjs.js.map
