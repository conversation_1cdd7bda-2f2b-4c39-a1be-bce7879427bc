import { ensureNotNull as s } from "../../helpers/assertions.es.js";
import { PrimitivePaneViewBase as a } from "../primitive-base.es.js";
import { LineStyle as h } from "lightweight-charts";
import { setLineStyle as m } from "../../helpers/line-style.es.js";
const p = {
  lineColor: "#eee",
  lineWidth: 1,
  lineDash: h.Solid
};
class u extends a {
  getXCoordinate(t) {
    return "time" in t ? this.timeToCoordinate(t.time) : t.x;
  }
  _drawImpl(t) {
    if (this.data.length === 0) return;
    const e = t.context;
    e.scale(t.horizontalPixelRatio, t.verticalPixelRatio), e.lineWidth = this.options.lineWidth, e.strokeStyle = this.options.lineColor;
    const o = new Path2D();
    e.beginPath();
    const r = this.data.map((i) => ({
      x: this.getXCoordinate(i),
      y: this.priceToCoordinate(i.price)
    })), [n, ...l] = r;
    o.moveTo(s(n.x), s(n.y));
    for (const i of l)
      !i.x || !i.y || o.lineTo(i.x, i.y);
    m(e, this.options.lineDash), e.stroke(o);
  }
  defaultOptions() {
    return p;
  }
}
export {
  p as LinePrimitiveOptionsDefault,
  u as LinePrimitivePaneView
};
//# sourceMappingURL=line.es.js.map
