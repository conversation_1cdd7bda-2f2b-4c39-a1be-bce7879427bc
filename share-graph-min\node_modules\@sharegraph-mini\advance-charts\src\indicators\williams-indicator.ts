import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {RSI, WilliamsR} from "technicalindicators";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface WilliamsIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  period: number
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: WilliamsIndicatorOptions = {
  color: "rgba(108, 80, 175, 1)",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: '#7e57c21a',
  period: 14,
  overlay: false
}

export type RsiLine = Nominal<number, 'Williams'>

export type RsiData = [RsiLine]

export default class WilliamsIndicator extends ChartIndicator<WilliamsIndicatorOptions, RsiData> {
  rsiSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<WilliamsIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    this.rsiSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'williams',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 80, minValue: 20})
    }, paneIndex);
    
    this.rsiSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 70,
        lowPrice: 30,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): WilliamsIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): RsiData | undefined {
    const closeSeries = c.new_var(c.symbol.close, this.options.period + 1);

    if(!closeSeries.calculable()) return;

    const [rsi] = new WilliamsR({
      
    }).result;

    return 1
  }


  applyIndicatorData() {
    const rsi: SingleValueData[] = [];
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      rsi.push({time: bar.time as Time, value: value[0]})
    }

    this.rsiSeries.setData(rsi)
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.rsiSeries);
  }

  _applyOptions() {
    this.rsiSeries.applyOptions({color: this.options.color})
    this.applyIndicatorData();
  }


  setPaneIndex(paneIndex: number) {
    this.rsiSeries.moveToPane(paneIndex)
  }

  getPaneIndex(): number {
    return this.rsiSeries.getPane().paneIndex()
  }
}