import { <PERSON>alRang<PERSON>, Time } from 'lightweight-charts';
import { Des<PERSON>yable, OHLCVSimple } from '../interface';
import { AdvanceChart } from './advance-chart';
import { Delegate, IPublicDelegate } from '../helpers/delegate';
import { Interval, Period } from './i-advance-chart';
import { default as dayjs } from '../helpers/dayjs-setup';
import { Dayjs } from 'dayjs';

export interface IDataFetchQuery {
    from: Date;
    to: Date;
    interval: Interval;
}
export type IDataFetchUtils = {
    forward: (time: Time, step: number) => Dayjs;
};
export interface IDataFetch {
    refeshTime?: number;
    /**
     * Fetches the initial data when:
     * - The chart is first loaded
     * - The user changes the visible time range
     * - The interval/period is changed
     *
     * This provides the base dataset that the chart will display initially.
     * Subsequent updates/pagination will build upon this data.
     */
    fetchInitialData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;
    /**
     * Fetches historical data that's outside the current visible chart range.
     * This data is used to:
     * - Calculate indicators that require lookback periods
     * - Provide seamless scrolling experience when user reaches chart boundaries
     * Automatically triggered when scrolling to the beginning/end of loaded data.
     */
    fetchPaginationData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;
    /**
     * Fetches updated data periodically based on refreshTime interval.
     * Queries data from the timestamp of last data point to current time.
     * New data points with same timestamps will overwrite existing ones.
     * Used for real-time updates to the chart.
     */
    fetchUpdateData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;
}
export declare function roundTime(time: Time, period: Period): Time;
export declare const aggregate: (items: OHLCVSimple[]) => {
    time: Time;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
};
export declare class DataFeed implements Destroyable {
    advanceChart: AdvanceChart;
    protected dataFetch: IDataFetch;
    _loading: boolean;
    _data: OHLCVSimple[];
    _dataChanged: Delegate<void, void, void>;
    interval: Interval;
    initialData: boolean;
    endOfData: boolean;
    _refeshTimer: NodeJS.Timeout | null;
    _destroyed: boolean;
    constructor(advanceChart: AdvanceChart, dataFetch: IDataFetch);
    get data(): OHLCVSimple[];
    set data(d: OHLCVSimple[]);
    isNeedPaging(logicalRange: LogicalRange): boolean;
    groupData(): {
        time: Time;
        open: number;
        high: number;
        low: number;
        close: number;
        volume: number;
    }[];
    processNewData(data: OHLCVSimple[]): OHLCVSimple[] | undefined;
    updateData(): Promise<void>;
    pagingData(logicalRange: LogicalRange): Promise<void>;
    onVisibleLogicalRangeChange(logicalRange: LogicalRange | null): Promise<void>;
    forward(time: Time, step: number): dayjs.Dayjs;
    setRange({ from, to, interval }: IDataFetchQuery): Promise<void>;
    private resetState;
    dataChanged(): IPublicDelegate<typeof this._dataChanged>;
    trade(trade: {
        time: Time;
        price: number;
        volume: number;
    }): void;
    destroy(): void;
}
