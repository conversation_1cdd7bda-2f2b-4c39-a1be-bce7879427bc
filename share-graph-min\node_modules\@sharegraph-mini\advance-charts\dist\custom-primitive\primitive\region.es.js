var w = Object.defineProperty;
var P = (t, e, i) => e in t ? w(t, e, { enumerable: !0, configurable: !0, writable: !0, value: i }) : t[e] = i;
var n = (t, e, i) => P(t, typeof e != "symbol" ? e + "" : e, i);
import { LineStyle as l } from "lightweight-charts";
import { SeriesPrimitiveBase as h, PrimitivePaneViewBase as u } from "../primitive-base.es.js";
import { LinePrimitiveOptionsDefault as d, LinePrimitivePaneView as c } from "../pane-view/line.es.js";
import { merge as m, cloneDeep as V } from "es-toolkit";
import { ensureNotNull as p } from "../../helpers/assertions.es.js";
const L = {
  backgroundColor: "#2196f31a",
  ...d,
  upPrice: 0,
  lowPrice: 0
};
class f extends u {
  _drawBackgroundImpl(e) {
    const i = e.context;
    i.scale(e.horizontalPixelRatio, e.verticalPixelRatio);
    const s = i.canvas.width, o = new Path2D(), a = p(this.priceToCoordinate(this.options.upPrice)), r = p(this.priceToCoordinate(this.options.lowPrice));
    o.moveTo(0, a), o.lineTo(s, a), o.lineTo(s, r), o.lineTo(0, r), o.lineTo(0, a), o.closePath(), i.beginPath(), i.fillStyle = this.options.backgroundColor, i.fill(o);
  }
  defaultOptions() {
    return {
      backgroundColor: "#2196f31a",
      upPrice: 0,
      lowPrice: 0
    };
  }
}
class C extends h {
  constructor(i) {
    super();
    n(this, "bandPaneView");
    n(this, "upLinePaneView");
    n(this, "lowLinePaneView");
    n(this, "_options");
    n(this, "upPriceLine", null);
    n(this, "lowPriceLine", null);
    this._options = m(V(L), i), this.bandPaneView = new f(this._options), this.upLinePaneView = new c({ ...this._options, lineDash: l.LargeDashed }), this.lowLinePaneView = new c({ ...this._options, lineDash: l.LargeDashed }), this._paneViews = [this.upLinePaneView, this.bandPaneView, this.lowLinePaneView];
  }
  _updateAllViews() {
    const i = this.chart.timeScale().width(), { upPrice: s, lowPrice: o } = this._options;
    this.upLinePaneView.update([
      {
        x: 0,
        price: s
      },
      {
        x: i,
        price: s
      }
    ]), this.lowLinePaneView.update([
      {
        x: 0,
        price: o
      },
      {
        x: i,
        price: o
      }
    ]);
  }
}
export {
  f as RegionPaneView,
  C as RegionPrimitive,
  L as RegionPrimitiveOptionsDefault
};
//# sourceMappingURL=region.es.js.map
