import { ISeriesApi, Nominal, SeriesType, SingleValueData, WhitespaceData } from 'lightweight-charts';
import { SMAIndicatorOptions } from './sma-indicator';
import { Context, IIndicatorBar } from '../helpers/execution-indicator';
import { SeriesPrimitiveBase } from '../custom-primitive/primitive-base';
import { LinePrimitivePaneView } from '../custom-primitive/pane-view/line';
import { ChartIndicator } from './abstract-indicator';

export interface WMAIndicatorOptions extends SMAIndicatorOptions {
}
export declare const defaultOptions: WMAIndicatorOptions;
export declare class WMAPrimitive extends SeriesPrimitiveBase<SingleValueData | WhitespaceData> {
    protected source: WMAIndicator;
    linePrimitive: LinePrimitivePaneView;
    constructor(source: WMAIndicator);
    update(indicatorBars: IIndicatorBar<WMAData>[]): void;
}
export type WMAData = readonly [Nominal<number, 'WMA'>];
export default class WMAIndicator extends ChartIndicator<WMAIndicatorOptions, WMAData> {
    wmaPrimitive: WMAPrimitive;
    getDefaultOptions(): WMAIndicatorOptions;
    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void;
    remove(): void;
    applyIndicatorData(): void;
    formula(c: Context): WMAData | undefined;
}
