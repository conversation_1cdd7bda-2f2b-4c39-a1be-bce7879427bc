{"version": 3, "file": "region.cjs.js", "sources": ["../../../src/custom-primitive/primitive/region.ts"], "sourcesContent": ["import {Coordinate, DeepPartial, IPriceLine, ISeriesPrimitive, LineStyle, Time} from \"lightweight-charts\";\r\nimport {PrimitivePaneViewBase, SeriesPrimitiveBase} from \"../primitive-base\";\r\nimport {LinePrimitivePaneView, LinePrimitiveOptionsDefault} from \"../pane-view/line\";\r\nimport {cloneDeep, merge} from \"es-toolkit\";\r\nimport {BitmapCoordinatesRenderingScope} from \"fancy-canvas\";\r\nimport {ensureNotNull} from \"../../helpers/assertions\";\r\n\r\nexport interface PrimitivePaneViewBaseOptions {\r\n  backgroundColor: string,\r\n  upPrice: number,\r\n  lowPrice: number,\r\n}\r\n\r\nexport interface RegionPrimitiveOptions extends PrimitivePaneViewBaseOptions {\r\n  lineWidth: number, \r\n  lineColor: string;\r\n}\r\n\r\nexport const RegionPrimitiveOptionsDefault: RegionPrimitiveOptions = {\r\n  backgroundColor: '#2196f31a',\r\n  ...LinePrimitiveOptionsDefault,\r\n  upPrice: 0,\r\n  lowPrice: 0,\r\n}\r\n\r\nexport interface RegionPaneViewData {\r\n  top: Coordinate, \r\n  bottom: Coordinate\r\n}\r\nexport class RegionPaneView extends PrimitivePaneViewBase<PrimitivePaneViewBaseOptions> {\r\n  _drawBackgroundImpl(renderingScope: BitmapCoordinatesRenderingScope): void {\r\n    const ctx = renderingScope.context;\r\n    ctx.scale(renderingScope.horizontalPixelRatio, renderingScope.verticalPixelRatio)\r\n    const width = ctx.canvas.width\r\n    const region = new Path2D;\r\n    const upCoor = ensureNotNull(this.priceToCoordinate(this.options.upPrice))\r\n    const lowCoor = ensureNotNull(this.priceToCoordinate(this.options.lowPrice))\r\n    region.moveTo(0, upCoor)\r\n    region.lineTo(width, upCoor)\r\n    region.lineTo(width, lowCoor)\r\n    region.lineTo(0, lowCoor)\r\n    region.lineTo(0, upCoor)\r\n    region.closePath()\r\n    ctx.beginPath();\r\n    ctx.fillStyle = this.options.backgroundColor\r\n    ctx.fill(region)\r\n  }\r\n\r\n  defaultOptions(): PrimitivePaneViewBaseOptions {\r\n    return { \r\n      backgroundColor: '#2196f31a',\r\n      upPrice: 0,\r\n      lowPrice: 0\r\n    }\r\n  }\r\n}\r\n\r\nexport class RegionPrimitive extends SeriesPrimitiveBase implements ISeriesPrimitive<Time> {\r\n  bandPaneView: RegionPaneView\r\n  upLinePaneView: LinePrimitivePaneView\r\n  lowLinePaneView: LinePrimitivePaneView\r\n  _options: RegionPrimitiveOptions\r\n\r\n  upPriceLine: IPriceLine | null = null\r\n  lowPriceLine: IPriceLine | null = null\r\n\r\n  constructor(options: DeepPartial<RegionPrimitiveOptions>) {\r\n    super();\r\n    this._options = merge(cloneDeep(RegionPrimitiveOptionsDefault), options)\r\n\r\n    this.bandPaneView = new RegionPaneView(this._options)\r\n    this.upLinePaneView = new LinePrimitivePaneView({...this._options, lineDash: LineStyle.LargeDashed})\r\n    this.lowLinePaneView = new LinePrimitivePaneView({...this._options, lineDash: LineStyle.LargeDashed})\r\n    this._paneViews = [this.upLinePaneView, this.bandPaneView, this.lowLinePaneView]\r\n  }\r\n\r\n  _updateAllViews(): void {\r\n    const width = this.chart.timeScale().width();\r\n    const { upPrice, lowPrice } = this._options;\r\n\r\n    this.upLinePaneView.update([\r\n      {\r\n        x: 0 as Coordinate,\r\n        price: upPrice,\r\n      },\r\n      {\r\n        x: width as Coordinate,\r\n        price: upPrice,\r\n      }\r\n    ])\r\n\r\n    this.lowLinePaneView.update([\r\n      {\r\n        x: 0 as Coordinate,\r\n        price: lowPrice,\r\n      },\r\n      {\r\n        x: width as Coordinate,\r\n        price: lowPrice,\r\n      }\r\n    ])\r\n  }\r\n}"], "names": ["RegionPrimitiveOptionsDefault", "LinePrimitiveOptionsDefault", "RegionPaneView", "PrimitivePaneViewBase", "renderingScope", "ctx", "width", "region", "upCoor", "ensureNotNull", "lowCoor", "RegionPrimitive", "SeriesPrimitiveBase", "options", "__publicField", "merge", "cloneDeep", "LinePrimitivePaneView", "LineStyle", "upPrice", "lowPrice"], "mappings": "2aAkBaA,EAAwD,CACnE,gBAAiB,YACjB,GAAGC,EAAA,4BACH,QAAS,EACT,SAAU,CACZ,EAMO,MAAMC,UAAuBC,EAAAA,qBAAoD,CACtF,oBAAoBC,EAAuD,CACzE,MAAMC,EAAMD,EAAe,QAC3BC,EAAI,MAAMD,EAAe,qBAAsBA,EAAe,kBAAkB,EAC1E,MAAAE,EAAQD,EAAI,OAAO,MACnBE,EAAS,IAAI,OACbC,EAASC,EAAAA,cAAc,KAAK,kBAAkB,KAAK,QAAQ,OAAO,CAAC,EACnEC,EAAUD,EAAAA,cAAc,KAAK,kBAAkB,KAAK,QAAQ,QAAQ,CAAC,EACpEF,EAAA,OAAO,EAAGC,CAAM,EAChBD,EAAA,OAAOD,EAAOE,CAAM,EACpBD,EAAA,OAAOD,EAAOI,CAAO,EACrBH,EAAA,OAAO,EAAGG,CAAO,EACjBH,EAAA,OAAO,EAAGC,CAAM,EACvBD,EAAO,UAAU,EACjBF,EAAI,UAAU,EACVA,EAAA,UAAY,KAAK,QAAQ,gBAC7BA,EAAI,KAAKE,CAAM,CAAA,CAGjB,gBAA+C,CACtC,MAAA,CACL,gBAAiB,YACjB,QAAS,EACT,SAAU,CACZ,CAAA,CAEJ,CAEO,MAAMI,UAAwBC,EAAAA,mBAAsD,CASzF,YAAYC,EAA8C,CAClD,MAAA,EATRC,EAAA,qBACAA,EAAA,uBACAA,EAAA,wBACAA,EAAA,iBAEAA,EAAA,mBAAiC,MACjCA,EAAA,oBAAkC,MAIhC,KAAK,SAAWC,EAAA,MAAMC,EAAU,UAAAhB,CAA6B,EAAGa,CAAO,EAEvE,KAAK,aAAe,IAAIX,EAAe,KAAK,QAAQ,EAC/C,KAAA,eAAiB,IAAIe,EAAAA,sBAAsB,CAAC,GAAG,KAAK,SAAU,SAAUC,YAAU,YAAY,EAC9F,KAAA,gBAAkB,IAAID,EAAAA,sBAAsB,CAAC,GAAG,KAAK,SAAU,SAAUC,YAAU,YAAY,EACpG,KAAK,WAAa,CAAC,KAAK,eAAgB,KAAK,aAAc,KAAK,eAAe,CAAA,CAGjF,iBAAwB,CACtB,MAAMZ,EAAQ,KAAK,MAAM,UAAA,EAAY,MAAM,EACrC,CAAE,QAAAa,EAAS,SAAAC,CAAS,EAAI,KAAK,SAEnC,KAAK,eAAe,OAAO,CACzB,CACE,EAAG,EACH,MAAOD,CACT,EACA,CACE,EAAGb,EACH,MAAOa,CAAA,CACT,CACD,EAED,KAAK,gBAAgB,OAAO,CAC1B,CACE,EAAG,EACH,MAAOC,CACT,EACA,CACE,EAAGd,EACH,MAAOc,CAAA,CACT,CACD,CAAA,CAEL"}