{"version": 3, "file": "delegate.cjs.js", "sources": ["../../src/helpers/delegate.ts"], "sourcesContent": ["export type Callback<T1 = void, T2 = void, T3 = void> = (param1: T1, param2: T2, param3: T3) => void;\r\n\r\nexport interface ISubscription<T1 = void, T2 = void, T3 = void> {\r\n\tsubscribe(callback: Callback<T1, T2, T3>, linkedObject?: unknown, singleshot?: boolean): void;\r\n\tunsubscribe(callback: Callback<T1, T2, T3>): void;\r\n\tunsubscribeAll(linkedObject: unknown): void;\r\n  lastParams(): [T1, T2, T3];\r\n}\r\n\r\ninterface Listener<T1, T2, T3> {\r\n\tcallback: Callback<T1, T2, T3>;\r\n\tlinkedObject?: unknown;\r\n\tsingleshot: boolean;\r\n}\r\n\r\nexport class Delegate<T1 = void, T2 = void, T3 = void> implements ISubscription<T1, T2, T3> {\r\n  private _listeners: Listener<T1, T2, T3>[] = [];\r\n\r\n  private _params: [T1, T2, T3] = [undefined as unknown as T1, undefined as unknown as T2, undefined as unknown as T3];\r\n\r\n  public fire(param1: T1, param2: T2, param3: T3) {\r\n    this._params = [param1, param2, param3];\r\n    const listenersSnapshot = [...this._listeners]\r\n    this._listeners = this._listeners.filter(listener => !listener.singleshot);\r\n\r\n    listenersSnapshot.forEach(listener => listener.callback(param1, param2, param3))\r\n  }\r\n\r\n  public lastParams(): [T1, T2, T3] {\r\n    return this._params;\r\n  }\r\n\r\n  public subscribe(callback: Callback<T1, T2, T3>, linkedObject?: unknown, singleshot?: boolean): void {\r\n    this._listeners.push({callback, linkedObject, singleshot: Boolean(singleshot)})\r\n  }\r\n\r\n  public unsubscribe(callback: Callback<T1, T2, T3>): void {\r\n    const index = this._listeners.findIndex(listener => listener.callback === callback);\r\n\r\n    if(index > -1) {\r\n      this._listeners.splice(index, 1)\r\n    }\r\n  }\r\n\r\n  public unsubscribeAll(linkedObject: unknown): void {\r\n    this._listeners = this._listeners.filter(listener => listener.linkedObject !== linkedObject)\r\n  }\r\n\r\n\r\n  hasListener() {\r\n    return Boolean(this._listeners.length)\r\n  }\r\n  destroy() {\r\n    this._listeners = []\r\n  }\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nexport type IPublicDelegate<T extends Delegate<any, any, any>> = T extends Delegate<infer T1, infer T2, infer T3>\r\n  ? ISubscription<T1, T2, T3>\r\n  : never;\r\n\r\n"], "names": ["Delegate", "__publicField", "param1", "param2", "param3", "listenersSnapshot", "listener", "callback", "linkedObject", "singleshot", "index"], "mappings": "oPAeO,MAAMA,CAA+E,CAArF,cACGC,EAAA,kBAAqC,CAAC,GAEtCA,EAAA,eAAwB,CAAC,OAA4B,OAA4B,MAA0B,GAE5G,KAAKC,EAAYC,EAAYC,EAAY,CAC9C,KAAK,QAAU,CAACF,EAAQC,EAAQC,CAAM,EACtC,MAAMC,EAAoB,CAAC,GAAG,KAAK,UAAU,EAC7C,KAAK,WAAa,KAAK,WAAW,OAAmBC,GAAA,CAACA,EAAS,UAAU,EAEzED,EAAkB,QAAoBC,GAAAA,EAAS,SAASJ,EAAQC,EAAQC,CAAM,CAAC,CAAA,CAG1E,YAA2B,CAChC,OAAO,KAAK,OAAA,CAGP,UAAUG,EAAgCC,EAAwBC,EAA4B,CAC9F,KAAA,WAAW,KAAK,CAAC,SAAAF,EAAU,aAAAC,EAAc,WAAY,EAAQC,EAAY,CAAA,CAGzE,YAAYF,EAAsC,CACvD,MAAMG,EAAQ,KAAK,WAAW,UAAsBJ,GAAAA,EAAS,WAAaC,CAAQ,EAE/EG,EAAQ,IACJ,KAAA,WAAW,OAAOA,EAAO,CAAC,CACjC,CAGK,eAAeF,EAA6B,CACjD,KAAK,WAAa,KAAK,WAAW,OAAmBF,GAAAA,EAAS,eAAiBE,CAAY,CAAA,CAI7F,aAAc,CACL,MAAA,EAAQ,KAAK,WAAW,MAAM,CAEvC,SAAU,CACR,KAAK,WAAa,CAAC,CAAA,CAEvB"}