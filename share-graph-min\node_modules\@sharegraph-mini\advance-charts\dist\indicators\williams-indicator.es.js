var a = Object.defineProperty;
var n = (o, i, e) => i in o ? a(o, i, { enumerable: !0, configurable: !0, writable: !0, value: e }) : o[i] = e;
var t = (o, i, e) => n(o, typeof i != "symbol" ? i + "" : i, e);
import { LineSeries as l } from "lightweight-charts";
import { RSI as c } from "technicalindicators";
import { ChartIndicator as p } from "./abstract-indicator.es.js";
import { RegionPrimitive as u } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as d } from "../helpers/utils.es.js";
const m = {
  color: "rgba(108, 80, 175, 1)",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#7e57c21a",
  period: 14,
  overlay: !1
};
class g extends p {
  constructor(e, r, s) {
    super(e, r);
    t(this, "rsiSeries");
    this.rsiSeries = e.addSeries(l, {
      color: this.options.color,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "williams",
      autoscaleInfoProvider: d({ maxValue: 80, minValue: 20 })
    }, s), this.rsiSeries.attachPrimitive(
      new u({
        upPrice: 70,
        lowPrice: 30,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return m;
  }
  formula(e) {
    const r = e.new_var(e.symbol.close, this.options.period + 1);
    if (!r.calculable()) return;
    const [s] = new c({
      period: this.options.period,
      values: r.getAll()
    }).result;
    return [s];
  }
  applyIndicatorData() {
    const e = [];
    for (const r of this._executionContext.data) {
      const s = r.value;
      s && e.push({ time: r.time, value: s[0] });
    }
    this.rsiSeries.setData(e);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.rsiSeries);
  }
  _applyOptions() {
    this.rsiSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.rsiSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.rsiSeries.getPane().paneIndex();
  }
}
export {
  g as default,
  m as defaultOptions
};
//# sourceMappingURL=williams-indicator.es.js.map
