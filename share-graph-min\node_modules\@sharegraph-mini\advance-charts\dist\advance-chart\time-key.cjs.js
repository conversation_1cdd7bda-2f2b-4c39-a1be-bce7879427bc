"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const d=require("lightweight-charts"),t=require("./i-advance-chart.cjs.js"),h=new(d.defaultHorzScaleBehavior()),c=60,s=c*60,n=s*24,S=n*7,l=n*3;function E(a,i){const e=h.key(a),u=i.period,o=i.times;switch(u){case t.Period.minute:return e-e%(c*o);case t.Period.hour:return e-e%(s*o);case t.Period.day:return e-e%(n*o);case t.Period.week:{const r=e+l;return e-r%(S*o)}case t.Period.month:{const r=new Date(e*1e3);return Math.floor(Date.UTC(r.getUTCFullYear(),r.getUTCMonth(),1,0,0,0)/1e3)}default:throw new Error("Invalid period")}}exports.timeKey=E;
//# sourceMappingURL=time-key.cjs.js.map
