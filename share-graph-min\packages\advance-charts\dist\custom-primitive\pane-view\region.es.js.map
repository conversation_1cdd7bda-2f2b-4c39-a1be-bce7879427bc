{"version": 3, "file": "region.es.js", "sources": ["../../../src/custom-primitive/pane-view/region.ts"], "sourcesContent": ["import {CanvasRenderingTarget2D} from \"fancy-canvas\";\r\nimport {PrimitivePaneViewBase} from \"../primitive-base\";\r\nimport {Coordinate} from \"lightweight-charts\";\r\nimport {ensureDefined} from \"../../helpers/assertions\";\r\n\r\nexport interface RegionPrimitivePaneViewOptions {\r\n  backgroundColor: string,\r\n  lineWidth: number, \r\n  lineColor: string;\r\n  lineStyle: number[]\r\n}\r\n\r\nexport const RegionPrimitiveOptionsDefault: RegionPrimitivePaneViewOptions = {\r\n  backgroundColor: '#2196f31a',\r\n  lineColor: '#787b86',\r\n  lineWidth: 1,\r\n  lineStyle: []\r\n}\r\n\r\ninterface RegionPrimitiveData {\r\n  x: Coordinate, \r\n  points: Coordinate[]\r\n}\r\n\r\nexport class RegionPrimitivePaneView extends PrimitivePaneViewBase<RegionPrimitivePaneViewOptions, RegionPrimitiveData> {\r\n  drawBackground(target: CanvasRenderingTarget2D): void {\r\n    const data = this.data\r\n    const visibleLogicalRange = this.getVisibleLogicalRange()\r\n    if(data.length < 2) return;\r\n    if(!visibleLogicalRange) return;\r\n    target.useBitmapCoordinateSpace(scope => {\r\n      const ctx = scope.context;\r\n      ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);\r\n\r\n      ctx.beginPath();\r\n      const region = new Path2D();\r\n      const from = 0;\r\n      const to = data.length\r\n      const firstBar = data[from];\r\n      region.moveTo(\r\n        firstBar.x, \r\n        ensureDefined(firstBar.points.at(0))\r\n      );\r\n\r\n      for (let i = from + 1; i < to; i++) {\r\n        const point = data[i];\r\n        region.lineTo(\r\n          point.x,\r\n          point.points.at(0) as number\r\n        );\r\n      }\r\n\r\n      for (let i = to - 1; i >= from; i--) {\r\n        const point = data[i];\r\n        region.lineTo(point.x, point.points.at(-1) as number);\r\n      }\r\n\r\n      region.lineTo(\r\n        firstBar.x, \r\n        ensureDefined(firstBar.points.at(0))\r\n      );\r\n      region.closePath();\r\n      ctx.fillStyle = this.options.backgroundColor\r\n      ctx.fill(region)\r\n\r\n      const paths = firstBar.points.map(() => new Path2D)\r\n\r\n      firstBar.points.map((point, index) => paths[index].moveTo(firstBar.x, point))\r\n\r\n      for (let i = from + 1; i < to; i++) {\r\n        const bar = data[i]\r\n        bar.points.map((point, index) => paths[index].lineTo(bar.x, point))\r\n      }\r\n\r\n      ctx.setLineDash(this.options.lineStyle);\r\n      ctx.lineWidth = this.options.lineWidth\r\n      ctx.strokeStyle = this.options.lineColor\r\n      paths.map(path => ctx.stroke(path))\r\n      ctx.setLineDash([]);\r\n    })\r\n    // const points: Coordinate[] = this.points\r\n    // if(points.length < 2) return;\r\n    // target.useBitmapCoordinateSpace(scope => {\r\n    //   const ctx = scope.context;\r\n    //   ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);\r\n\r\n    //   ctx.lineWidth = this.options.lineWidth;\r\n    //   ctx.strokeStyle = this.options.lineColor;\r\n      \r\n    //   ctx.beginPath();\r\n\r\n    //   const topLine = points[0]\r\n    //   const bottomLine = points[points.length - 1];\r\n\r\n    //   const width = ctx.canvas.width\r\n\r\n    //   const region = new Path2D();\r\n    //   const lines = new Path2D();\r\n\r\n      \r\n    //   for(const point of points) {\r\n    //     lines.moveTo(0, point)\r\n    //     lines.lineTo(width, point)\r\n    //   }\r\n\r\n    //   region.moveTo(0, topLine)\r\n    //   region.lineTo(width, topLine)\r\n    //   region.lineTo(width, bottomLine)\r\n    //   region.lineTo(0, bottomLine)\r\n    //   region.lineTo(0, topLine)\r\n    //   region.closePath();\r\n\r\n    //   ctx.setLineDash(LINE_DASH)\r\n    //   ctx.stroke(lines)\r\n    //   ctx.setLineDash([])\r\n    //   ctx.fillStyle = this.options.backgroundColor;\r\n    //   ctx.fill(region)\r\n    // })\r\n  }\r\n  defaultOptions(): RegionPrimitivePaneViewOptions {\r\n    return RegionPrimitiveOptionsDefault\r\n  }\r\n}"], "names": ["RegionPrimitiveOptionsDefault", "RegionPrimitivePaneView", "PrimitivePaneViewBase", "target", "data", "visibleLogicalRange", "scope", "ctx", "region", "from", "to", "firstBar", "ensureDefined", "i", "point", "paths", "index", "bar", "path"], "mappings": ";;AAYO,MAAMA,IAAgE;AAAA,EAC3E,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW,CAAA;AACb;AAOO,MAAMC,UAAgCC,EAA2E;AAAA,EACtH,eAAeC,GAAuC;AACpD,UAAMC,IAAO,KAAK,MACZC,IAAsB,KAAK,uBAAuB;AACrD,IAAAD,EAAK,SAAS,KACbC,KACJF,EAAO,yBAAyB,CAASG,MAAA;AACvC,YAAMC,IAAMD,EAAM;AAClB,MAAAC,EAAI,MAAMD,EAAM,sBAAsBA,EAAM,kBAAkB,GAE9DC,EAAI,UAAU;AACR,YAAAC,IAAS,IAAI,OAAO,GACpBC,IAAO,GACPC,IAAKN,EAAK,QACVO,IAAWP,EAAKK,CAAI;AACnB,MAAAD,EAAA;AAAA,QACLG,EAAS;AAAA,QACTC,EAAcD,EAAS,OAAO,GAAG,CAAC,CAAC;AAAA,MACrC;AAEA,eAASE,IAAIJ,IAAO,GAAGI,IAAIH,GAAIG,KAAK;AAC5B,cAAAC,IAAQV,EAAKS,CAAC;AACb,QAAAL,EAAA;AAAA,UACLM,EAAM;AAAA,UACNA,EAAM,OAAO,GAAG,CAAC;AAAA,QACnB;AAAA,MAAA;AAGF,eAASD,IAAIH,IAAK,GAAGG,KAAKJ,GAAMI,KAAK;AAC7B,cAAAC,IAAQV,EAAKS,CAAC;AACpB,QAAAL,EAAO,OAAOM,EAAM,GAAGA,EAAM,OAAO,GAAG,EAAE,CAAW;AAAA,MAAA;AAG/C,MAAAN,EAAA;AAAA,QACLG,EAAS;AAAA,QACTC,EAAcD,EAAS,OAAO,GAAG,CAAC,CAAC;AAAA,MACrC,GACAH,EAAO,UAAU,GACbD,EAAA,YAAY,KAAK,QAAQ,iBAC7BA,EAAI,KAAKC,CAAM;AAEf,YAAMO,IAAQJ,EAAS,OAAO,IAAI,MAAM,IAAI,QAAM;AAElD,MAAAA,EAAS,OAAO,IAAI,CAACG,GAAOE,MAAUD,EAAMC,CAAK,EAAE,OAAOL,EAAS,GAAGG,CAAK,CAAC;AAE5E,eAASD,IAAIJ,IAAO,GAAGI,IAAIH,GAAIG,KAAK;AAC5B,cAAAI,IAAMb,EAAKS,CAAC;AAClB,QAAAI,EAAI,OAAO,IAAI,CAACH,GAAOE,MAAUD,EAAMC,CAAK,EAAE,OAAOC,EAAI,GAAGH,CAAK,CAAC;AAAA,MAAA;AAGhE,MAAAP,EAAA,YAAY,KAAK,QAAQ,SAAS,GAClCA,EAAA,YAAY,KAAK,QAAQ,WACzBA,EAAA,cAAc,KAAK,QAAQ,WAC/BQ,EAAM,IAAI,CAAAG,MAAQX,EAAI,OAAOW,CAAI,CAAC,GAC9BX,EAAA,YAAY,EAAE;AAAA,IAAA,CACnB;AAAA,EAAA;AAAA,EAwCH,iBAAiD;AACxC,WAAAP;AAAA,EAAA;AAEX;"}