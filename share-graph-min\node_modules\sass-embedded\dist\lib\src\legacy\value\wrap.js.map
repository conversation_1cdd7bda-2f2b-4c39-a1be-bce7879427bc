{"version": 3, "file": "wrap.js", "sourceRoot": "", "sources": ["../../../../../lib/src/legacy/value/wrap.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;AA6BvC,oCAqCC;AAQD,kCAKC;AAGD,8BAQC;AAxFD,6BAA6B;AAE7B,iCAAuC;AACvC,mCAAoC;AACpC,iCAAkC;AAClC,+BAAgC;AAChC,qCAAsC;AACtC,qCAAsC;AAEtC,uCAAkC;AAClC,6CAA4C;AAC5C,2CAA0C;AAC1C,yCAAwC;AACxC,+CAA8C;AAC9C,+CAA8C;AAS9C;;;GAGG;AACH,SAAgB,YAAY,CAC1B,OAAyB,EACzB,QAA8B,EAC9B,IAAuB;IAEvB,IAAI,IAAI,EAAE,CAAC;QACT,OAAO,IAAI,CAAC,EAAE,CACZ,gBAAgB,CACb,QAAmC,CAAC,KAAK,CACxC,OAAO,EACP,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CACpB,CACF,CAAC;IACN,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC,EAAE,CACZ,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9B,SAAS,IAAI,CAAC,MAAe;gBAC3B,IAAI,CAAC;oBACH,IAAI,MAAM,YAAY,KAAK,EAAE,CAAC;wBAC5B,MAAM,CAAC,MAAM,CAAC,CAAC;oBACjB,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;oBACpC,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAc,EAAE,CAAC;oBACxB,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC;YACH,CAAC;YAED,uEAAuE;YACvE,MAAM,UAAU,GAAI,QAA4C,CAAC,KAAK,CACpE,OAAO,EACP,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAC/B,CAAC;YAEF,IAAI,UAAU,KAAK,SAAS;gBAAE,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;QACtE,CAAC,CAAiC,CAAC;IACvC,CAAC;AACH,CAAC;AAED,0DAA0D;AAC1D,SAAS,gBAAgB,CAAC,KAAc;IACtC,OAAO,WAAW,CAAC,KAAK,CAAgB,CAAC;AAC3C,CAAC;AAED,sEAAsE;AACtE,SAAgB,WAAW,CAAC,KAAc;IACxC,IAAI,KAAK,YAAY,KAAK;QAAE,MAAM,KAAK,CAAC;IACxC,IAAI,KAAK,YAAY,aAAK;QAAE,OAAO,KAAK,CAAC;IACzC,IAAI,KAAK,YAAY,sBAAe;QAAE,OAAO,KAAK,CAAC,KAAK,CAAC;IACzD,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7E,CAAC;AAED,+CAA+C;AAC/C,SAAgB,SAAS,CAAC,KAA0B;IAClD,IAAI,KAAK,YAAY,iBAAS;QAAE,OAAO,IAAI,mBAAW,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,KAAK,YAAY,eAAQ;QAAE,OAAO,IAAI,iBAAU,CAAC,KAAK,CAAC,CAAC;IAC5D,IAAI,KAAK,YAAY,aAAO;QAAE,OAAO,IAAI,eAAS,CAAC,KAAK,CAAC,CAAC;IAC1D,IAAI,KAAK,YAAY,mBAAU;QAAE,OAAO,IAAI,qBAAY,CAAC,KAAK,CAAC,CAAC;IAChE,IAAI,KAAK,YAAY,mBAAU;QAAE,OAAO,IAAI,qBAAY,CAAC,KAAK,CAAC,CAAC;IAChE,IAAI,KAAK,YAAY,aAAK;QAAE,OAAO,KAAK,CAAC;IACzC,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACtE,CAAC"}