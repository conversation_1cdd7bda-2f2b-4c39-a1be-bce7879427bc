{"version": 3, "file": "number-formatter.es.js", "sources": ["../../src/helpers/number-formatter.ts"], "sourcesContent": ["export type INumberParam = number | undefined | null\r\n\r\nexport class NumberFormatter {\r\n  constructor(public locale: string = 'en-gb'){}\r\n\r\n  private _volumeFormatter: Intl.NumberFormat | undefined;\r\n  get volumeFormatter() {\r\n    if(!this._volumeFormatter) {\r\n      this._volumeFormatter = new Intl.NumberFormat(this.locale, { notation: 'compact', compactDisplay: 'short', maximumFractionDigits: 2})\r\n    }\r\n\r\n    return this._volumeFormatter\r\n  }\r\n\r\n  private _decimalFormatter: Intl.NumberFormat | undefined;\r\n  get decimalFormatter() {\r\n    if(!this._decimalFormatter) {\r\n      this._decimalFormatter = new Intl.NumberFormat(this.locale)\r\n    }\r\n\r\n    return this._decimalFormatter\r\n  }\r\n\r\n  private _percentFormatter: Intl.NumberFormat | undefined;\r\n  get percentFormatter() {\r\n    if(!this._percentFormatter) {\r\n      this._percentFormatter = new Intl.NumberFormat(this.locale, { \r\n        style: \"percent\",\r\n        minimumFractionDigits: 2, // Ensure 2 decimal places\r\n        maximumFractionDigits: 2, // Restrict to 2 decimal places\r\n      })\r\n    }\r\n\r\n    return this._percentFormatter\r\n  }\r\n\r\n  volume(num: INumberParam) {\r\n    if(num == null) return ''\r\n    if(Number.isNaN(num)) return \"NaN\"\r\n    return this.volumeFormatter.format(num)\r\n  }\r\n\r\n  decimal(num: INumberParam) {\r\n    if(num == null) return ''\r\n    if(Number.isNaN(num)) return \"NaN\"\r\n    return this.decimalFormatter.format(num)\r\n  }\r\n\r\n  percent(num: INumberParam) {\r\n    if(num == null) return ''\r\n    if(Number.isNaN(num)) return \"NaN\"\r\n    return this.percentFormatter.format(num)\r\n  }\r\n}\r\n\r\nexport const NumberFormatterFactory = {\r\n  _cache: new Map<string, NumberFormatter>(),\r\n\r\n  formatter(locale: string) {\r\n    if(this._cache.has(locale)) this._cache.get(locale) as NumberFormatter;\r\n\r\n    const instance = new NumberFormatter(locale);\r\n    this._cache.set(locale, instance)\r\n    return instance;\r\n  }\r\n}\r\n\r\n\r\n"], "names": ["NumberFormatter", "locale", "__publicField", "num", "NumberFormatterFactory", "instance"], "mappings": ";;;AAEO,MAAMA,EAAgB;AAAA,EAC3B,YAAmBC,IAAiB,SAAQ;AAEpC,IAAAC,EAAA;AASA,IAAAA,EAAA;AASA,IAAAA,EAAA;AApBW,SAAA,SAAAD;AAAA,EAAA;AAAA,EAGnB,IAAI,kBAAkB;AACjB,WAAC,KAAK,qBACP,KAAK,mBAAmB,IAAI,KAAK,aAAa,KAAK,QAAQ,EAAE,UAAU,WAAW,gBAAgB,SAAS,uBAAuB,GAAE,IAG/H,KAAK;AAAA,EAAA;AAAA,EAId,IAAI,mBAAmB;AAClB,WAAC,KAAK,sBACP,KAAK,oBAAoB,IAAI,KAAK,aAAa,KAAK,MAAM,IAGrD,KAAK;AAAA,EAAA;AAAA,EAId,IAAI,mBAAmB;AAClB,WAAC,KAAK,sBACP,KAAK,oBAAoB,IAAI,KAAK,aAAa,KAAK,QAAQ;AAAA,MAC1D,OAAO;AAAA,MACP,uBAAuB;AAAA;AAAA,MACvB,uBAAuB;AAAA;AAAA,IAAA,CACxB,IAGI,KAAK;AAAA,EAAA;AAAA,EAGd,OAAOE,GAAmB;AACrB,WAAAA,KAAO,OAAa,KACpB,OAAO,MAAMA,CAAG,IAAU,QACtB,KAAK,gBAAgB,OAAOA,CAAG;AAAA,EAAA;AAAA,EAGxC,QAAQA,GAAmB;AACtB,WAAAA,KAAO,OAAa,KACpB,OAAO,MAAMA,CAAG,IAAU,QACtB,KAAK,iBAAiB,OAAOA,CAAG;AAAA,EAAA;AAAA,EAGzC,QAAQA,GAAmB;AACtB,WAAAA,KAAO,OAAa,KACpB,OAAO,MAAMA,CAAG,IAAU,QACtB,KAAK,iBAAiB,OAAOA,CAAG;AAAA,EAAA;AAE3C;AAEO,MAAMC,IAAyB;AAAA,EACpC,4BAAY,IAA6B;AAAA,EAEzC,UAAUH,GAAgB;AACrB,IAAA,KAAK,OAAO,IAAIA,CAAM,KAAQ,KAAA,OAAO,IAAIA,CAAM;AAE5C,UAAAI,IAAW,IAAIL,EAAgBC,CAAM;AACtC,gBAAA,OAAO,IAAIA,GAAQI,CAAQ,GACzBA;AAAA,EAAA;AAEX;"}